"""
Export Frame untuk SPK Karyawan TOPSIS
Fitur export hasil ranking ke format Excel dan CSV
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import os
from datetime import datetime

class ExportFrame:
    def __init__(self, parent, db_manager):
        """Initialize export frame"""
        self.parent = parent
        self.db_manager = db_manager
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        self.create_widgets()
        self.load_export_data()
    
    def create_widgets(self):
        """Create export widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="Export Data Hasil TOPSIS", 
                 style='Title.TLabel').pack(side='left')
        
        ttk.Button(title_frame, text="🔄 Refresh Data", 
                  command=self.load_export_data, style='Nav.TButton').pack(side='right')
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=1)
        main_container.grid_columnconfigure(1, weight=1)
        
        # Left side - Export options
        self.create_export_options(main_container)
        
        # Right side - Preview
        self.create_preview_panel(main_container)
    
    def create_export_options(self, parent):
        """Create export options panel"""
        options_frame = ttk.LabelFrame(parent, text="Opsi Export", padding=20)
        options_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))
        
        # Export format selection
        format_frame = ttk.LabelFrame(options_frame, text="Format Export", padding=10)
        format_frame.pack(fill='x', pady=(0, 15))
        
        self.export_format = tk.StringVar(value="excel")
        
        ttk.Radiobutton(format_frame, text="📊 Excel (.xlsx)", 
                       variable=self.export_format, value="excel").pack(anchor='w', pady=2)
        
        ttk.Radiobutton(format_frame, text="📄 CSV (.csv)", 
                       variable=self.export_format, value="csv").pack(anchor='w', pady=2)
        
        # Data selection
        data_frame = ttk.LabelFrame(options_frame, text="Data yang Diekspor", padding=10)
        data_frame.pack(fill='x', pady=(0, 15))
        
        self.include_ranking = tk.BooleanVar(value=True)
        self.include_criteria = tk.BooleanVar(value=True)
        self.include_details = tk.BooleanVar(value=False)
        self.include_history = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(data_frame, text="🏆 Hasil Ranking TOPSIS", 
                       variable=self.include_ranking).pack(anchor='w', pady=2)
        
        ttk.Checkbutton(data_frame, text="⚖️ Data Kriteria & Bobot", 
                       variable=self.include_criteria).pack(anchor='w', pady=2)
        
        ttk.Checkbutton(data_frame, text="📊 Detail Perhitungan", 
                       variable=self.include_details).pack(anchor='w', pady=2)
        
        ttk.Checkbutton(data_frame, text="📝 Riwayat Perhitungan", 
                       variable=self.include_history).pack(anchor='w', pady=2)
        
        # File options
        file_frame = ttk.LabelFrame(options_frame, text="Pengaturan File", padding=10)
        file_frame.pack(fill='x', pady=(0, 15))
        
        # Filename
        ttk.Label(file_frame, text="Nama File:", style='Header.TLabel').pack(anchor='w')
        
        self.filename_var = tk.StringVar(value=f"TOPSIS_Results_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        filename_entry = ttk.Entry(file_frame, textvariable=self.filename_var, width=40)
        filename_entry.pack(fill='x', pady=5)
        
        # Directory selection
        ttk.Label(file_frame, text="Lokasi Penyimpanan:", style='Header.TLabel').pack(anchor='w', pady=(10, 0))
        
        dir_frame = ttk.Frame(file_frame)
        dir_frame.pack(fill='x', pady=5)
        
        self.directory_var = tk.StringVar(value=os.path.expanduser("~/Desktop"))
        dir_entry = ttk.Entry(dir_frame, textvariable=self.directory_var, width=30)
        dir_entry.pack(side='left', fill='x', expand=True)
        
        ttk.Button(dir_frame, text="📁 Browse", 
                  command=self.browse_directory, style='Nav.TButton').pack(side='right', padx=(5, 0))
        
        # Export buttons
        button_frame = ttk.Frame(options_frame)
        button_frame.pack(fill='x', pady=20)
        
        ttk.Button(button_frame, text="📤 Export Sekarang", 
                  command=self.export_data, style='Success.TButton').pack(fill='x', pady=5)
        
        ttk.Button(button_frame, text="👁️ Preview Data", 
                  command=self.preview_data, style='Action.TButton').pack(fill='x', pady=5)
        
        # Quick export buttons
        quick_frame = ttk.LabelFrame(options_frame, text="Export Cepat", padding=10)
        quick_frame.pack(fill='x')
        
        ttk.Button(quick_frame, text="🏆 Export Ranking Only", 
                  command=self.quick_export_ranking, style='Nav.TButton').pack(fill='x', pady=2)
        
        ttk.Button(quick_frame, text="📊 Export Lengkap", 
                  command=self.quick_export_complete, style='Nav.TButton').pack(fill='x', pady=2)
    
    def create_preview_panel(self, parent):
        """Create data preview panel"""
        preview_frame = ttk.LabelFrame(parent, text="Preview Data", padding=10)
        preview_frame.grid(row=0, column=1, sticky='nsew', padx=(10, 0))
        
        # Configure grid
        preview_frame.grid_rowconfigure(0, weight=1)
        preview_frame.grid_columnconfigure(0, weight=1)
        
        # Notebook for different data types
        self.preview_notebook = ttk.Notebook(preview_frame)
        self.preview_notebook.grid(row=0, column=0, sticky='nsew')
        
        # Ranking tab
        self.create_ranking_preview()
        
        # Criteria tab
        self.create_criteria_preview()
        
        # Statistics tab
        self.create_statistics_preview()
        
        # Status
        self.preview_status = tk.StringVar()
        status_label = ttk.Label(preview_frame, textvariable=self.preview_status, 
                               style='Info.TLabel')
        status_label.grid(row=1, column=0, pady=5)
    
    def create_ranking_preview(self):
        """Create ranking preview tab"""
        ranking_frame = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(ranking_frame, text="🏆 Ranking")
        
        # Configure grid
        ranking_frame.grid_rowconfigure(0, weight=1)
        ranking_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview for ranking
        columns = ('Rank', 'Nama', 'NIP', 'Posisi', 'Score', 'Status')
        self.ranking_tree = ttk.Treeview(ranking_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        for col in columns:
            self.ranking_tree.heading(col, text=col)
            self.ranking_tree.column(col, width=100, minwidth=50)
        
        # Scrollbar
        ranking_scrollbar = ttk.Scrollbar(ranking_frame, orient='vertical', command=self.ranking_tree.yview)
        self.ranking_tree.configure(yscrollcommand=ranking_scrollbar.set)
        
        # Grid layout
        self.ranking_tree.grid(row=0, column=0, sticky='nsew')
        ranking_scrollbar.grid(row=0, column=1, sticky='ns')
    
    def create_criteria_preview(self):
        """Create criteria preview tab"""
        criteria_frame = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(criteria_frame, text="⚖️ Kriteria")
        
        # Configure grid
        criteria_frame.grid_rowconfigure(0, weight=1)
        criteria_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview for criteria
        columns = ('Kode', 'Nama', 'Bobot', 'Persentase', 'Jenis')
        self.criteria_tree = ttk.Treeview(criteria_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        for col in columns:
            self.criteria_tree.heading(col, text=col)
            self.criteria_tree.column(col, width=120, minwidth=80)
        
        # Scrollbar
        criteria_scrollbar = ttk.Scrollbar(criteria_frame, orient='vertical', command=self.criteria_tree.yview)
        self.criteria_tree.configure(yscrollcommand=criteria_scrollbar.set)
        
        # Grid layout
        self.criteria_tree.grid(row=0, column=0, sticky='nsew')
        criteria_scrollbar.grid(row=0, column=1, sticky='ns')
    
    def create_statistics_preview(self):
        """Create statistics preview tab"""
        stats_frame = ttk.Frame(self.preview_notebook)
        self.preview_notebook.add(stats_frame, text="📊 Statistik")
        
        # Configure grid
        stats_frame.grid_rowconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(0, weight=1)
        
        # Text widget for statistics
        self.stats_preview = tk.Text(stats_frame, wrap='word', font=('Consolas', 10), state='disabled')
        
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient='vertical', command=self.stats_preview.yview)
        self.stats_preview.configure(yscrollcommand=stats_scrollbar.set)
        
        # Grid layout
        self.stats_preview.grid(row=0, column=0, sticky='nsew')
        stats_scrollbar.grid(row=0, column=1, sticky='ns')
    
    def load_export_data(self):
        """Load data for export preview"""
        try:
            # Load ranking data
            self.load_ranking_data()
            
            # Load criteria data
            self.load_criteria_data()
            
            # Load statistics
            self.load_statistics_data()
            
            self.preview_status.set("Data berhasil dimuat")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat data: {str(e)}")
            self.preview_status.set("Error memuat data")
    
    def load_ranking_data(self):
        """Load ranking data into preview"""
        # Clear existing items
        for item in self.ranking_tree.get_children():
            self.ranking_tree.delete(item)
        
        # Load results
        results = self.db_manager.get_topsis_results()
        
        for result in results:
            status = result.get('status', '').replace('🟢 ', '').replace('🔵 ', '').replace('🟡 ', '').replace('🔴 ', '')
            
            self.ranking_tree.insert('', 'end', values=(
                result.get('ranking', ''),
                result.get('nama', ''),
                result.get('nip', ''),
                result.get('posisi', ''),
                f"{result.get('score', 0):.4f}",
                status
            ))
    
    def load_criteria_data(self):
        """Load criteria data into preview"""
        # Clear existing items
        for item in self.criteria_tree.get_children():
            self.criteria_tree.delete(item)
        
        # Load criteria
        criteria = self.db_manager.get_all_criteria()
        
        for criterion in criteria:
            bobot = criterion.get('bobot', 0)
            persentase = bobot * 100
            
            self.criteria_tree.insert('', 'end', values=(
                criterion.get('kode', ''),
                criterion.get('nama', ''),
                f"{bobot:.3f}",
                f"{persentase:.1f}%",
                criterion.get('jenis', '').title()
            ))
    
    def load_statistics_data(self):
        """Load statistics data into preview"""
        try:
            self.stats_preview.configure(state='normal')
            self.stats_preview.delete(1.0, tk.END)
            
            # Get data
            results = self.db_manager.get_topsis_results()
            employees = self.db_manager.get_all_employees()
            criteria = self.db_manager.get_all_criteria()
            history = self.db_manager.get_calculation_history()
            
            stats_text = f"""STATISTIK EXPORT DATA TOPSIS
{'='*40}

RINGKASAN DATA:
• Total Karyawan: {len(employees)}
• Hasil TOPSIS: {len(results)}
• Kriteria: {len(criteria)}
• Riwayat Perhitungan: {len(history)}

"""
            
            if results:
                scores = [r.get('score', 0) for r in results]
                stats_text += f"""STATISTIK SKOR:
• Skor Tertinggi: {max(scores):.4f}
• Skor Terendah: {min(scores):.4f}
• Rata-rata: {sum(scores)/len(scores):.4f}

DISTRIBUSI STATUS:
"""
                
                # Count status distribution
                status_counts = {}
                for result in results:
                    status = result.get('status', 'Unknown')
                    status_counts[status] = status_counts.get(status, 0) + 1
                
                for status, count in status_counts.items():
                    percentage = (count / len(results)) * 100
                    stats_text += f"• {status}: {count} ({percentage:.1f}%)\n"
            
            stats_text += f"""
INFORMASI EXPORT:
• Tanggal: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• Format: {self.export_format.get().upper()}
• File: {self.filename_var.get()}
• Lokasi: {self.directory_var.get()}
"""
            
            self.stats_preview.insert(tk.END, stats_text)
            self.stats_preview.configure(state='disabled')
            
        except Exception as e:
            print(f"Error loading statistics: {e}")
    
    def browse_directory(self):
        """Browse for export directory"""
        directory = filedialog.askdirectory(initialdir=self.directory_var.get())
        if directory:
            self.directory_var.set(directory)
    
    def preview_data(self):
        """Preview data before export"""
        self.load_export_data()
        messagebox.showinfo("Preview", "Data telah diperbarui di panel preview")
    
    def export_data(self):
        """Export data based on selected options"""
        try:
            # Validate selections
            if not any([self.include_ranking.get(), self.include_criteria.get(), 
                       self.include_details.get(), self.include_history.get()]):
                messagebox.showwarning("Peringatan", "Pilih minimal satu jenis data untuk diekspor!")
                return
            
            # Prepare filename
            filename = self.filename_var.get().strip()
            if not filename:
                filename = f"TOPSIS_Export_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Add extension
            if self.export_format.get() == "excel":
                if not filename.endswith('.xlsx'):
                    filename += '.xlsx'
            else:
                if not filename.endswith('.csv'):
                    filename += '.csv'
            
            # Full path
            directory = self.directory_var.get()
            if not os.path.exists(directory):
                messagebox.showerror("Error", "Direktori tidak ditemukan!")
                return
            
            filepath = os.path.join(directory, filename)
            
            # Export based on format
            if self.export_format.get() == "excel":
                self.export_to_excel(filepath)
            else:
                self.export_to_csv(filepath)
            
            messagebox.showinfo("Sukses", f"Data berhasil diekspor ke:\n{filepath}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal mengekspor data: {str(e)}")
    
    def export_to_excel(self, filepath):
        """Export data to Excel format"""
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:

            # Export ranking data
            if self.include_ranking.get():
                results = self.db_manager.get_topsis_results()
                if results:
                    df_ranking = pd.DataFrame(results)
                    # Clean status column
                    if 'status' in df_ranking.columns:
                        df_ranking['status'] = df_ranking['status'].str.replace('🟢 |🔵 |🟡 |🔴 ', '', regex=True)
                    df_ranking.to_excel(writer, sheet_name='Ranking_TOPSIS', index=False)
            
            # Export criteria data
            if self.include_criteria.get():
                criteria = self.db_manager.get_all_criteria()
                if criteria:
                    df_criteria = pd.DataFrame(criteria)
                    df_criteria['persentase'] = df_criteria['bobot'] * 100
                    df_criteria.to_excel(writer, sheet_name='Kriteria_Bobot', index=False)
            
            # Export employee details
            if self.include_details.get():
                employees = self.db_manager.get_all_employees()
                if employees:
                    df_employees = pd.DataFrame(employees)
                    df_employees.to_excel(writer, sheet_name='Detail_Karyawan', index=False)
            
            # Export calculation history
            if self.include_history.get():
                history = self.db_manager.get_calculation_history()
                if history:
                    df_history = pd.DataFrame(history)
                    df_history.to_excel(writer, sheet_name='Riwayat_Perhitungan', index=False)
    
    def export_to_csv(self, filepath):
        """Export data to CSV format"""
        # For CSV, we'll combine all selected data into one file
        combined_data = []
        
        # Add ranking data
        if self.include_ranking.get():
            results = self.db_manager.get_topsis_results()
            if results:
                df_ranking = pd.DataFrame(results)
                if 'status' in df_ranking.columns:
                    df_ranking['status'] = df_ranking['status'].str.replace('🟢 |🔵 |🟡 |🔴 ', '', regex=True)
                combined_data.append(("HASIL RANKING TOPSIS", df_ranking))
        
        # Add criteria data
        if self.include_criteria.get():
            criteria = self.db_manager.get_all_criteria()
            if criteria:
                df_criteria = pd.DataFrame(criteria)
                df_criteria['persentase'] = df_criteria['bobot'] * 100
                combined_data.append(("KRITERIA DAN BOBOT", df_criteria))
        
        # Write to CSV
        if combined_data:
            with open(filepath, 'w', encoding='utf-8-sig', newline='') as f:
                for i, (title, df) in enumerate(combined_data):
                    if i > 0:
                        f.write('\n\n')
                    f.write(f"{title}\n")
                    f.write('=' * len(title) + '\n')
                    df.to_csv(f, index=False, lineterminator='\n')
    
    def quick_export_ranking(self):
        """Quick export ranking only"""
        self.include_ranking.set(True)
        self.include_criteria.set(False)
        self.include_details.set(False)
        self.include_history.set(False)
        self.export_data()
    
    def quick_export_complete(self):
        """Quick export all data"""
        self.include_ranking.set(True)
        self.include_criteria.set(True)
        self.include_details.set(True)
        self.include_history.set(True)
        self.export_data()
