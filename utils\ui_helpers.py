"""
UI Helper Functions
Enhanced UI utilities for better user experience
Author: <PERSON> Wibowo
NIM: 211011450583
Kelas: 06TPLP003
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
from typing import Callable, Any, Optional

class LoadingDialog:
    """Loading dialog with progress indication"""
    
    def __init__(self, parent, title="Loading...", message="Please wait..."):
        self.parent = parent
        self.title = title
        self.message = message
        self.dialog = None
        self.progress_var = None
        self.is_cancelled = False
        
    def show(self):
        """Show loading dialog"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("400x150")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Create content
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill="both", expand=True)
        
        # Message
        ttk.Label(main_frame, text=self.message, 
                 font=('Segoe UI', 11)).pack(pady=(0, 15))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        progress_bar.pack(fill="x", pady=(0, 15))
        progress_bar.start(10)
        
        # Cancel button
        ttk.Button(main_frame, text="Cancel", 
                  command=self.cancel).pack()
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.cancel)
        
    def center_dialog(self):
        """Center dialog on parent"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - 200
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - 75
        self.dialog.geometry(f"400x150+{x}+{y}")
        
    def cancel(self):
        """Cancel operation"""
        self.is_cancelled = True
        self.close()
        
    def close(self):
        """Close dialog"""
        if self.dialog:
            self.dialog.destroy()
            self.dialog = None

class ProgressDialog:
    """Progress dialog with percentage indication"""
    
    def __init__(self, parent, title="Processing...", message="Processing..."):
        self.parent = parent
        self.title = title
        self.message = message
        self.dialog = None
        self.progress_var = None
        self.status_var = None
        
    def show(self):
        """Show progress dialog"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("450x180")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Create content
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill="both", expand=True)
        
        # Message
        ttk.Label(main_frame, text=self.message, 
                 font=('Segoe UI', 11)).pack(pady=(0, 15))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                     maximum=100)
        progress_bar.pack(fill="x", pady=(0, 10))
        
        # Status label
        self.status_var = tk.StringVar(value="Initializing...")
        ttk.Label(main_frame, textvariable=self.status_var,
                 font=('Segoe UI', 9)).pack(pady=(0, 15))
        
    def center_dialog(self):
        """Center dialog on parent"""
        self.dialog.update_idletasks()
        x = self.parent.winfo_x() + (self.parent.winfo_width() // 2) - 225
        y = self.parent.winfo_y() + (self.parent.winfo_height() // 2) - 90
        self.dialog.geometry(f"450x180+{x}+{y}")
        
    def update_progress(self, percentage: float, status: str = ""):
        """Update progress"""
        if self.progress_var:
            self.progress_var.set(percentage)
        if self.status_var and status:
            self.status_var.set(status)
        if self.dialog:
            self.dialog.update()
            
    def close(self):
        """Close dialog"""
        if self.dialog:
            self.dialog.destroy()
            self.dialog = None

def run_with_loading(parent, func: Callable, args: tuple = (), 
                    title: str = "Loading...", message: str = "Please wait..."):
    """Run function with loading dialog"""
    loading = LoadingDialog(parent, title, message)
    result = None
    error = None
    
    def worker():
        nonlocal result, error
        try:
            result = func(*args)
        except Exception as e:
            error = e
        finally:
            # Close loading dialog in main thread
            parent.after(0, loading.close)
    
    # Show loading dialog
    loading.show()
    
    # Start worker thread
    thread = threading.Thread(target=worker, daemon=True)
    thread.start()
    
    # Wait for completion
    while thread.is_alive() and not loading.is_cancelled:
        parent.update()
        time.sleep(0.1)
    
    if loading.is_cancelled:
        return None
    
    if error:
        raise error
        
    return result

def show_success_toast(parent, message: str, duration: int = 3000):
    """Show success toast notification"""
    toast = tk.Toplevel(parent)
    toast.title("")
    toast.geometry("300x80")
    toast.resizable(False, False)
    toast.overrideredirect(True)
    toast.configure(bg='#2ecc71')
    
    # Position at top-right
    x = parent.winfo_x() + parent.winfo_width() - 320
    y = parent.winfo_y() + 20
    toast.geometry(f"300x80+{x}+{y}")
    
    # Content
    frame = tk.Frame(toast, bg='#2ecc71')
    frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    tk.Label(frame, text="✅ Success", font=('Segoe UI', 10, 'bold'),
             bg='#2ecc71', fg='white').pack()
    tk.Label(frame, text=message, font=('Segoe UI', 9),
             bg='#2ecc71', fg='white', wraplength=280).pack()
    
    # Auto close
    parent.after(duration, toast.destroy)

def show_error_toast(parent, message: str, duration: int = 5000):
    """Show error toast notification"""
    toast = tk.Toplevel(parent)
    toast.title("")
    toast.geometry("300x80")
    toast.resizable(False, False)
    toast.overrideredirect(True)
    toast.configure(bg='#e74c3c')
    
    # Position at top-right
    x = parent.winfo_x() + parent.winfo_width() - 320
    y = parent.winfo_y() + 20
    toast.geometry(f"300x80+{x}+{y}")
    
    # Content
    frame = tk.Frame(toast, bg='#e74c3c')
    frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    tk.Label(frame, text="❌ Error", font=('Segoe UI', 10, 'bold'),
             bg='#e74c3c', fg='white').pack()
    tk.Label(frame, text=message, font=('Segoe UI', 9),
             bg='#e74c3c', fg='white', wraplength=280).pack()
    
    # Auto close
    parent.after(duration, toast.destroy)

def show_warning_toast(parent, message: str, duration: int = 4000):
    """Show warning toast notification"""
    toast = tk.Toplevel(parent)
    toast.title("")
    toast.geometry("300x80")
    toast.resizable(False, False)
    toast.overrideredirect(True)
    toast.configure(bg='#f39c12')
    
    # Position at top-right
    x = parent.winfo_x() + parent.winfo_width() - 320
    y = parent.winfo_y() + 20
    toast.geometry(f"300x80+{x}+{y}")
    
    # Content
    frame = tk.Frame(toast, bg='#f39c12')
    frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    tk.Label(frame, text="⚠️ Warning", font=('Segoe UI', 10, 'bold'),
             bg='#f39c12', fg='white').pack()
    tk.Label(frame, text=message, font=('Segoe UI', 9),
             bg='#f39c12', fg='white', wraplength=280).pack()
    
    # Auto close
    parent.after(duration, toast.destroy)

def create_tooltip(widget, text: str):
    """Create tooltip for widget"""
    def on_enter(event):
        tooltip = tk.Toplevel()
        tooltip.wm_overrideredirect(True)
        tooltip.configure(bg='#333333')
        
        label = tk.Label(tooltip, text=text, font=('Segoe UI', 9),
                        bg='#333333', fg='white', padx=5, pady=3)
        label.pack()
        
        x = widget.winfo_rootx() + 20
        y = widget.winfo_rooty() + widget.winfo_height() + 5
        tooltip.geometry(f"+{x}+{y}")
        
        widget.tooltip = tooltip
        
    def on_leave(event):
        if hasattr(widget, 'tooltip'):
            widget.tooltip.destroy()
            del widget.tooltip
    
    widget.bind("<Enter>", on_enter)
    widget.bind("<Leave>", on_leave)

def animate_button_click(button):
    """Animate button click"""
    original_relief = button.cget('relief')
    button.configure(relief='sunken')
    button.after(100, lambda: button.configure(relief=original_relief))

def validate_and_highlight(entry_widget, validation_func, error_message: str = "Invalid input"):
    """Validate entry and highlight if invalid"""
    value = entry_widget.get()
    
    try:
        if validation_func(value):
            entry_widget.configure(style='Valid.TEntry')
            return True
        else:
            entry_widget.configure(style='Invalid.TEntry')
            return False
    except Exception:
        entry_widget.configure(style='Invalid.TEntry')
        return False
