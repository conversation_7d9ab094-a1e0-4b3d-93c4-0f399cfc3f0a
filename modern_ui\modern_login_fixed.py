"""
Modern Login Window with Fixed Window Controls
Enhanced SPK TOPSIS Application - Login Module
Author: <PERSON> Wibowo
NIM: 211011450583
Kelas: 06TPLP003
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import customtkinter as ctk
    CTK_AVAILABLE = True
except ImportError:
    CTK_AVAILABLE = False

try:
    from database.db_manager import DatabaseManager
except ImportError:
    # Try alternative import path
    try:
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'database'))
        from db_manager import DatabaseManager
    except ImportError:
        # Create a simple fallback database manager
        class DatabaseManager:
            def authenticate_user(self, username, password):
                # Simple hardcoded authentication for demo
                if username == "admin" and password == "admin123":
                    return {
                        'id': 1,
                        'username': 'admin',
                        'full_name': 'Administrator',
                        'role': 'admin'
                    }
                elif username == "operator" and password == "operator123":
                    return {
                        'id': 2,
                        'username': 'operator',
                        'full_name': 'Data Operator',
                        'role': 'operator'
                    }
                return None

class ModernLoginWindow:
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.user_data = None
        self.login_successful = False
        
        if CTK_AVAILABLE:
            self.setup_hybrid_ui()
        else:
            self.setup_tk_ui()
    
    def setup_hybrid_ui(self):
        """Setup hybrid UI with Tkinter window and CustomTkinter content"""
        # Set CustomTkinter appearance
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # Create Tkinter window for proper window controls
        self.root = tk.Tk()
        self.root.title("🏢 SPK Karyawan TOPSIS - Login")
        self.root.geometry("700x900")
        self.root.resizable(True, True)
        self.root.minsize(600, 700)
        self.root.configure(bg='#212121')
        
        # Proper window controls
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.state('normal')
        
        # Center window
        self.center_window()
        
        # Create CustomTkinter frame inside Tkinter window
        self.main_ctk_frame = ctk.CTkFrame(self.root, corner_radius=20)
        self.main_ctk_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Create scrollable content
        self.scrollable_frame = ctk.CTkScrollableFrame(self.main_ctk_frame, corner_radius=15)
        self.scrollable_frame.pack(fill="both", expand=True, padx=15, pady=15)
        
        # Create content
        self.create_ctk_header()
        self.create_ctk_form()
        self.create_ctk_footer()
        
        # Bind Enter key
        self.root.bind('<Return>', lambda e: self.handle_login())
        
        # Focus
        self.root.focus_force()
    
    def setup_tk_ui(self):
        """Setup standard Tkinter UI as fallback"""
        self.root = tk.Tk()
        self.root.title("🏢 SPK Karyawan TOPSIS - Login")
        self.root.geometry("700x900")
        self.root.resizable(True, True)
        self.root.minsize(600, 700)
        self.root.configure(bg='#f0f0f0')
        
        # Center window
        self.center_window()
        
        # Create main frame
        self.main_frame = tk.Frame(self.root, bg='white', relief='solid', bd=1)
        self.main_frame.pack(fill="both", expand=True, padx=30, pady=30)
        
        # Create content
        self.create_tk_header()
        self.create_tk_form()
        self.create_tk_footer()
        
        # Bind Enter key
        self.root.bind('<Return>', lambda e: self.handle_login())
    
    def center_window(self):
        """Center window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def on_closing(self):
        """Handle window closing event"""
        self.login_successful = False
        self.user_data = None
        self.root.destroy()
    
    def create_ctk_header(self):
        """Create CustomTkinter header"""
        # Header frame
        header_frame = ctk.CTkFrame(self.scrollable_frame, height=140, corner_radius=15)
        header_frame.pack(fill="x", padx=10, pady=(10, 5))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = ctk.CTkLabel(header_frame, 
                                  text="🏆 SPK TOPSIS",
                                  font=ctk.CTkFont(size=28, weight="bold"))
        title_label.pack(pady=(25, 3))
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(header_frame,
                                     text="Sistem Pendukung Keputusan Enhanced v2.0",
                                     font=ctk.CTkFont(size=13))
        subtitle_label.pack(pady=1)
        
        # Description
        desc_label = ctk.CTkLabel(header_frame,
                                 text="Pengangkatan Karyawan Tetap - Metode TOPSIS",
                                 font=ctk.CTkFont(size=11))
        desc_label.pack(pady=1)
    
    def create_ctk_form(self):
        """Create CustomTkinter form"""
        # Form frame
        form_frame = ctk.CTkFrame(self.scrollable_frame, corner_radius=15)
        form_frame.pack(fill="x", padx=10, pady=5)
        
        # Login title
        login_title = ctk.CTkLabel(form_frame,
                                  text="🔐 Login to Continue",
                                  font=ctk.CTkFont(size=18, weight="bold"))
        login_title.pack(pady=(25, 15))
        
        # Username section
        username_label = ctk.CTkLabel(form_frame,
                                     text="👤 Username",
                                     font=ctk.CTkFont(size=13, weight="bold"))
        username_label.pack(anchor="w", padx=30, pady=(8, 3))
        
        self.username_entry = ctk.CTkEntry(form_frame,
                                          placeholder_text="Enter your username",
                                          height=40,
                                          font=ctk.CTkFont(size=13))
        self.username_entry.pack(fill="x", padx=30, pady=(0, 12))
        
        # Password section
        password_label = ctk.CTkLabel(form_frame,
                                     text="🔒 Password",
                                     font=ctk.CTkFont(size=13, weight="bold"))
        password_label.pack(anchor="w", padx=30, pady=(0, 3))
        
        self.password_entry = ctk.CTkEntry(form_frame,
                                          placeholder_text="Enter your password",
                                          show="*",
                                          height=40,
                                          font=ctk.CTkFont(size=13))
        self.password_entry.pack(fill="x", padx=30, pady=(0, 12))
        
        # Show password checkbox
        self.show_password_var = ctk.BooleanVar()
        self.show_password_cb = ctk.CTkCheckBox(form_frame,
                                               text="👁️ Show Password",
                                               variable=self.show_password_var,
                                               command=self.toggle_password_visibility,
                                               font=ctk.CTkFont(size=11))
        self.show_password_cb.pack(anchor="w", padx=30, pady=(0, 15))
        
        # Login button
        self.login_btn = ctk.CTkButton(form_frame,
                                      text="🚀 Login",
                                      height=45,
                                      font=ctk.CTkFont(size=15, weight="bold"),
                                      command=self.handle_login)
        self.login_btn.pack(fill="x", padx=30, pady=(0, 25))
        
        # Set focus
        self.username_entry.focus()
    
    def create_ctk_footer(self):
        """Create CustomTkinter footer"""
        # Footer frame
        footer_frame = ctk.CTkFrame(self.scrollable_frame, corner_radius=15)
        footer_frame.pack(fill="x", padx=10, pady=(5, 10))
        
        # Credentials title
        creds_title = ctk.CTkLabel(footer_frame,
                                  text="💡 Demo Credentials:",
                                  font=ctk.CTkFont(size=13, weight="bold"))
        creds_title.pack(pady=(15, 8))
        
        # Admin credentials
        admin_frame = ctk.CTkFrame(footer_frame, corner_radius=8)
        admin_frame.pack(fill="x", padx=20, pady=4)
        
        admin_title = ctk.CTkLabel(admin_frame,
                                  text="👑 Administrator (Full Access)",
                                  font=ctk.CTkFont(size=11, weight="bold"))
        admin_title.pack(pady=(8, 2))
        
        admin_creds = ctk.CTkLabel(admin_frame,
                                  text="Username: admin | Password: admin123",
                                  font=ctk.CTkFont(family="Consolas", size=10))
        admin_creds.pack(pady=(0, 8))
        
        # Operator credentials
        operator_frame = ctk.CTkFrame(footer_frame, corner_radius=8)
        operator_frame.pack(fill="x", padx=20, pady=4)
        
        operator_title = ctk.CTkLabel(operator_frame,
                                     text="⚙️ Data Operator (Input Only)",
                                     font=ctk.CTkFont(size=11, weight="bold"))
        operator_title.pack(pady=(8, 2))
        
        operator_creds = ctk.CTkLabel(operator_frame,
                                     text="Username: operator | Password: operator123",
                                     font=ctk.CTkFont(family="Consolas", size=10))
        operator_creds.pack(pady=(0, 8))
        
        # Author info
        author_label = ctk.CTkLabel(footer_frame,
                                   text="© 2024 Muhammad Bayu Prasetyo Wibowo - 211011450583 - 06TPLP003",
                                   font=ctk.CTkFont(size=9))
        author_label.pack(pady=(8, 12))

    def create_tk_header(self):
        """Create standard Tkinter header"""
        header_frame = tk.Frame(self.main_frame, bg='#3b82f6', height=140)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame,
                              text="🏆 SPK TOPSIS",
                              font=('Segoe UI', 20, 'bold'),
                              bg='#3b82f6', fg='white')
        title_label.pack(pady=(25, 3))

        subtitle_label = tk.Label(header_frame,
                                 text="Sistem Pendukung Keputusan Enhanced v2.0",
                                 font=('Segoe UI', 11),
                                 bg='#3b82f6', fg='#dbeafe')
        subtitle_label.pack(pady=1)

    def create_tk_form(self):
        """Create standard Tkinter form"""
        form_frame = tk.Frame(self.main_frame, bg='white')
        form_frame.pack(fill="both", expand=True, padx=20, pady=10)

        login_title = tk.Label(form_frame,
                              text="🔐 Login to Continue",
                              font=('Segoe UI', 14, 'bold'),
                              bg='white', fg='#1f2937')
        login_title.pack(pady=(25, 15))

        # Username
        username_label = tk.Label(form_frame,
                                 text="👤 Username",
                                 font=('Segoe UI', 11, 'bold'),
                                 bg='white', fg='#374151')
        username_label.pack(anchor="w", padx=30, pady=(8, 3))

        self.username_entry = tk.Entry(form_frame,
                                      font=('Segoe UI', 11),
                                      relief='solid', bd=1)
        self.username_entry.pack(fill="x", padx=30, pady=(0, 12), ipady=6)

        # Password
        password_label = tk.Label(form_frame,
                                 text="🔒 Password",
                                 font=('Segoe UI', 11, 'bold'),
                                 bg='white', fg='#374151')
        password_label.pack(anchor="w", padx=30, pady=(0, 3))

        self.password_entry = tk.Entry(form_frame,
                                      show="*",
                                      font=('Segoe UI', 11),
                                      relief='solid', bd=1)
        self.password_entry.pack(fill="x", padx=30, pady=(0, 12), ipady=6)

        # Login button
        self.login_btn = tk.Button(form_frame,
                                  text="🚀 Login",
                                  font=('Segoe UI', 12, 'bold'),
                                  bg='#3b82f6', fg='white',
                                  relief='flat', bd=0,
                                  command=self.handle_login)
        self.login_btn.pack(fill="x", padx=30, pady=(15, 25), ipady=10)

        self.username_entry.focus()

    def create_tk_footer(self):
        """Create standard Tkinter footer"""
        footer_frame = tk.Frame(self.main_frame, bg='#f8fafc', height=120)
        footer_frame.pack(fill="x", padx=20, pady=(10, 20))
        footer_frame.pack_propagate(False)

        creds_title = tk.Label(footer_frame,
                              text="💡 Demo Credentials:",
                              font=('Segoe UI', 11, 'bold'),
                              bg='#f8fafc', fg='#374151')
        creds_title.pack(pady=(15, 8))

        admin_text = tk.Label(footer_frame,
                             text="👑 Admin: admin / admin123",
                             font=('Consolas', 9),
                             bg='#f8fafc', fg='#6b7280')
        admin_text.pack(pady=2)

        operator_text = tk.Label(footer_frame,
                                text="⚙️ Operator: operator / operator123",
                                font=('Consolas', 9),
                                bg='#f8fafc', fg='#6b7280')
        operator_text.pack(pady=2)

    def toggle_password_visibility(self):
        """Toggle password visibility"""
        if CTK_AVAILABLE:
            if self.show_password_var.get():
                self.password_entry.configure(show="")
            else:
                self.password_entry.configure(show="*")

    def handle_login(self):
        """Handle login attempt"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username or not password:
            self.show_error("❌ Login Failed", "Please enter both username and password!")
            return

        # Authenticate user
        user_data = self.db_manager.authenticate_user(username, password)

        if user_data:
            self.user_data = user_data
            self.login_successful = True

            # Show success message
            role_icon = "👑" if user_data['role'] == 'admin' else "⚙️"
            role_name = "Administrator" if user_data['role'] == 'admin' else "Data Operator"

            self.show_success("✅ Login Successful!",
                            f"Welcome, {user_data['full_name']}!\n\n"
                            f"{role_icon} Role: {role_name}\n"
                            f"🚀 Starting SPK Application...")

            self.root.destroy()
        else:
            self.show_error("❌ Login Failed",
                          "Invalid username or password!\n\n"
                          "💡 Try these credentials:\n\n"
                          "👑 Admin: admin / admin123\n"
                          "⚙️ Operator: operator / operator123")

    def show_success(self, title, message):
        """Show success message"""
        messagebox.showinfo(title, message)

    def show_error(self, title, message):
        """Show error message"""
        messagebox.showerror(title, message)

    def run(self):
        """Run the login window"""
        self.root.mainloop()
        return self.user_data if self.login_successful else None

def show_modern_login():
    """Show modern login window and return user data if successful"""
    login_window = ModernLoginWindow()
    return login_window.run()

if __name__ == "__main__":
    user_data = show_modern_login()
    if user_data:
        print(f"Login successful: {user_data}")
    else:
        print("Login cancelled or failed")
