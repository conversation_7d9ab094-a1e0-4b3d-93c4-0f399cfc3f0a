#!/usr/bin/env python3
"""
Script untuk menghapus alternatif berle<PERSON>han dan hanya menyi<PERSON>, Jaya, Bunga
SPK TOPSIS Enhanced v2.0
"""

import sqlite3
import os

def cleanup_alternatives():
    """Hapus alternatif be<PERSON>, si<PERSON><PERSON> ha<PERSON>, Jaya, Bunga"""
    
    db_path = 'SPK_TOPSIS_Enhanced_v2.0_Portable/spk_karyawan_enhanced.db'
    
    if not os.path.exists(db_path):
        print("❌ Database tidak ditemukan!")
        return False
    
    try:
        # Backup database
        backup_path = 'SPK_TOPSIS_Enhanced_v2.0_Portable/spk_karyawan_enhanced_backup_before_cleanup.db'
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"📁 Backup database dibuat: {backup_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Memeriksa data sebelum cleanup...")
        
        # Lihat alternatif yang ada
        cursor.execute("SELECT id, name, position FROM dynamic_alternatives ORDER BY id")
        alternatives_before = cursor.fetchall()
        print(f"\n📊 Alternatif sebelum cleanup ({len(alternatives_before)}):")
        for alt in alternatives_before:
            print(f"   {alt[0]}. {alt[1]} - {alt[2]}")
        
        # Lihat evaluasi yang ada
        cursor.execute("SELECT COUNT(*) FROM dynamic_evaluations")
        eval_count_before = cursor.fetchone()[0]
        print(f"\n📝 Total evaluasi sebelum cleanup: {eval_count_before}")
        
        print("\n🧹 Memulai cleanup...")
        
        # Identifikasi ID untuk Rahmat, Jaya, Bunga
        keep_names = ['Rahmat', 'Jaya', 'Bunga']
        keep_ids = []
        
        for name in keep_names:
            cursor.execute("SELECT id FROM dynamic_alternatives WHERE name LIKE ? LIMIT 1", (f'%{name}%',))
            result = cursor.fetchone()
            if result:
                keep_ids.append(result[0])
                print(f"✅ Akan dipertahankan: {name} (ID: {result[0]})")
            else:
                print(f"⚠️ Tidak ditemukan: {name}")
        
        if not keep_ids:
            print("❌ Tidak ada alternatif yang cocok ditemukan!")
            return False
        
        # Hapus evaluasi untuk alternatif yang akan dihapus
        placeholders = ','.join(['?' for _ in keep_ids])
        cursor.execute(f"DELETE FROM dynamic_evaluations WHERE alternative_id NOT IN ({placeholders})", keep_ids)
        deleted_evals = cursor.rowcount
        print(f"🗑️ Dihapus {deleted_evals} evaluasi")
        
        # Hapus hasil TOPSIS untuk alternatif yang akan dihapus
        cursor.execute(f"DELETE FROM topsis_results WHERE alternative_id NOT IN ({placeholders})", keep_ids)
        deleted_results = cursor.rowcount
        print(f"🗑️ Dihapus {deleted_results} hasil TOPSIS")
        
        # Hapus alternatif yang tidak diinginkan
        cursor.execute(f"DELETE FROM dynamic_alternatives WHERE id NOT IN ({placeholders})", keep_ids)
        deleted_alts = cursor.rowcount
        print(f"🗑️ Dihapus {deleted_alts} alternatif")
        
        # Update nama untuk memastikan konsistensi
        for i, alt_id in enumerate(keep_ids):
            if i < len(keep_names):
                cursor.execute("UPDATE dynamic_alternatives SET name = ?, position = 'Karyawan' WHERE id = ?", 
                             (keep_names[i], alt_id))
        
        # Tambahkan evaluasi jika belum ada
        print("\n📝 Memastikan evaluasi lengkap...")
        
        # Data evaluasi sesuai tabel user
        eval_data = {
            'Rahmat': {'C1': 10, 'C2': 9, 'C3': 10, 'C4': 2, 'C5': 15},
            'Jaya': {'C1': 14, 'C2': 15, 'C3': 12, 'C4': 2, 'C5': 13},
            'Bunga': {'C1': 13, 'C2': 12, 'C3': 15, 'C4': 1, 'C5': 12}
        }
        
        # Get criteria mapping
        cursor.execute("SELECT id, code FROM dynamic_criteria ORDER BY code")
        criteria_map = {code: cid for cid, code in cursor.fetchall()}
        
        # Insert evaluasi
        for name, scores in eval_data.items():
            cursor.execute("SELECT id FROM dynamic_alternatives WHERE name = ?", (name,))
            alt_result = cursor.fetchone()
            if alt_result:
                alt_id = alt_result[0]
                for criteria_code, score in scores.items():
                    if criteria_code in criteria_map:
                        criteria_id = criteria_map[criteria_code]
                        cursor.execute("""
                            INSERT OR REPLACE INTO dynamic_evaluations 
                            (alternative_id, criteria_id, score, created_at, updated_at) 
                            VALUES (?, ?, ?, datetime('now'), datetime('now'))
                        """, (alt_id, criteria_id, score))
                print(f"✅ Evaluasi untuk {name} dipastikan lengkap")
        
        conn.commit()
        
        # Verifikasi hasil
        print("\n" + "="*50)
        print("✅ CLEANUP SELESAI!")
        print("="*50)
        
        cursor.execute("SELECT id, name, position FROM dynamic_alternatives ORDER BY name")
        alternatives_after = cursor.fetchall()
        print(f"\n👥 Alternatif setelah cleanup ({len(alternatives_after)}):")
        for alt in alternatives_after:
            print(f"   {alt[0]}. {alt[1]} - {alt[2]}")
        
        cursor.execute("SELECT COUNT(*) FROM dynamic_evaluations")
        eval_count_after = cursor.fetchone()[0]
        print(f"\n📝 Total evaluasi setelah cleanup: {eval_count_after}")
        
        # Detail evaluasi per alternatif
        cursor.execute("""
            SELECT a.name, COUNT(e.id) as eval_count
            FROM dynamic_alternatives a
            LEFT JOIN dynamic_evaluations e ON a.id = e.alternative_id
            GROUP BY a.id, a.name
            ORDER BY a.name
        """)
        eval_details = cursor.fetchall()
        print(f"\n📊 Detail evaluasi per karyawan:")
        for name, count in eval_details:
            print(f"   {name}: {count} evaluasi")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧹 SPK TOPSIS Enhanced v2.0 - Cleanup Alternatives")
    print("=" * 60)
    print("Author: Muhammad Bayu Prasetyo Wibowo")
    print("NIM: 211011450583")
    print("Kelas: 06TPLP003")
    print("=" * 60)
    print()
    
    print("🎯 Akan menghapus semua alternatif KECUALI:")
    print("   ✅ Rahmat")
    print("   ✅ Jaya")
    print("   ✅ Bunga")
    print()
    
    confirm = input("Lanjutkan cleanup? (y/N): ").strip().lower()
    if confirm == 'y':
        success = cleanup_alternatives()
        
        if success:
            print("\n🎉 Cleanup berhasil!")
            print("\n🎯 Langkah selanjutnya:")
            print("   1. Buka aplikasi SPK TOPSIS")
            print("   2. Login sebagai admin")
            print("   3. Cek menu 'Kelola Alternatif' - seharusnya hanya 3 karyawan")
            print("   4. Jalankan TOPSIS untuk melihat ranking")
        else:
            print("\n❌ Cleanup gagal!")
    else:
        print("Cleanup dibatalkan.")
    
    input("\nPress Enter to exit...")
