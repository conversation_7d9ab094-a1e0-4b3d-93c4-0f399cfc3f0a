#!/usr/bin/env python3
"""
SPK Karyawan TOPSIS Enhanced v2.0
Sistem Pendukung Keputusan dengan Login dan Sistem Dinamis

Features:
- User Authentication (Login/Logout)
- Dynamic Criteria Management
- Dynamic Alternatives Management
- Enhanced TOPSIS Calculator
- Role-based Access Control

Author: <PERSON> Wibowo
NIM: 211011450583
Kelas: 06TPLP003
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []

    required_modules = [
        'numpy', 'pandas', 'matplotlib', 'openpyxl', 'tkinter'
    ]

    for module in required_modules:
        try:
            if module == 'tkinter':
                import tkinter
            else:
                __import__(module)
        except ImportError:
            missing_deps.append(module)

    if missing_deps:
        error_msg = f"""
Dependensi yang diperlukan tidak ditemukan:
{', '.join(missing_deps)}

<PERSON><PERSON> install dependensi dengan perintah:
pip install {' '.join([m for m in missing_deps if m != 'tkinter'])}

Catatan: tkinter biasanya sudah terinstall dengan Python
        """
        messagebox.showerror("Dependensi Tidak Ditemukan", error_msg.strip())
        return False

    return True

def setup_matplotlib():
    """Setup matplotlib for tkinter backend"""
    try:
        import matplotlib
        matplotlib.use('TkAgg')  # Use TkAgg backend for tkinter integration

        # Set default style
        import matplotlib.pyplot as plt
        plt.style.use('default')

        # Configure font
        matplotlib.rcParams['font.size'] = 10
        matplotlib.rcParams['font.family'] = 'sans-serif'

        # Suppress warnings
        import warnings
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

    except Exception as e:
        print(f"Warning: Could not setup matplotlib: {e}")

def create_directories():
    """Create necessary directories"""
    directories = ['database', 'exports', 'logs', 'temp']

    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"Created directory: {directory}")
            except Exception as e:
                print(f"Warning: Could not create directory {directory}: {e}")

def test_database_connection():
    """Test database connection"""
    try:
        from database.enhanced_database_manager import EnhancedDatabaseManager
        db_manager = EnhancedDatabaseManager()

        # Test basic operations
        users = db_manager.get_all_users()
        criteria = db_manager.get_all_criteria()

        print(f"✅ Database connection successful")
        print(f"   - Users: {len(users)}")
        print(f"   - Criteria: {len(criteria)}")

        return True

    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def main():
    """Main function to start the enhanced application"""
    try:
        print("=" * 60)
        print("🏆 SPK Karyawan TOPSIS Enhanced v2.0")
        print("=" * 60)
        print("Author: Muhammad Bayu Prasetyo Wibowo")
        print("NIM: 211011450583")
        print("Kelas: 06TPLP003")
        print("=" * 60)
        print()

        # Check dependencies
        print("🔍 Checking dependencies...")
        if not check_dependencies():
            return
        print("✅ All dependencies available")

        # Setup matplotlib
        print("🎨 Setting up matplotlib...")
        setup_matplotlib()
        print("✅ Matplotlib configured")

        # Create necessary directories
        print("📁 Creating directories...")
        create_directories()
        print("✅ Directories ready")

        # Test database connection
        print("🗄️ Testing database connection...")
        if not test_database_connection():
            messagebox.showerror("Database Error",
                               "Gagal terhubung ke database!\n"
                               "Pastikan file database dapat diakses.")
            return
        print("✅ Database ready")

        print()
        print("🚀 Starting Enhanced SPK Application...")
        print("=" * 60)

        # Import and start enhanced main window
        from ui.enhanced_main_window import EnhancedMainWindow

        # Create and run application
        app = EnhancedMainWindow()
        app.run()

        print("👋 Application closed successfully!")

    except ImportError as e:
        error_msg = f"""
Error importing modules: {str(e)}

Pastikan semua file aplikasi tersedia:
- ui/enhanced_main_window.py
- ui/login_window.py
- database/enhanced_database_manager.py
- core/enhanced_topsis_calculator.py

Dan semua dependensi telah diinstall.
        """
        messagebox.showerror("Import Error", error_msg.strip())
        print(f"❌ Import Error: {e}")

    except Exception as e:
        error_msg = f"""
Terjadi kesalahan saat memulai aplikasi:

{str(e)}

Detail error:
{traceback.format_exc()}
        """
        messagebox.showerror("Startup Error", error_msg.strip())
        print(f"❌ Startup Error: {e}")
        print(traceback.format_exc())

if __name__ == "__main__":
    # Set up error handling for tkinter
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        error_msg = f"""
Terjadi kesalahan yang tidak terduga:

{exc_type.__name__}: {exc_value}

Detail:
{''.join(traceback.format_tb(exc_traceback))}
        """

        try:
            messagebox.showerror("Unexpected Error", error_msg.strip())
        except:
            print(error_msg)

    sys.excepthook = handle_exception

    # Start the enhanced application
    main()