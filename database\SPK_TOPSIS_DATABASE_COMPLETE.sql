-- ============================================================
-- SPK TOPSIS ENHANCED v2.0 - COMPLETE DATABASE FILE
-- ============================================================
-- Author: <PERSON>rasetyo Wibowo
-- NIM: 211011450583
-- Kelas: 06TPLP003
-- Database: SQLite 3
-- Purpose: Sistem Pendukung Keputusan Evaluasi Karyawan TOPSIS
-- File: Database lengkap dengan semua komponen penting
-- ============================================================

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================
-- SECTION 1: DROP EXISTING TABLES (Clean Start)
-- ============================================================

DROP TABLE IF EXISTS topsis_results;
DROP TABLE IF EXISTS calculation_history;
DROP TABLE IF EXISTS dynamic_evaluations;
DROP TABLE IF EXISTS dynamic_alternatives;
DROP TABLE IF EXISTS dynamic_criteria;
DROP TABLE IF EXISTS users;

-- Drop views if exist
DROP VIEW IF EXISTS v_decision_matrix;
DROP VIEW IF EXISTS v_latest_ranking;
DROP VIEW IF EXISTS v_system_stats;

-- ============================================================
-- SECTION 2: CREATE TABLES
-- ============================================================

-- 1. TABEL USERS - Authentication dan User Management
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,  -- SHA-256 hashed
    role TEXT NOT NULL CHECK(role IN ('admin', 'operator')),
    full_name TEXT,
    email TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- 2. TABEL DYNAMIC_CRITERIA - Kriteria Evaluasi
CREATE TABLE dynamic_criteria (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    weight REAL NOT NULL CHECK(weight >= 0 AND weight <= 1),
    type TEXT NOT NULL CHECK(type IN ('benefit', 'cost')),
    description TEXT,
    min_value REAL DEFAULT 1.0,
    max_value REAL DEFAULT 15.0,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. TABEL DYNAMIC_ALTERNATIVES - Data Karyawan/Alternatif
CREATE TABLE dynamic_alternatives (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    position TEXT DEFAULT 'Karyawan',
    department TEXT,
    employee_id TEXT UNIQUE,
    description TEXT,
    hire_date DATE,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. TABEL DYNAMIC_EVALUATIONS - Data Evaluasi/Penilaian
CREATE TABLE dynamic_evaluations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    alternative_id INTEGER NOT NULL,
    criteria_id INTEGER NOT NULL,
    score REAL NOT NULL CHECK(score >= 0),
    evaluator TEXT,
    evaluation_date DATE DEFAULT CURRENT_DATE,
    evaluation_period TEXT, -- e.g., "2024-Q1", "2024-01"
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alternative_id) REFERENCES dynamic_alternatives(id) ON DELETE CASCADE,
    FOREIGN KEY (criteria_id) REFERENCES dynamic_criteria(id) ON DELETE CASCADE,
    UNIQUE(alternative_id, criteria_id, evaluation_period)
);

-- 5. TABEL TOPSIS_RESULTS - Hasil Perhitungan TOPSIS
CREATE TABLE topsis_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    calculation_id TEXT NOT NULL,
    alternative_id INTEGER NOT NULL,
    normalized_scores TEXT, -- JSON: normalized values
    weighted_scores TEXT,   -- JSON: weighted normalized values
    positive_distance REAL NOT NULL,
    negative_distance REAL NOT NULL,
    preference_value REAL NOT NULL CHECK(preference_value >= 0 AND preference_value <= 1),
    ranking INTEGER NOT NULL,
    calculation_method TEXT DEFAULT 'TOPSIS',
    calculated_by TEXT,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alternative_id) REFERENCES dynamic_alternatives(id) ON DELETE CASCADE
);

-- 6. TABEL CALCULATION_HISTORY - Riwayat Perhitungan
CREATE TABLE calculation_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    calculation_id TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    evaluation_period TEXT,
    total_alternatives INTEGER NOT NULL,
    total_criteria INTEGER NOT NULL,
    criteria_weights TEXT, -- JSON: criteria weights used
    calculation_status TEXT DEFAULT 'completed' CHECK(calculation_status IN ('pending', 'completed', 'failed')),
    created_by TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================
-- SECTION 3: CREATE INDEXES FOR PERFORMANCE
-- ============================================================

-- Users indexes
CREATE UNIQUE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

-- Criteria indexes
CREATE UNIQUE INDEX idx_criteria_code ON dynamic_criteria(code);
CREATE INDEX idx_criteria_type ON dynamic_criteria(type);
CREATE INDEX idx_criteria_active ON dynamic_criteria(is_active);

-- Alternatives indexes
CREATE INDEX idx_alternatives_name ON dynamic_alternatives(name);
CREATE UNIQUE INDEX idx_alternatives_employee_id ON dynamic_alternatives(employee_id);
CREATE INDEX idx_alternatives_active ON dynamic_alternatives(is_active);
CREATE INDEX idx_alternatives_department ON dynamic_alternatives(department);

-- Evaluations indexes
CREATE UNIQUE INDEX idx_eval_alt_crit_period ON dynamic_evaluations(alternative_id, criteria_id, evaluation_period);
CREATE INDEX idx_eval_alternative ON dynamic_evaluations(alternative_id);
CREATE INDEX idx_eval_criteria ON dynamic_evaluations(criteria_id);
CREATE INDEX idx_eval_date ON dynamic_evaluations(evaluation_date);
CREATE INDEX idx_eval_period ON dynamic_evaluations(evaluation_period);

-- Results indexes
CREATE INDEX idx_results_calculation_id ON topsis_results(calculation_id);
CREATE INDEX idx_results_alternative ON topsis_results(alternative_id);
CREATE INDEX idx_results_ranking ON topsis_results(ranking);
CREATE INDEX idx_results_calculated ON topsis_results(calculated_at);

-- History indexes
CREATE UNIQUE INDEX idx_history_calculation_id ON calculation_history(calculation_id);
CREATE INDEX idx_history_created_at ON calculation_history(created_at);
CREATE INDEX idx_history_period ON calculation_history(evaluation_period);

-- ============================================================
-- SECTION 4: CREATE VIEWS FOR REPORTING
-- ============================================================

-- View: Decision Matrix (Matriks Keputusan)
CREATE VIEW v_decision_matrix AS
SELECT 
    a.id as alternative_id,
    a.name as alternative_name,
    a.position,
    a.employee_id,
    c.id as criteria_id,
    c.code as criteria_code,
    c.name as criteria_name,
    c.weight as criteria_weight,
    c.type as criteria_type,
    e.score,
    e.evaluation_date,
    e.evaluation_period,
    e.evaluator,
    e.notes
FROM dynamic_alternatives a
CROSS JOIN dynamic_criteria c
LEFT JOIN dynamic_evaluations e ON a.id = e.alternative_id AND c.id = e.criteria_id
WHERE a.is_active = 1 AND c.is_active = 1
ORDER BY a.name, c.code;

-- View: Latest Ranking (Ranking Terbaru)
CREATE VIEW v_latest_ranking AS
SELECT 
    r.ranking,
    a.name as karyawan,
    a.position,
    a.employee_id,
    r.preference_value,
    ROUND(r.preference_value * 100, 2) as percentage,
    r.positive_distance,
    r.negative_distance,
    r.calculated_at,
    r.calculated_by,
    h.title as calculation_title
FROM topsis_results r
JOIN dynamic_alternatives a ON r.alternative_id = a.id
JOIN calculation_history h ON r.calculation_id = h.calculation_id
WHERE r.calculation_id = (
    SELECT calculation_id 
    FROM calculation_history 
    WHERE calculation_status = 'completed'
    ORDER BY created_at DESC 
    LIMIT 1
)
ORDER BY r.ranking;

-- View: System Statistics (Statistik Sistem)
CREATE VIEW v_system_stats AS
SELECT 
    (SELECT COUNT(*) FROM dynamic_criteria WHERE is_active = 1) as total_criteria,
    (SELECT COUNT(*) FROM dynamic_alternatives WHERE is_active = 1) as total_alternatives,
    (SELECT COUNT(*) FROM dynamic_evaluations) as total_evaluations,
    (SELECT COUNT(*) FROM calculation_history) as total_calculations,
    (SELECT COUNT(*) FROM users WHERE is_active = 1) as total_users,
    (SELECT MAX(calculated_at) FROM topsis_results) as last_calculation,
    (SELECT SUM(weight) FROM dynamic_criteria WHERE is_active = 1) as total_weight,
    (SELECT COUNT(*) FROM topsis_results WHERE calculation_id = (
        SELECT calculation_id FROM calculation_history 
        ORDER BY created_at DESC LIMIT 1
    )) as latest_results_count;

-- View: Evaluation Completeness (Kelengkapan Evaluasi)
CREATE VIEW v_evaluation_completeness AS
SELECT 
    a.id as alternative_id,
    a.name as alternative_name,
    a.employee_id,
    COUNT(e.id) as completed_evaluations,
    (SELECT COUNT(*) FROM dynamic_criteria WHERE is_active = 1) as total_criteria,
    ROUND(
        CAST(COUNT(e.id) AS REAL) / 
        (SELECT COUNT(*) FROM dynamic_criteria WHERE is_active = 1) * 100, 
        1
    ) as completion_percentage,
    CASE 
        WHEN COUNT(e.id) = (SELECT COUNT(*) FROM dynamic_criteria WHERE is_active = 1) 
        THEN 'Complete'
        ELSE 'Incomplete'
    END as status
FROM dynamic_alternatives a
LEFT JOIN dynamic_evaluations e ON a.id = e.alternative_id
WHERE a.is_active = 1
GROUP BY a.id, a.name, a.employee_id
ORDER BY completion_percentage DESC, a.name;

-- ============================================================
-- SECTION 5: CREATE TRIGGERS FOR DATA INTEGRITY
-- ============================================================

-- Trigger: Update timestamp on criteria update
CREATE TRIGGER update_criteria_timestamp
    AFTER UPDATE ON dynamic_criteria
BEGIN
    UPDATE dynamic_criteria
    SET updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id;
END;

-- Trigger: Update timestamp on alternatives update
CREATE TRIGGER update_alternatives_timestamp
    AFTER UPDATE ON dynamic_alternatives
BEGIN
    UPDATE dynamic_alternatives
    SET updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id;
END;

-- Trigger: Update timestamp on evaluations update
CREATE TRIGGER update_evaluations_timestamp
    AFTER UPDATE ON dynamic_evaluations
BEGIN
    UPDATE dynamic_evaluations
    SET updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.id;
END;

-- Trigger: Validate criteria weights (total should not exceed 1.0)
CREATE TRIGGER validate_criteria_weights_insert
    BEFORE INSERT ON dynamic_criteria
BEGIN
    SELECT CASE
        WHEN (
            SELECT SUM(weight)
            FROM dynamic_criteria
            WHERE is_active = 1
        ) + NEW.weight > 1.0001 THEN
            RAISE(ABORT, 'Total bobot kriteria tidak boleh melebihi 1.0 (100%)')
    END;
END;

CREATE TRIGGER validate_criteria_weights_update
    BEFORE UPDATE ON dynamic_criteria
BEGIN
    SELECT CASE
        WHEN (
            SELECT SUM(weight)
            FROM dynamic_criteria
            WHERE id != NEW.id AND is_active = 1
        ) + NEW.weight > 1.0001 THEN
            RAISE(ABORT, 'Total bobot kriteria tidak boleh melebihi 1.0 (100%)')
    END;
END;

-- Trigger: Validate evaluation scores within criteria range
CREATE TRIGGER validate_evaluation_scores
    BEFORE INSERT ON dynamic_evaluations
BEGIN
    SELECT CASE
        WHEN NEW.score < (
            SELECT min_value FROM dynamic_criteria WHERE id = NEW.criteria_id
        ) OR NEW.score > (
            SELECT max_value FROM dynamic_criteria WHERE id = NEW.criteria_id
        ) THEN
            RAISE(ABORT, 'Nilai evaluasi harus dalam range yang ditentukan untuk kriteria ini')
    END;
END;

-- ============================================================
-- SECTION 6: INSERT INITIAL DATA
-- ============================================================

-- Insert default users with hashed passwords
-- admin123 -> 240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9
-- operator123 -> 5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8
INSERT INTO users (username, password, role, full_name, email, created_at) VALUES
('admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', 'admin', 'Administrator', '<EMAIL>', datetime('now')),
('operator', '5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8', 'operator', 'Data Operator', '<EMAIL>', datetime('now'));

-- Insert criteria with proper weights (total = 100%)
INSERT INTO dynamic_criteria (code, name, weight, type, description, min_value, max_value, created_at, updated_at) VALUES
('C1', 'Kemampuan Teknik', 0.14, 'benefit', 'Kemampuan teknis dalam menjalankan tugas pekerjaan', 1.0, 15.0, datetime('now'), datetime('now')),
('C2', 'Kualitas', 0.19, 'benefit', 'Kualitas hasil kerja yang dihasilkan', 1.0, 15.0, datetime('now'), datetime('now')),
('C3', 'Presisi', 0.28, 'benefit', 'Tingkat ketelitian dan presisi dalam bekerja', 1.0, 15.0, datetime('now'), datetime('now')),
('C4', 'Pelanggaran', 0.18, 'cost', 'Jumlah pelanggaran (semakin rendah semakin baik)', 1.0, 15.0, datetime('now'), datetime('now')),
('C5', 'Absensi', 0.21, 'benefit', 'Tingkat kehadiran karyawan', 1.0, 15.0, datetime('now'), datetime('now'));

-- Insert sample alternatives (employees)
INSERT INTO dynamic_alternatives (name, position, department, employee_id, description, hire_date, created_at, updated_at) VALUES
('Rahmat', 'Karyawan', 'Produksi', 'EMP001', 'Karyawan dengan komitmen kehadiran tinggi', '2022-01-15', datetime('now'), datetime('now')),
('Jaya', 'Karyawan', 'Produksi', 'EMP002', 'Karyawan dengan kemampuan teknik dan kualitas kerja baik', '2022-03-10', datetime('now'), datetime('now')),
('Bunga', 'Karyawan', 'Produksi', 'EMP003', 'Karyawan dengan presisi dan ketelitian kerja tinggi', '2022-02-20', datetime('now'), datetime('now'));

-- Insert sample evaluations (sesuai data tabel user)
INSERT INTO dynamic_evaluations (alternative_id, criteria_id, score, evaluator, evaluation_date, evaluation_period, created_at, updated_at) VALUES
-- Rahmat evaluations (ID=1)
(1, 1, 10.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(1, 2, 9.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(1, 3, 10.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(1, 4, 2.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(1, 5, 15.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
-- Jaya evaluations (ID=2)
(2, 1, 14.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(2, 2, 15.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(2, 3, 12.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(2, 4, 2.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(2, 5, 13.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
-- Bunga evaluations (ID=3)
(3, 1, 13.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(3, 2, 12.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(3, 3, 15.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(3, 4, 1.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now')),
(3, 5, 12.0, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now'));

-- ============================================================
-- SECTION 7: VERIFICATION AND TESTING QUERIES
-- ============================================================

-- Display database information
.print "============================================================"
.print "✅ SPK TOPSIS DATABASE BERHASIL DIBUAT"
.print "============================================================"
.print ""

-- Show users
.print "👤 USERS:"
.headers on
.mode table
SELECT id, username, role, full_name FROM users;
.print ""

-- Show criteria with weights
.print "📊 KRITERIA:"
SELECT
    code,
    name,
    ROUND(weight*100) || '%' as bobot,
    type,
    description
FROM dynamic_criteria
ORDER BY code;
.print ""

-- Show total weight validation
.print "⚖️ VALIDASI BOBOT:"
SELECT
    ROUND(SUM(weight)*100) || '%' as total_bobot,
    CASE
        WHEN ABS(SUM(weight) - 1.0) < 0.001 THEN '✅ Valid'
        ELSE '❌ Invalid'
    END as status
FROM dynamic_criteria;
.print ""

-- Show alternatives
.print "👥 KARYAWAN:"
SELECT id, name, position, employee_id, department FROM dynamic_alternatives ORDER BY name;
.print ""

-- Show evaluation matrix
.print "📝 MATRIKS EVALUASI:"
SELECT
    a.name as Karyawan,
    MAX(CASE WHEN c.code = 'C1' THEN e.score END) as C1,
    MAX(CASE WHEN c.code = 'C2' THEN e.score END) as C2,
    MAX(CASE WHEN c.code = 'C3' THEN e.score END) as C3,
    MAX(CASE WHEN c.code = 'C4' THEN e.score END) as C4,
    MAX(CASE WHEN c.code = 'C5' THEN e.score END) as C5
FROM dynamic_alternatives a
JOIN dynamic_evaluations e ON a.id = e.alternative_id
JOIN dynamic_criteria c ON e.criteria_id = c.id
GROUP BY a.id, a.name
ORDER BY a.name;
.print ""

-- Show system statistics
.print "📈 STATISTIK SISTEM:"
SELECT * FROM v_system_stats;
.print ""

-- Show evaluation completeness
.print "📋 KELENGKAPAN EVALUASI:"
SELECT
    alternative_name as Karyawan,
    completed_evaluations as Selesai,
    total_criteria as Total,
    completion_percentage || '%' as Persentase,
    status as Status
FROM v_evaluation_completeness;
.print ""

.print "============================================================"
.print "🎯 DATABASE SIAP DIGUNAKAN!"
.print ""
.print "📁 File: spk_karyawan_enhanced.db"
.print "📊 Tabel: 6 tabel utama + 4 views"
.print "👤 Users: admin/admin123, operator/operator123"
.print "📝 Data: 5 kriteria, 3 karyawan, 15 evaluasi"
.print ""
.print "🚀 Langkah selanjutnya:"
.print "   1. Jalankan aplikasi SPK TOPSIS"
.print "   2. Login dengan admin/admin123"
.print "   3. Hitung TOPSIS untuk melihat ranking"
.print "============================================================"
