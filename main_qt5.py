#!/usr/bin/env python3
"""
SP<PERSON> Ka<PERSON>wan TOPSIS Enhanced v2.0 - Qt5 Version
Modern Desktop Application with Beautiful UI

Features:
- Modern Qt5 Interface
- User Authentication (Login/Logout)
- Dynamic Criteria Management
- Dynamic Alternatives Management
- Enhanced TOPSIS Calculator
- Role-based Access Control
- Beautiful Modern Design

Author: <PERSON> Wibowo
NIM: 211011450583
Kelas: 06TPLP003
"""

import sys
import os
import traceback

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_qt5_installation():
    """Check if PyQt5 is installed"""
    try:
        import PyQt5
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        return True
    except ImportError as e:
        print("❌ PyQt5 not found!")
        print(f"Error: {e}")
        print("\n📦 To install PyQt5, run:")
        print("pip install PyQt5 PyQt5-tools")
        print("\nOr install all requirements:")
        print("pip install -r requirements.txt")
        return False

def check_dependencies():
    """Check all required dependencies"""
    print("🔍 Checking dependencies...")
    
    dependencies = [
        ('numpy', 'NumPy'),
        ('matplotlib', 'Matplotlib'),
        ('openpyxl', 'OpenPyXL'),
        ('PyQt5', 'PyQt5')
    ]
    
    missing = []
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name} - OK")
        except ImportError:
            print(f"❌ {name} - Missing")
            missing.append(name)
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        print("\n📦 Install missing dependencies:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies available")
    return True

def setup_application():
    """Setup Qt5 application"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont, QIcon
        
        # Create application
        app = QApplication(sys.argv)
        app.setApplicationName("SPK Karyawan TOPSIS Enhanced v2.0")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("Muhammad Bayu Prasetyo Wibowo")
        
        # Set application icon (if available)
        try:
            app.setWindowIcon(QIcon("assets/icon.png"))
        except:
            pass
        
        # Set default font
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # Set application style
        app.setStyle('Fusion')  # Modern style
        
        return app
    except Exception as e:
        print(f"❌ Failed to setup Qt5 application: {e}")
        return None

def run_qt5_application():
    """Run the Qt5 application"""
    print("=" * 60)
    print("🏆 SPK Karyawan TOPSIS Enhanced v2.0 - Qt5 Version")
    print("=" * 60)
    print("Author: Muhammad Bayu Prasetyo Wibowo")
    print("NIM: 211011450583")
    print("Kelas: 06TPLP003")
    print("=" * 60)
    print()
    
    # Check PyQt5 installation
    if not check_qt5_installation():
        return False
    
    # Check all dependencies
    if not check_dependencies():
        return False
    
    print("\n🎨 Setting up Qt5 application...")
    
    # Setup application
    app = setup_application()
    if not app:
        return False
    
    print("✅ Qt5 application ready")
    
    try:
        # Import Qt5 components
        from qt_ui.qt_login_window import show_qt_login
        from qt_ui.qt_main_window import ModernMainWindow
        
        print("\n🔐 Starting login process...")
        
        # Show login window
        user_data = show_qt_login()
        
        if user_data:
            print(f"✅ Login successful: {user_data['full_name']} ({user_data['role']})")
            print("\n🚀 Starting main application...")
            
            # Create and show main window
            main_window = ModernMainWindow(user_data)
            main_window.show()
            
            print("✅ Qt5 application started successfully!")
            print("\n💡 Enjoy the modern Qt5 interface!")
            print("=" * 60)
            
            # Run application
            return app.exec_()
        else:
            print("❌ Login cancelled or failed")
            return False
            
    except Exception as e:
        print(f"❌ Error starting Qt5 application: {e}")
        print("\nFull error traceback:")
        traceback.print_exc()
        return False

def main():
    """Main entry point"""
    try:
        result = run_qt5_application()
        if result:
            print("\n👋 Qt5 application closed successfully!")
        else:
            print("\n❌ Qt5 application failed to start")
            print("\n🔄 Fallback: You can still use the Tkinter version:")
            print("python main_enhanced.py")
        
        return result
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Application interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Set high DPI support for modern displays
    try:
        from PyQt5.QtCore import Qt
        from PyQt5.QtWidgets import QApplication
        
        # Enable high DPI scaling
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    except:
        pass
    
    # Run application
    success = main()
    sys.exit(0 if success else 1)
