#!/usr/bin/env python3
"""
Enhanced Database Manager untuk SPK TOPSIS Enhanced v2.0
Author: <PERSON> Wibowo
NIM: ************
Kelas: 06TPLP003

Database manager dengan fitur lengkap untuk sistem TOPSIS
"""

import sqlite3
import json
import hashlib
import os
from datetime import datetime, date
from typing import List, Dict, Tuple, Optional
import uuid

class EnhancedDatabaseManager:
    """Enhanced Database Manager untuk SPK TOPSIS"""
    
    def __init__(self, db_path: str = 'spk_karyawan_enhanced.db'):
        self.db_path = db_path
        self.conn = None
        self.current_user = None
        
        # Auto connect and setup
        if not self.connect():
            self.setup_database()
    
    def connect(self) -> bool:
        """Koneksi ke database"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute("PRAGMA foreign_keys = ON")
            self.conn.row_factory = sqlite3.Row  # Enable column access by name
            return True
        except Exception as e:
            print(f"❌ Error koneksi database: {e}")
            return False
    
    def close(self):
        """Tutup koneksi database"""
        if self.conn:
            self.conn.close()
    
    def setup_database(self):
        """Setup database dari SQL file"""
        try:
            sql_file = os.path.join('database', 'SPK_TOPSIS_DATABASE_COMPLETE.sql')
            if os.path.exists(sql_file):
                with open(sql_file, 'r', encoding='utf-8') as file:
                    sql_script = file.read()
                
                if self.connect():
                    self.conn.executescript(sql_script)
                    self.conn.commit()
                    print("✅ Database setup berhasil")
                    return True
            else:
                print(f"❌ SQL file tidak ditemukan: {sql_file}")
        except Exception as e:
            print(f"❌ Error setup database: {e}")
        return False
    
    # ============================================================
    # USER MANAGEMENT
    # ============================================================
    
    def hash_password(self, password: str) -> str:
        """Hash password menggunakan SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """Autentikasi user"""
        try:
            cursor = self.conn.cursor()
            hashed_password = self.hash_password(password)
            
            cursor.execute("""
                SELECT id, username, role, full_name, email 
                FROM users 
                WHERE username = ? AND password = ? AND is_active = 1
            """, (username, hashed_password))
            
            user = cursor.fetchone()
            if user:
                # Update last login
                cursor.execute("""
                    UPDATE users SET last_login = CURRENT_TIMESTAMP 
                    WHERE id = ?
                """, (user['id'],))
                self.conn.commit()
                
                user_dict = dict(user)
                self.current_user = user_dict
                return user_dict
            return None
        except Exception as e:
            print(f"❌ Error authentication: {e}")
            return None
    
    def get_all_users(self) -> List[Dict]:
        """Ambil semua user"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT id, username, role, full_name, email, is_active, 
                       created_at, last_login
                FROM users
                ORDER BY username
            """)
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"❌ Error getting users: {e}")
            return []
    
    def logout_user(self):
        """Logout current user"""
        self.current_user = None

    def has_permission(self, permission: str) -> bool:
        """Check if current user has specific permission"""
        if not self.current_user:
            return False

        user_role = self.current_user.get('role', '')

        # Admin has all permissions
        if user_role == 'admin':
            return True

        # Operator permissions
        if user_role == 'operator':
            allowed_permissions = [
                'view_data', 'add_evaluation', 'view_results',
                'export_data', 'view_history', 'edit_criteria',
                'add_criteria', 'manage_users'
            ]
            return permission in allowed_permissions

        return False
    
    # ============================================================
    # CRITERIA MANAGEMENT
    # ============================================================
    
    def get_all_criteria(self) -> List[Dict]:
        """Ambil semua kriteria"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT id, code, name, weight, type, description, min_value, max_value,
                       is_active, created_at, updated_at
                FROM dynamic_criteria
                WHERE is_active = 1
                ORDER BY name
            """)

            # Convert to format expected by UI
            criteria = []
            for row in cursor.fetchall():
                criterion = dict(row)
                # Map database columns to UI expected names
                criterion['kode'] = criterion['code']
                criterion['nama'] = criterion['name']
                criterion['bobot'] = criterion['weight']
                criterion['jenis'] = criterion['type']
                criterion['deskripsi'] = criterion.get('description', '')
                criteria.append(criterion)

            return criteria
        except Exception as e:
            print(f"❌ Error getting criteria: {e}")
            return []
    
    def add_criteria(self, code: str, name: str, weight: float, criteria_type: str = 'benefit',
                    description: str = None, min_value: float = None,
                    max_value: float = None) -> bool:
        """Tambah kriteria baru"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO dynamic_criteria
                (code, name, weight, type, description, min_value, max_value)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (code, name, weight, criteria_type, description, min_value, max_value))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error adding criteria: {e}")
            return False

    def update_criteria(self, criteria_id: int, criteria_data: Dict) -> bool:
        """Update kriteria"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                UPDATE dynamic_criteria
                SET code = ?, name = ?, weight = ?, type = ?,
                    description = ?, min_value = ?, max_value = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                criteria_data.get('kode', criteria_data.get('code')),
                criteria_data.get('nama', criteria_data.get('name')),
                criteria_data.get('bobot', criteria_data.get('weight')),
                criteria_data.get('jenis', criteria_data.get('type')),
                criteria_data.get('deskripsi', criteria_data.get('description')),
                criteria_data.get('min_value', 1.0),
                criteria_data.get('max_value', 15.0),
                criteria_id
            ))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error updating criteria: {e}")
            return False

    def delete_criteria(self, criteria_id: int) -> bool:
        """Hapus kriteria (soft delete)"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                UPDATE dynamic_criteria
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (criteria_id,))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error deleting criteria: {e}")
            return False
    
    # ============================================================
    # ALTERNATIVES MANAGEMENT
    # ============================================================
    
    def get_all_alternatives(self) -> List[Dict]:
        """Ambil semua alternatif"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT id, name, position, department, employee_id, description,
                       hire_date, is_active, created_at, updated_at
                FROM dynamic_alternatives
                WHERE is_active = 1
                ORDER BY name
            """)

            # Convert to format expected by UI
            alternatives = []
            for row in cursor.fetchall():
                alternative = dict(row)
                # Map database columns to UI expected names
                alternative['kode'] = alternative.get('employee_id', f"ALT{alternative['id']:03d}")
                alternative['nama'] = alternative['name']
                alternative['posisi'] = alternative['position']
                alternative['deskripsi'] = alternative.get('description', '')
                alternatives.append(alternative)

            return alternatives
        except Exception as e:
            print(f"❌ Error getting alternatives: {e}")
            return []
    
    def add_alternative(self, alternative_data: Dict) -> int:
        """Tambah alternatif/karyawan baru"""
        try:
            cursor = self.conn.cursor()

            # Extract data from dict (supporting both Indonesian and English keys)
            name = alternative_data.get('nama', alternative_data.get('name', ''))
            position = alternative_data.get('posisi', alternative_data.get('position', 'Karyawan'))
            employee_id = alternative_data.get('kode', alternative_data.get('employee_id', ''))
            description = alternative_data.get('deskripsi', alternative_data.get('description', ''))
            department = alternative_data.get('department', 'Produksi')
            hire_date = alternative_data.get('hire_date', None)

            cursor.execute("""
                INSERT INTO dynamic_alternatives
                (name, position, department, employee_id, description, hire_date)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (name, position, department, employee_id, description, hire_date))

            self.conn.commit()
            return cursor.lastrowid
        except Exception as e:
            print(f"❌ Error adding alternative: {e}")
            return None

    def update_alternative(self, alternative_id: int, alternative_data: Dict) -> bool:
        """Update alternatif/karyawan"""
        try:
            cursor = self.conn.cursor()

            # Extract data from dict (supporting both Indonesian and English keys)
            name = alternative_data.get('nama', alternative_data.get('name', ''))
            position = alternative_data.get('posisi', alternative_data.get('position', 'Karyawan'))
            employee_id = alternative_data.get('kode', alternative_data.get('employee_id', ''))
            description = alternative_data.get('deskripsi', alternative_data.get('description', ''))
            department = alternative_data.get('department', 'Produksi')

            cursor.execute("""
                UPDATE dynamic_alternatives
                SET name = ?, position = ?, department = ?, employee_id = ?,
                    description = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (name, position, department, employee_id, description, alternative_id))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error updating alternative: {e}")
            return False

    def delete_alternative(self, alternative_id: int) -> bool:
        """Hapus alternatif (soft delete)"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                UPDATE dynamic_alternatives
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (alternative_id,))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error deleting alternative: {e}")
            return False

    def get_alternative_criteria_values(self, alternative_id: int) -> Dict:
        """Ambil nilai kriteria untuk alternatif tertentu"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT criteria_id, score
                FROM dynamic_evaluations
                WHERE alternative_id = ?
            """, (alternative_id,))

            return {row['criteria_id']: row['score'] for row in cursor.fetchall()}
        except Exception as e:
            print(f"❌ Error getting alternative criteria values: {e}")
            return {}

    def set_alternative_criteria_value(self, alternative_id: int, criteria_id: int, score: float) -> bool:
        """Set nilai kriteria untuk alternatif"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO dynamic_evaluations
                (alternative_id, criteria_id, score, evaluation_date)
                VALUES (?, ?, ?, CURRENT_DATE)
            """, (alternative_id, criteria_id, score))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error setting alternative criteria value: {e}")
            return False
    
    # ============================================================
    # EVALUATIONS MANAGEMENT
    # ============================================================
    
    def get_all_evaluations(self) -> List[Dict]:
        """Ambil semua evaluasi"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT e.id, e.alternative_id, e.criteria_id, e.score,
                       e.evaluator, e.evaluation_period, e.notes,
                       a.name as alternative_name, c.name as criteria_name
                FROM dynamic_evaluations e
                JOIN dynamic_alternatives a ON e.alternative_id = a.id
                JOIN dynamic_criteria c ON e.criteria_id = c.id
                ORDER BY a.name, c.name
            """)
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"❌ Error getting evaluations: {e}")
            return []
    
    def add_evaluation(self, alternative_id: int, criteria_id: int, score: float,
                      evaluator: str = None, evaluation_period: str = None, 
                      notes: str = None) -> bool:
        """Tambah/update evaluasi"""
        try:
            cursor = self.conn.cursor()
            
            if not evaluation_period:
                evaluation_period = f"{datetime.now().year}-Q{(datetime.now().month-1)//3 + 1}"
            
            cursor.execute("""
                INSERT OR REPLACE INTO dynamic_evaluations 
                (alternative_id, criteria_id, score, evaluator, evaluation_period, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (alternative_id, criteria_id, score, evaluator, evaluation_period, notes))
            
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error adding evaluation: {e}")
            return False
    
    # ============================================================
    # TOPSIS CALCULATION SUPPORT
    # ============================================================
    
    def get_decision_matrix(self, evaluation_period: str = None) -> Dict:
        """Ambil matriks keputusan untuk TOPSIS"""
        try:
            cursor = self.conn.cursor()
            
            # Get alternatives
            alternatives = self.get_all_alternatives()
            
            # Get criteria
            criteria = self.get_all_criteria()
            
            # Get evaluations
            if evaluation_period:
                cursor.execute("""
                    SELECT alternative_id, criteria_id, score
                    FROM dynamic_evaluations
                    WHERE evaluation_period = ?
                """, (evaluation_period,))
            else:
                cursor.execute("""
                    SELECT alternative_id, criteria_id, score
                    FROM dynamic_evaluations
                """)
            
            evaluations = cursor.fetchall()
            
            # Build matrix
            matrix = {}
            for alt in alternatives:
                matrix[alt['id']] = {}
                for crit in criteria:
                    matrix[alt['id']][crit['id']] = None
            
            # Fill matrix with evaluation scores
            for eval_row in evaluations:
                alt_id = eval_row['alternative_id']
                crit_id = eval_row['criteria_id']
                score = eval_row['score']
                
                if alt_id in matrix and crit_id in matrix[alt_id]:
                    matrix[alt_id][crit_id] = score
            
            return {
                'alternatives': alternatives,
                'criteria': criteria,
                'matrix': matrix,
                'evaluation_period': evaluation_period
            }
            
        except Exception as e:
            print(f"❌ Error getting decision matrix: {e}")
            return {'alternatives': [], 'criteria': [], 'matrix': {}}
    
    # ============================================================
    # CALCULATION HISTORY
    # ============================================================
    
    def save_calculation_history(self, title: str, description: str = None,
                               evaluation_period: str = None, alternatives: List = None,
                               criteria: List = None, criteria_weights: Dict = None,
                               created_by: str = None) -> bool:
        """Simpan history perhitungan"""
        try:
            cursor = self.conn.cursor()
            calculation_id = str(uuid.uuid4())
            
            if not created_by and self.current_user:
                created_by = self.current_user.get('username', 'system')
            
            cursor.execute("""
                INSERT OR REPLACE INTO calculation_history 
                (calculation_id, title, description, evaluation_period, 
                 total_alternatives, total_criteria, criteria_weights, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                calculation_id, title, description, evaluation_period,
                len(alternatives) if alternatives else 0, 
                len(criteria) if criteria else 0, 
                json.dumps(criteria_weights) if criteria_weights else '{}', 
                created_by
            ))
            
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error saving calculation history: {e}")
            return False
    
    def get_calculation_history(self) -> List[Dict]:
        """Ambil history perhitungan"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT calculation_id, title, description, evaluation_period,
                       total_alternatives, total_criteria, criteria_weights,
                       created_by, created_at
                FROM calculation_history
                ORDER BY created_at DESC
                LIMIT 50
            """)
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"❌ Error getting calculation history: {e}")
            return []
