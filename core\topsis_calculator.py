"""
TOPSIS Calculator untuk SPK Karyawan TOPSIS
Implementasi algoritma TOPSIS (Technique for Order Preference by Similarity to Ideal Solution)
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple
import math

class TOPSISCalculator:
    def __init__(self):
        """Initialize TOPSIS Calculator"""
        self.criteria_codes = ['C1', 'C2', 'C3', 'C4', 'C5']
        self.criteria_names = {
            'C1': 'Kemampuan Teknik',
            'C2': 'Kualitas', 
            'C3': 'Presisi',
            'C4': 'Pelanggaran',
            'C5': 'Absensi'
        }
        self.criteria_types = {
            'C1': 'benefit',
            'C2': 'benefit',
            'C3': 'benefit', 
            'C4': 'cost',
            'C5': 'benefit'
        }
    
    def calculate_topsis(self, employees_data: List[Dict], criteria_weights: Dict[str, float]) -> List[Dict]:
        """
        Menghitung TOPSIS untuk data karyawan
        
        Args:
            employees_data: List data karyawan dengan nilai kriteria
            criteria_weights: Dictionary bobot kriteria {kode: bobot}
            
        Returns:
            List hasil TOPSIS dengan ranking
        """
        if not employees_data:
            return []
        
        # Step 1: Buat decision matrix
        decision_matrix = self._create_decision_matrix(employees_data)
        
        # Step 2: Normalisasi matrix
        normalized_matrix = self._normalize_matrix(decision_matrix)
        
        # Step 3: Weighted normalized matrix
        weighted_matrix = self._create_weighted_matrix(normalized_matrix, criteria_weights)
        
        # Step 4: Tentukan ideal positive dan negative solutions
        ideal_positive, ideal_negative = self._determine_ideal_solutions(weighted_matrix, criteria_weights)
        
        # Step 5: Hitung jarak ke ideal solutions
        distances_positive, distances_negative = self._calculate_distances(weighted_matrix, ideal_positive, ideal_negative)
        
        # Step 6: Hitung preference score
        preference_scores = self._calculate_preference_scores(distances_positive, distances_negative)
        
        # Step 7: Ranking dan status
        results = self._create_results(employees_data, preference_scores, distances_positive, distances_negative)
        
        return results
    
    def _create_decision_matrix(self, employees_data: List[Dict]) -> np.ndarray:
        """Membuat decision matrix dari data karyawan"""
        matrix = []
        for employee in employees_data:
            row = [
                employee['kemampuan_teknik'],  # C1
                employee['kualitas'],          # C2
                employee['presisi'],           # C3
                employee['pelanggaran'],       # C4
                employee['absensi']            # C5
            ]
            matrix.append(row)
        
        return np.array(matrix, dtype=float)
    
    def _normalize_matrix(self, matrix: np.ndarray) -> np.ndarray:
        """
        Normalisasi matrix menggunakan metode euclidean
        Formula: r_ij = x_ij / sqrt(sum(x_ij^2))
        """
        normalized = np.zeros_like(matrix)
        
        for j in range(matrix.shape[1]):  # Untuk setiap kriteria
            column = matrix[:, j]
            sum_squares = np.sum(column ** 2)
            sqrt_sum = math.sqrt(sum_squares)
            
            if sqrt_sum != 0:
                normalized[:, j] = column / sqrt_sum
            else:
                normalized[:, j] = 0
        
        return normalized
    
    def _create_weighted_matrix(self, normalized_matrix: np.ndarray, weights: Dict[str, float]) -> np.ndarray:
        """
        Membuat weighted normalized matrix
        Formula: v_ij = w_j * r_ij
        """
        weighted = np.zeros_like(normalized_matrix)
        
        for j, criteria_code in enumerate(self.criteria_codes):
            weight = weights.get(criteria_code, 0)
            weighted[:, j] = normalized_matrix[:, j] * weight
        
        return weighted
    
    def _determine_ideal_solutions(self, weighted_matrix: np.ndarray, weights: Dict[str, float]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Menentukan ideal positive (A+) dan ideal negative (A-) solutions
        """
        ideal_positive = np.zeros(weighted_matrix.shape[1])
        ideal_negative = np.zeros(weighted_matrix.shape[1])
        
        for j, criteria_code in enumerate(self.criteria_codes):
            column = weighted_matrix[:, j]
            criteria_type = self.criteria_types[criteria_code]
            
            if criteria_type == 'benefit':
                # Untuk benefit: A+ = max, A- = min
                ideal_positive[j] = np.max(column)
                ideal_negative[j] = np.min(column)
            else:  # cost
                # Untuk cost: A+ = min, A- = max
                ideal_positive[j] = np.min(column)
                ideal_negative[j] = np.max(column)
        
        return ideal_positive, ideal_negative
    
    def _calculate_distances(self, weighted_matrix: np.ndarray, 
                           ideal_positive: np.ndarray, ideal_negative: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Menghitung jarak euclidean ke ideal positive dan negative
        Formula: D+ = sqrt(sum((v_ij - A+_j)^2))
                D- = sqrt(sum((v_ij - A-_j)^2))
        """
        distances_positive = np.zeros(weighted_matrix.shape[0])
        distances_negative = np.zeros(weighted_matrix.shape[0])
        
        for i in range(weighted_matrix.shape[0]):
            # Jarak ke ideal positive
            diff_positive = weighted_matrix[i] - ideal_positive
            distances_positive[i] = math.sqrt(np.sum(diff_positive ** 2))
            
            # Jarak ke ideal negative
            diff_negative = weighted_matrix[i] - ideal_negative
            distances_negative[i] = math.sqrt(np.sum(diff_negative ** 2))
        
        return distances_positive, distances_negative
    
    def _calculate_preference_scores(self, distances_positive: np.ndarray, distances_negative: np.ndarray) -> np.ndarray:
        """
        Menghitung preference score (closeness coefficient)
        Formula: Pi = D- / (D+ + D-)
        """
        preference_scores = np.zeros(len(distances_positive))
        
        for i in range(len(distances_positive)):
            d_plus = distances_positive[i]
            d_minus = distances_negative[i]
            
            if (d_plus + d_minus) != 0:
                preference_scores[i] = d_minus / (d_plus + d_minus)
            else:
                preference_scores[i] = 0
        
        return preference_scores
    
    def _create_results(self, employees_data: List[Dict], preference_scores: np.ndarray,
                       distances_positive: np.ndarray, distances_negative: np.ndarray) -> List[Dict]:
        """Membuat hasil akhir dengan ranking dan status"""
        results = []
        
        # Buat list dengan score untuk sorting
        scored_employees = []
        for i, employee in enumerate(employees_data):
            scored_employees.append({
                'employee_data': employee,
                'score': preference_scores[i],
                'distance_positive': distances_positive[i],
                'distance_negative': distances_negative[i]
            })
        
        # Sort berdasarkan score (descending)
        scored_employees.sort(key=lambda x: x['score'], reverse=True)
        
        # Assign ranking dan status
        for rank, item in enumerate(scored_employees, 1):
            employee = item['employee_data']
            score = item['score']
            
            # Tentukan status berdasarkan score
            if score >= 0.7:
                status = "Sangat Direkomendasikan"
            elif score >= 0.5:
                status = "Direkomendasikan"
            elif score >= 0.3:
                status = "Perlu Pertimbangan"
            else:
                status = "Tidak Direkomendasikan"
            
            results.append({
                'employee_id': employee['id'],
                'nama': employee['nama'],
                'nip': employee['nip'],
                'posisi': employee['posisi'],
                'score': round(score, 4),
                'ranking': rank,
                'distance_positive': round(item['distance_positive'], 4),
                'distance_negative': round(item['distance_negative'], 4),
                'status': status,
                'criteria_values': {
                    'C1': employee['kemampuan_teknik'],
                    'C2': employee['kualitas'],
                    'C3': employee['presisi'],
                    'C4': employee['pelanggaran'],
                    'C5': employee['absensi']
                }
            })
        
        return results
    
    def get_calculation_details(self, employees_data: List[Dict], criteria_weights: Dict[str, float]) -> Dict:
        """
        Mendapatkan detail perhitungan TOPSIS untuk debugging/display
        """
        if not employees_data:
            return {}
        
        # Step-by-step calculation
        decision_matrix = self._create_decision_matrix(employees_data)
        normalized_matrix = self._normalize_matrix(decision_matrix)
        weighted_matrix = self._create_weighted_matrix(normalized_matrix, criteria_weights)
        ideal_positive, ideal_negative = self._determine_ideal_solutions(weighted_matrix, criteria_weights)
        distances_positive, distances_negative = self._calculate_distances(weighted_matrix, ideal_positive, ideal_negative)
        preference_scores = self._calculate_preference_scores(distances_positive, distances_negative)
        
        return {
            'decision_matrix': decision_matrix.tolist(),
            'normalized_matrix': normalized_matrix.tolist(),
            'weighted_matrix': weighted_matrix.tolist(),
            'ideal_positive': ideal_positive.tolist(),
            'ideal_negative': ideal_negative.tolist(),
            'distances_positive': distances_positive.tolist(),
            'distances_negative': distances_negative.tolist(),
            'preference_scores': preference_scores.tolist(),
            'criteria_weights': criteria_weights,
            'criteria_types': self.criteria_types
        }
    
    def validate_data(self, employees_data: List[Dict], criteria_weights: Dict[str, float]) -> Tuple[bool, str]:
        """
        Validasi data sebelum perhitungan TOPSIS
        """
        # Validasi data karyawan
        if not employees_data:
            return False, "Data karyawan kosong"
        
        if len(employees_data) < 2:
            return False, "Minimal 2 karyawan diperlukan untuk perhitungan TOPSIS"
        
        # Validasi bobot kriteria
        if not criteria_weights:
            return False, "Bobot kriteria tidak ditemukan"
        
        total_weight = sum(criteria_weights.values())
        if abs(total_weight - 1.0) > 0.001:
            return False, f"Total bobot kriteria harus 1.0, saat ini: {total_weight:.3f}"
        
        # Validasi nilai kriteria
        for employee in employees_data:
            for criteria in ['kemampuan_teknik', 'kualitas', 'presisi', 'pelanggaran', 'absensi']:
                value = employee.get(criteria)
                if value is None or not (1 <= value <= 15):
                    return False, f"Nilai {criteria} untuk {employee.get('nama', 'Unknown')} harus antara 1-15"
        
        return True, "Data valid"
