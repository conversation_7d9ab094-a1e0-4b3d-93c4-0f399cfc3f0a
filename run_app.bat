@echo off
title SPK Karyawan TOPSIS Enhanced v2.0
color 0A

echo ============================================================
echo 🏆 SPK Karyawan TOPSIS Enhanced v2.0
echo ============================================================
echo Author: <PERSON>o Wibowo
echo NIM: 211011450583
echo Kelas: 06TPLP003
echo ============================================================
echo.

echo 🔍 Checking Python installation...
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.8+ first.
    echo 📥 Download from: https://python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python found

echo.
echo 📦 Checking dependencies...
py -c "import numpy, matplotlib, tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔄 Installing required packages...
    py -m pip install numpy matplotlib openpyxl
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies!
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed
) else (
    echo ✅ Dependencies ready
)

echo.
echo 🚀 Starting SPK Application...
echo 💡 Login Credentials:
echo    👑 Admin: admin / admin123 (Full Access)
echo    ⚙️ Operator: operator / operator123 (Data Input)
echo.

py main_enhanced.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Application error occurred!
    pause
) else (
    echo.
    echo 👋 Application closed successfully!
)
