import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Any
import sys
import os

# Tambahkan path parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import DatabaseManager
from topsis import TOPSISCalculator
from models import Employee, TOPSISResult
from gui.employee_form import EmployeeForm
from gui.criteria_form import CriteriaForm
from gui.result_window import ResultWindow

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("SPK Pengangkatan Karyawan Tetap - Metode TOPSIS")
        self.root.geometry("1200x700")
        self.root.resizable(True, True)
        self.root.minsize(1000, 600)

        # Configure close protocol
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Initialize components
        self.db_manager = DatabaseManager()
        self.topsis_calculator = TOPSISCalculator()

        # Setup GUI
        self.setup_styles()
        self.create_widgets()
        self.refresh_employee_list()

        # Center window
        self.center_window()
    
    def setup_styles(self):
        """Setup ttk styles"""
        style = ttk.Style()
        style.theme_use('clam')

        # Configure modern styles
        style.configure('Title.TLabel',
                       font=('Segoe UI', 20, 'bold'),
                       foreground='#2c3e50',
                       background='#ecf0f1')

        style.configure('Subtitle.TLabel',
                       font=('Segoe UI', 12, 'italic'),
                       foreground='#34495e',
                       background='#ecf0f1')

        style.configure('Heading.TLabel',
                       font=('Segoe UI', 11, 'bold'),
                       foreground='#2c3e50')

        style.configure('Action.TButton',
                       font=('Segoe UI', 9, 'bold'),
                       padding=(10, 8))

        style.configure('Primary.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       padding=(20, 15))

        style.configure('Success.TButton',
                       font=('Segoe UI', 9, 'bold'),
                       padding=(12, 8))

        style.configure('Info.TButton',
                       font=('Segoe UI', 9, 'bold'),
                       padding=(12, 8))

        style.configure('Warning.TButton',
                       font=('Segoe UI', 9, 'bold'),
                       padding=(12, 8))

        style.configure('Modern.TLabelFrame',
                       borderwidth=2,
                       relief='solid',
                       background='#ffffff')

        style.configure('Modern.TLabelFrame.Label',
                       font=('Segoe UI', 10, 'bold'),
                       foreground='#2c3e50',
                       background='#ffffff')

        style.configure('Modern.TFrame',
                       background='#ecf0f1')

        style.configure('Header.TFrame',
                       background='#ecf0f1')

        # Configure treeview
        style.configure('Modern.Treeview',
                       font=('Segoe UI', 9),
                       rowheight=25)

        style.configure('Modern.Treeview.Heading',
                       font=('Segoe UI', 9, 'bold'),
                       foreground='#2c3e50')

        # Map button colors
        style.map('Primary.TButton',
                 background=[('active', '#3498db'), ('pressed', '#2980b9')])

        style.map('Success.TButton',
                 background=[('active', '#27ae60'), ('pressed', '#229954')])

        style.map('Info.TButton',
                 background=[('active', '#f39c12'), ('pressed', '#e67e22')])

        style.map('Secondary.TButton',
                 background=[('active', '#6c757d'), ('pressed', '#5a6268')])

        style.map('Warning.TButton',
                 background=[('active', '#e67e22'), ('pressed', '#d35400')])
    
    def create_widgets(self):
        """Create main window widgets"""
        # Set background color
        self.root.configure(bg='#ecf0f1')

        # Main frame with modern styling
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # Header section with gradient-like effect
        header_frame = ttk.Frame(main_frame, padding="20")
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        # header_frame.configure(style='Header.TFrame')

        # Title with icon-like symbol
        title_text = "🏢 Sistem Pendukung Keputusan"
        title_label = ttk.Label(header_frame, text=title_text, style='Title.TLabel')
        title_label.pack()

        # Subtitle with modern styling
        subtitle_text = "📊 Pengangkatan Karyawan Tetap - Metode TOPSIS"
        subtitle_label = ttk.Label(header_frame, text=subtitle_text, style='Subtitle.TLabel')
        subtitle_label.pack(pady=(5, 0))

        # Separator line
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Left panel - Controls with modern design - LEBIH LEBAR
        left_frame = ttk.LabelFrame(main_frame, text="🎛️ Panel Kontrol", padding="15")
        left_frame.grid(row=2, column=0, sticky=(tk.W, tk.N, tk.S), padx=(0, 15))
        left_frame.configure(width=500)  # Lebih lebar untuk fullscreen laptop

        # Employee management section
        emp_section = ttk.Frame(left_frame)
        emp_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(emp_section, text="👥 Manajemen Karyawan", style='Heading.TLabel').pack(pady=(0, 8))

        ttk.Button(emp_section, text="➕ Tambah Karyawan",
                  command=self.add_employee, style='Success.TButton').pack(fill=tk.X, pady=3)

        ttk.Button(emp_section, text="✏️ Edit Karyawan",
                  command=self.edit_employee, style='Info.TButton').pack(fill=tk.X, pady=3)

        ttk.Button(emp_section, text="🗑️ Hapus Karyawan",
                  command=self.delete_employee, style='Action.TButton').pack(fill=tk.X, pady=3)

        # Modern separator
        ttk.Separator(left_frame, orient='horizontal').pack(fill=tk.X, pady=15)

        # Criteria management section
        criteria_section = ttk.Frame(left_frame)
        criteria_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(criteria_section, text="⚖️ Pengaturan Kriteria", style='Heading.TLabel').pack(pady=(0, 8))

        ttk.Button(criteria_section, text="🎯 Atur Bobot Kriteria",
                  command=self.manage_criteria, style='Info.TButton').pack(fill=tk.X, pady=3)

        # Modern separator
        ttk.Separator(left_frame, orient='horizontal').pack(fill=tk.X, pady=15)

        # TOPSIS calculation section with enhanced content
        topsis_section = ttk.Frame(left_frame)
        topsis_section.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(topsis_section, text="🧮 Perhitungan TOPSIS", style='Heading.TLabel').pack(pady=(0, 8))

        # TOPSIS info panel
        topsis_info = ttk.LabelFrame(topsis_section, text="📊 Status TOPSIS", padding="8")
        topsis_info.pack(fill=tk.X, pady=(0, 8))

        # Status variables
        self.topsis_status_var = tk.StringVar()
        self.last_calculation_var = tk.StringVar()
        self.criteria_status_var = tk.StringVar()

        # Status labels
        ttk.Label(topsis_info, textvariable=self.topsis_status_var,
                 font=('Segoe UI', 8), foreground='#17a2b8').pack(anchor=tk.W)
        ttk.Label(topsis_info, textvariable=self.last_calculation_var,
                 font=('Segoe UI', 8), foreground='#6c757d').pack(anchor=tk.W)
        ttk.Label(topsis_info, textvariable=self.criteria_status_var,
                 font=('Segoe UI', 8), foreground='#28a745').pack(anchor=tk.W)

        # Update status
        self.update_topsis_status()

        # TOMBOL UTAMA - LIHAT HASIL & EXPORT
        btn_excel_main = ttk.Button(topsis_section,
                                   text="📊 LIHAT HASIL & EXPORT",
                                   command=self.show_results_and_export,
                                   style='Success.TButton')
        btn_excel_main.pack(fill=tk.X, pady=10, ipady=12)  # Tombol besar utama

        # Separator
        ttk.Separator(topsis_section, orient='horizontal').pack(fill=tk.X, pady=8)

        # Tombol Export Ranking Summary - LEBIH KECIL
        btn_summary = ttk.Button(topsis_section,
                                text="🏆 EXPORT RANKING RINGKAS",
                                command=self.export_ranking_summary,
                                style='Action.TButton')
        btn_summary.pack(fill=tk.X, pady=5, ipady=5)

        # Separator
        ttk.Separator(topsis_section, orient='horizontal').pack(fill=tk.X, pady=8)

        # Tombol Hitung Ranking - KECIL DI BAWAH
        btn_hitung = ttk.Button(topsis_section,
                               text="🚀 Hitung Ranking",
                               command=self.calculate_topsis,
                               style='Info.TButton')
        btn_hitung.pack(fill=tk.X, pady=5, ipady=3)  # Tombol kecil

        # Modern separator
        ttk.Separator(left_frame, orient='horizontal').pack(fill=tk.X, pady=15)

        # Info section
        info_section = ttk.Frame(left_frame)
        info_section.pack(fill=tk.X)

        info_text = ttk.Label(info_section,
                             text="💡 Tips: Pastikan data karyawan dan\nbobot kriteria sudah diatur sebelum\nmenghitung ranking TOPSIS",
                             font=('Segoe UI', 8),
                             foreground='#6c757d',
                             justify='left')
        info_text.pack(pady=5)
        
        # Right panel - Employee list with modern design
        right_frame = ttk.LabelFrame(main_frame, text="📋 Daftar Karyawan", padding="15")
        right_frame.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)

        # Search and filter frame
        search_frame = ttk.Frame(right_frame)
        search_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        search_frame.columnconfigure(1, weight=1)

        ttk.Label(search_frame, text="🔍", font=('Segoe UI', 12)).grid(row=0, column=0, padx=(0, 5))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var,
                               font=('Segoe UI', 9), width=30)
        search_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_search)

        ttk.Button(search_frame, text="🔄", command=self.refresh_employee_list,
                  width=3).grid(row=0, column=2)

        # Treeview for employee list with modern styling
        columns = ('ID', 'Nama', 'NIP', 'Posisi', 'Kemampuan_Teknik', 'Kualitas', 'Presisi', 'Pelanggaran', 'Absensi')
        self.employee_tree = ttk.Treeview(right_frame, columns=columns, show='headings', height=15)

        # Configure columns with better headers
        column_config = {
            'ID': {'width': 50, 'text': 'ID'},
            'Nama': {'width': 150, 'text': '👤 Nama'},
            'NIP': {'width': 100, 'text': '🆔 NIP'},
            'Posisi': {'width': 120, 'text': '💼 Posisi'},
            'Kemampuan_Teknik': {'width': 90, 'text': '🔧 Kemampuan (C1)'},
            'Kualitas': {'width': 80, 'text': '⭐ Kualitas (C2)'},
            'Presisi': {'width': 80, 'text': '🎯 Presisi (C3)'},
            'Pelanggaran': {'width': 90, 'text': '⚠️ Pelanggaran (C4)'},
            'Absensi': {'width': 80, 'text': '📊 Absensi (C5)'}
        }

        for col in columns:
            config = column_config[col]
            self.employee_tree.heading(col, text=config['text'])
            self.employee_tree.column(col, width=config['width'], minwidth=50)

        # Modern scrollbars
        v_scrollbar = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.employee_tree.yview)
        h_scrollbar = ttk.Scrollbar(right_frame, orient=tk.HORIZONTAL, command=self.employee_tree.xview)
        self.employee_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid treeview and scrollbars
        self.employee_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        # Modern status bar
        status_frame = ttk.Frame(main_frame, padding="10")
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(15, 0))
        status_frame.columnconfigure(1, weight=1)

        # Status icon and text
        ttk.Label(status_frame, text="📊", font=('Segoe UI', 12)).grid(row=0, column=0, padx=(0, 10))

        self.status_var = tk.StringVar()
        self.status_var.set("✅ Sistem siap digunakan")
        status_label = ttk.Label(status_frame, textvariable=self.status_var,
                               font=('Segoe UI', 9), foreground='#27ae60')
        status_label.grid(row=0, column=1, sticky=tk.W)

        # Version info
        version_label = ttk.Label(status_frame, text="v1.0 | TOPSIS Method",
                                font=('Segoe UI', 8), foreground='#7f8c8d')
        version_label.grid(row=0, column=2, sticky=tk.E)
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def refresh_employee_list(self):
        """Refresh employee list in treeview"""
        # Clear existing items
        for item in self.employee_tree.get_children():
            self.employee_tree.delete(item)

        # Get employees from database
        employees = self.db_manager.get_all_employees()

        # Apply search filter if exists
        search_term = getattr(self, 'search_var', tk.StringVar()).get().lower()
        if search_term:
            employees = [emp for emp in employees
                        if search_term in emp['nama'].lower() or
                           search_term in emp['nip'].lower() or
                           search_term in emp['posisi'].lower()]

        # Insert employees into treeview with alternating colors
        for i, emp in enumerate(employees):
            values = (
                emp['id'],
                emp['nama'],
                emp['nip'],
                emp['posisi'],
                f"🔧 {emp['kemampuan_teknik']:.0f}",
                f"⭐ {emp['kualitas']:.0f}",
                f"🎯 {emp['presisi']:.0f}",
                f"⚠️ {emp['pelanggaran']:.0f}",
                f"📊 {emp['absensi']:.0f}"
            )

            # Alternate row colors
            tags = ('evenrow',) if i % 2 == 0 else ('oddrow',)
            self.employee_tree.insert('', tk.END, values=values, tags=tags)

        # Configure row colors
        self.employee_tree.tag_configure('evenrow', background='#f8f9fa')
        self.employee_tree.tag_configure('oddrow', background='#ffffff')

        # Update status
        total_employees = len(self.db_manager.get_all_employees())
        if search_term:
            self.status_var.set(f"🔍 Menampilkan {len(employees)} dari {total_employees} karyawan | Filter: '{search_term}'")
        else:
            self.status_var.set(f"📊 Total karyawan: {len(employees)} | Sistem siap digunakan")

        # Update TOPSIS status
        self.update_topsis_status()

    def on_search(self, event=None):
        """Handle search input"""
        self.refresh_employee_list()

    def update_topsis_status(self):
        """Update TOPSIS status information"""
        try:
            # Get employee count
            employees = self.db_manager.get_all_employees()
            employee_count = len(employees)

            # Get criteria weights with better error handling
            try:
                weights = self.db_manager.get_criteria_weights()
                criteria_set = weights and sum(weights.values()) > 0
            except Exception as e:
                print(f"Error getting criteria weights: {e}")
                criteria_set = False

            # Get last calculation results
            try:
                results = self.db_manager.get_latest_topsis_results()
                has_results = len(results) > 0
            except Exception as e:
                print(f"Error getting TOPSIS results: {e}")
                has_results = False

            # Update status with better messaging
            if employee_count == 0:
                self.topsis_status_var.set("⚠️ Belum ada data karyawan")
                self.last_calculation_var.set("📝 Tambahkan karyawan terlebih dahulu")
                self.criteria_status_var.set("💡 Gunakan tombol 'Tambah Karyawan' di atas")
            elif not criteria_set:
                self.topsis_status_var.set(f"👥 {employee_count} karyawan siap")
                self.last_calculation_var.set("⚙️ Atur bobot kriteria terlebih dahulu")
                self.criteria_status_var.set("💡 Gunakan tombol 'Atur Bobot Kriteria' di atas")
            elif not has_results:
                self.topsis_status_var.set(f"✅ {employee_count} karyawan & kriteria siap")
                self.last_calculation_var.set("🚀 Siap untuk perhitungan TOPSIS")
                self.criteria_status_var.set("💡 Klik 'Hitung Ranking' untuk memulai")
            else:
                self.topsis_status_var.set(f"🏆 Hasil tersedia untuk {len(results)} karyawan")
                self.last_calculation_var.set("📊 Perhitungan terakhir berhasil")
                self.criteria_status_var.set("📈 Siap export ke Excel atau lihat detail")

        except Exception as e:
            print(f"Error in update_topsis_status: {e}")
            self.topsis_status_var.set("⚠️ Status tidak dapat dimuat")
            self.last_calculation_var.set("Silakan refresh aplikasi")
            self.criteria_status_var.set("💡 Restart aplikasi jika masalah berlanjut")

    def add_employee(self):
        """Open add employee form"""
        form = EmployeeForm(self.root, self.db_manager)
        self.root.wait_window(form.window)
        self.refresh_employee_list()
    
    def edit_employee(self):
        """Open edit employee form"""
        selected = self.employee_tree.selection()
        if not selected:
            messagebox.showwarning("Peringatan", "Pilih karyawan yang akan diedit")
            return
        
        # Get employee ID
        item = self.employee_tree.item(selected[0])
        employee_id = item['values'][0]
        
        form = EmployeeForm(self.root, self.db_manager, employee_id)
        self.root.wait_window(form.window)
        self.refresh_employee_list()
    
    def delete_employee(self):
        """Delete selected employee"""
        selected = self.employee_tree.selection()
        if not selected:
            messagebox.showwarning("Peringatan", "Pilih karyawan yang akan dihapus")
            return
        
        # Get employee info
        item = self.employee_tree.item(selected[0])
        employee_id = item['values'][0]
        employee_name = item['values'][1]
        
        # Confirm deletion
        if messagebox.askyesno("Konfirmasi", f"Hapus karyawan {employee_name}?"):
            if self.db_manager.delete_employee(employee_id):
                messagebox.showinfo("Sukses", "Karyawan berhasil dihapus")
                self.refresh_employee_list()
            else:
                messagebox.showerror("Error", "Gagal menghapus karyawan")
    
    def manage_criteria(self):
        """Open criteria management form"""
        form = CriteriaForm(self.root, self.db_manager)
        self.root.wait_window(form.window)
    
    def calculate_topsis(self):
        """Calculate TOPSIS ranking"""
        try:
            # Get employees data
            employees = self.db_manager.get_all_employees()
            if not employees:
                messagebox.showwarning("Peringatan", "Tidak ada data karyawan")
                return
            
            # Get criteria weights
            criteria = self.db_manager.get_all_criteria()
            weights = {
                'kemampuan_teknik': next((c['bobot'] for c in criteria if 'Kemampuan Teknik' in c['nama_kriteria'] or 'C1' in c['nama_kriteria']), 0.14),
                'kualitas': next((c['bobot'] for c in criteria if 'Kualitas' in c['nama_kriteria'] or 'C2' in c['nama_kriteria']), 0.19),
                'presisi': next((c['bobot'] for c in criteria if 'Presisi' in c['nama_kriteria'] or 'C3' in c['nama_kriteria']), 0.28),
                'pelanggaran': next((c['bobot'] for c in criteria if 'Pelanggaran' in c['nama_kriteria'] or 'C4' in c['nama_kriteria']), 0.18),
                'absensi': next((c['bobot'] for c in criteria if 'Absensi' in c['nama_kriteria'] or 'C5' in c['nama_kriteria']), 0.21)
            }
            
            # Validate weights
            is_valid, message = self.topsis_calculator.validate_weights(weights)
            if not is_valid:
                messagebox.showerror("Error", f"Bobot kriteria tidak valid: {message}")
                return
            
            self.status_var.set("🧮 Menghitung ranking TOPSIS... Mohon tunggu")
            self.root.update()

            # Calculate TOPSIS
            results = self.topsis_calculator.calculate_topsis(employees, weights)

            # Save results to database
            if self.db_manager.save_topsis_results(results):
                messagebox.showinfo("🎉 Perhitungan Selesai",
                                  f"✅ Perhitungan TOPSIS berhasil!\n\n"
                                  f"📊 Total karyawan dianalisis: {len(results)}\n"
                                  f"🏆 Karyawan terbaik: {results[0]['nama']}\n"
                                  f"⭐ Score tertinggi: {results[0]['score']:.4f}")
                self.status_var.set("✅ Perhitungan TOPSIS selesai | Siap melihat hasil")
                # Update TOPSIS status
                self.update_topsis_status()
            else:
                messagebox.showerror("❌ Error", "Gagal menyimpan hasil perhitungan")
                
        except Exception as e:
            messagebox.showerror("Error", f"Terjadi kesalahan: {str(e)}")
            self.status_var.set("❌ Error dalam perhitungan | Periksa data input")
    
    def show_results(self):
        """Show TOPSIS results window"""
        results = self.db_manager.get_latest_topsis_results()
        if not results:
            messagebox.showwarning("Peringatan", "Belum ada hasil perhitungan.\nSilakan hitung ranking terlebih dahulu.")
            return

        result_window = ResultWindow(self.root, results)

    def show_results_and_export(self):
        """Show TOPSIS results with export options - Main function"""
        try:
            # Cek apakah ada hasil perhitungan
            results = self.db_manager.get_latest_topsis_results()

            if not results:
                # Jika belum ada hasil, hitung dulu
                response = messagebox.askyesno("🧮 Perhitungan Diperlukan",
                                             "Belum ada hasil perhitungan TOPSIS.\n\n"
                                             "Apakah Anda ingin menghitung ranking terlebih dahulu?")
                if response:
                    self.calculate_topsis()
                    # Setelah hitung, cek lagi
                    results = self.db_manager.get_latest_topsis_results()
                    if not results:
                        return
                else:
                    return

            # Tampilkan hasil detail dengan messagebox yang menarik
            if results:
                # Get top 3 employees
                top_employees = results[:3]

                detail_text = "🏆 HASIL RANKING TOPSIS\n"
                detail_text += "=" * 40 + "\n\n"

                for i, result in enumerate(top_employees, 1):
                    # result adalah dictionary, bukan objek
                    score_percentage = result['score'] * 100
                    if i == 1:
                        detail_text += f"🥇 PERINGKAT {i} (TERBAIK)\n"
                    elif i == 2:
                        detail_text += f"🥈 PERINGKAT {i}\n"
                    else:
                        detail_text += f"🥉 PERINGKAT {i}\n"

                    detail_text += f"   👤 Nama: {result['nama']}\n"
                    detail_text += f"   🆔 NIP: {result['nip']}\n"
                    detail_text += f"   💼 Posisi: {result['posisi']}\n"
                    detail_text += f"   ⭐ Score: {result['score']:.4f} ({score_percentage:.1f}%)\n\n"

                detail_text += f"📊 Total Karyawan Dianalisis: {len(results)}\n"
                detail_text += f"📅 Analisis: {len(results)} karyawan berhasil diranking\n\n"
                detail_text += "Apakah Anda ingin export hasil ini ke Excel?"

                # Tampilkan dialog dengan hasil
                response = messagebox.askyesno("🎉 Hasil Perhitungan TOPSIS", detail_text)

                if response:
                    # Jika user pilih Yes, lakukan export
                    self.export_to_excel_main()

        except Exception as e:
            messagebox.showerror("❌ Error", f"Terjadi kesalahan: {str(e)}")

    def export_to_excel_main(self):
        """Export TOPSIS results to Excel - Main function"""
        try:
            print("Export to Excel called!")  # Debug
            results = self.db_manager.get_latest_topsis_results()

            if not results:
                messagebox.showwarning("⚠️ Peringatan",
                                     "Belum ada hasil perhitungan TOPSIS.\n\n"
                                     "Silakan:\n"
                                     "1. Pastikan ada data karyawan\n"
                                     "2. Atur bobot kriteria (opsional)\n"
                                     "3. Klik 'Hitung Ranking' terlebih dahulu")
                return

            # Import pandas dan openpyxl
            try:
                import pandas as pd
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill, Alignment
            except ImportError:
                messagebox.showerror("❌ Error",
                                   "Library pandas dan openpyxl diperlukan untuk export Excel.\n\n"
                                   "Install dengan: pip install pandas openpyxl")
                return

            # File dialog untuk save
            from tkinter import filedialog
            from datetime import datetime

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"TOPSIS_Results_{timestamp}.xlsx"

            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile=default_filename,
                title="Simpan Hasil TOPSIS ke Excel"
            )

            if not file_path:
                return

            # Prepare data untuk Excel
            excel_data = []
            for i, result in enumerate(results, 1):
                # result adalah dictionary, bukan objek
                score_percentage = result['score'] * 100
                if score_percentage >= 80:
                    recommendation = "🌟 Sangat Direkomendasikan"
                elif score_percentage >= 70:
                    recommendation = "⭐ Direkomendasikan"
                elif score_percentage >= 60:
                    recommendation = "💫 Cukup Direkomendasikan"
                else:
                    recommendation = "⚠️ Kurang Direkomendasikan"

                excel_data.append({
                    'Ranking': i,
                    'Nama_Karyawan': result['nama'],
                    'NIP': result['nip'],
                    'Posisi': result['posisi'],
                    'Score_TOPSIS': round(result['score'], 6),
                    'Score_Persentase': f"{score_percentage:.1f}%",
                    'Rekomendasi': recommendation,
                    'Kemampuan_Teknik_C1': result.get('kemampuan_teknik', 'N/A'),
                    'Kualitas_C2': result.get('kualitas', 'N/A'),
                    'Presisi_C3': result.get('presisi', 'N/A'),
                    'Pelanggaran_C4': result.get('pelanggaran', 'N/A'),
                    'Absensi_C5': result.get('absensi', 'N/A'),
                    'Tanggal_Analisis': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })

            # Create DataFrame
            df = pd.DataFrame(excel_data)

            # Save to Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Hasil_TOPSIS', index=False)

                # Get workbook and worksheet
                workbook = writer.book
                worksheet = writer.sheets['Hasil_TOPSIS']

                # Style the header
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                header_font = Font(color="FFFFFF", bold=True)

                for cell in worksheet[1]:
                    cell.fill = header_fill
                    cell.font = header_font
                    cell.alignment = Alignment(horizontal="center")

                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            # Success message
            messagebox.showinfo("🎉 Export Berhasil!",
                              f"✅ Data TOPSIS berhasil diekspor ke Excel!\n\n"
                              f"📁 File: {file_path.split('/')[-1]}\n"
                              f"📊 Total karyawan: {len(excel_data)}\n"
                              f"📈 Format: Excel (.xlsx)\n\n"
                              f"💡 File siap untuk analisis lebih lanjut")

            # Update status
            self.status_var.set(f"📈 Export Excel berhasil | {len(excel_data)} karyawan | {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            print(f"Export error: {e}")  # Debug
            messagebox.showerror("❌ Error Export", f"Gagal mengekspor ke Excel:\n{str(e)}")



    def export_ranking_summary(self):
        """Export ranking summary for permanent employee promotion"""
        try:
            print("Export ranking summary called!")  # Debug
            results = self.db_manager.get_latest_topsis_results()
            print(f"Found {len(results)} results")  # Debug

            if not results:
                messagebox.showwarning("⚠️ Peringatan",
                                     "Belum ada hasil perhitungan TOPSIS.\n\n"
                                     "Silakan:\n"
                                     "1. Pastikan ada data karyawan\n"
                                     "2. Atur bobot kriteria (opsional)\n"
                                     "3. Klik 'Hitung Ranking' terlebih dahulu")
                return

            # Import required libraries
            try:
                import pandas as pd
                from datetime import datetime
            except ImportError:
                messagebox.showerror("❌ Error",
                                   "Library yang diperlukan tidak tersedia.\n\n"
                                   "Untuk menggunakan fitur export, install:\n"
                                   "pip install pandas openpyxl")
                return

            # Filter top performers (recommended for permanent employment)
            top_performers = []
            recommended_performers = []

            for result in results:
                recommendation = result.get_recommendation()
                if "Sangat" in recommendation or result.ranking <= 3:
                    top_performers.append(result)
                elif "Direkomendasikan" in recommendation and "Kurang" not in recommendation:
                    recommended_performers.append(result)

            # Prepare summary data
            summary_data = []

            # Add top performers
            for i, result in enumerate(top_performers, 1):
                summary_data.append({
                    'Prioritas': f"Prioritas {i}",
                    'Ranking_TOPSIS': result.ranking,
                    'Nama_Karyawan': result.nama,
                    'NIP': result.nip,
                    'Posisi': result.posisi,
                    'Score_TOPSIS': round(result.score, 4),
                    'Score_Persentase': f"{result.get_score_percentage():.1f}%",
                    'Status_Rekomendasi': result.get_recommendation(),
                    'Kategori': "🌟 Sangat Direkomendasikan",
                    'Keterangan': "Siap untuk pengangkatan karyawan tetap",
                    'Tanggal_Analisis': datetime.now().strftime("%Y-%m-%d")
                })

            # Add recommended performers
            for i, result in enumerate(recommended_performers, len(top_performers) + 1):
                summary_data.append({
                    'Prioritas': f"Prioritas {i}",
                    'Ranking_TOPSIS': result.ranking,
                    'Nama_Karyawan': result.nama,
                    'NIP': result.nip,
                    'Posisi': result.posisi,
                    'Score_TOPSIS': round(result.score, 4),
                    'Score_Persentase': f"{result.get_score_percentage():.1f}%",
                    'Status_Rekomendasi': result.get_recommendation(),
                    'Kategori': "⭐ Direkomendasikan",
                    'Keterangan': "Dapat dipertimbangkan untuk pengangkatan",
                    'Tanggal_Analisis': datetime.now().strftime("%Y-%m-%d")
                })

            if not summary_data:
                messagebox.showwarning("⚠️ Peringatan",
                                     "Tidak ada karyawan yang direkomendasikan untuk pengangkatan tetap.\n\n"
                                     "Silakan review kriteria penilaian atau tambah data karyawan.")
                return

            # Create DataFrame
            df = pd.DataFrame(summary_data)

            # Ask user for save location
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="💾 Simpan Ranking Karyawan Tetap",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv"), ("All files", "*.*")],
                initialname=f"Ranking_Karyawan_Tetap_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if filename:
                if filename.endswith('.xlsx'):
                    # Excel export with multiple sheets
                    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                        # Main ranking sheet
                        df.to_excel(writer, sheet_name='Ranking_Karyawan_Tetap', index=False)

                        # Summary statistics
                        self.create_promotion_summary_sheet(writer, results, top_performers, recommended_performers)

                        # Criteria analysis
                        self.create_criteria_analysis_sheet(writer)

                        # All employees comparison
                        self.create_all_employees_comparison_sheet(writer, results)

                elif filename.endswith('.csv'):
                    # CSV export
                    df.to_csv(filename, index=False, encoding='utf-8')

                messagebox.showinfo("🎉 Export Berhasil!",
                                  f"✅ Ranking karyawan tetap berhasil diekspor!\n\n"
                                  f"📁 File: {filename}\n"
                                  f"🌟 Sangat direkomendasikan: {len(top_performers)} karyawan\n"
                                  f"⭐ Direkomendasikan: {len(recommended_performers)} karyawan\n"
                                  f"📋 Total kandidat: {len(summary_data)} karyawan\n\n"
                                  f"💡 File siap untuk presentasi ke manajemen")

                # Update status
                self.status_var.set("📋 Export ranking karyawan tetap berhasil | Siap untuk review manajemen")

        except Exception as e:
            messagebox.showerror("❌ Error Export", f"Gagal mengekspor ranking:\n{str(e)}")

    def create_promotion_summary_sheet(self, writer, all_results, top_performers, recommended_performers):
        """Create promotion summary statistics sheet"""
        import pandas as pd
        from datetime import datetime

        # Calculate statistics
        total_employees = len(all_results)
        total_candidates = len(top_performers) + len(recommended_performers)

        summary_stats = {
            'Metrik_Analisis': [
                'Total Karyawan Dianalisis',
                'Kandidat Karyawan Tetap',
                'Sangat Direkomendasikan',
                'Direkomendasikan',
                'Persentase Kandidat',
                'Score Tertinggi Kandidat',
                'Score Terendah Kandidat',
                'Tanggal Analisis',
                'Metode Analisis'
            ],
            'Nilai': [
                total_employees,
                total_candidates,
                len(top_performers),
                len(recommended_performers),
                f"{(total_candidates/total_employees)*100:.1f}%",
                f"{max([r.score for r in top_performers + recommended_performers]):.4f}" if total_candidates > 0 else "N/A",
                f"{min([r.score for r in top_performers + recommended_performers]):.4f}" if total_candidates > 0 else "N/A",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "TOPSIS (Technique for Order Preference by Similarity to Ideal Solution)"
            ],
            'Keterangan': [
                'Jumlah total karyawan yang dianalisis',
                'Karyawan yang memenuhi syarat pengangkatan tetap',
                'Prioritas utama untuk pengangkatan',
                'Dapat dipertimbangkan untuk pengangkatan',
                'Rasio kandidat terhadap total karyawan',
                'Score TOPSIS tertinggi dari kandidat',
                'Score TOPSIS terendah dari kandidat',
                'Waktu pelaksanaan analisis',
                'Metode pengambilan keputusan yang digunakan'
            ]
        }

        summary_df = pd.DataFrame(summary_stats)
        summary_df.to_excel(writer, sheet_name='Ringkasan_Analisis', index=False)

    def create_criteria_analysis_sheet(self, writer):
        """Create criteria analysis sheet"""
        import pandas as pd

        weights = self.db_manager.get_criteria_weights()
        if weights:
            criteria_analysis = {
                'Kriteria_Penilaian': [
                    'Kemampuan Teknik (C1)',
                    'Kualitas (C2)',
                    'Presisi (C3)',
                    'Pelanggaran (C4)',
                    'Absensi (C5)'
                ],
                'Bobot_Kriteria': [
                    weights.get('kemampuan_teknik', 0),
                    weights.get('kualitas', 0),
                    weights.get('presisi', 0),
                    weights.get('pelanggaran', 0),
                    weights.get('absensi', 0)
                ],
                'Persentase_Bobot': [
                    f"{weights.get('kemampuan_teknik', 0)*100:.1f}%",
                    f"{weights.get('kualitas', 0)*100:.1f}%",
                    f"{weights.get('presisi', 0)*100:.1f}%",
                    f"{weights.get('pelanggaran', 0)*100:.1f}%",
                    f"{weights.get('absensi', 0)*100:.1f}%"
                ],
                'Tipe_Kriteria': [
                    'Benefit (Semakin tinggi semakin baik)',
                    'Benefit (Semakin tinggi semakin baik)',
                    'Benefit (Semakin tinggi semakin baik)',
                    'Cost (Semakin rendah semakin baik)',
                    'Benefit (Semakin tinggi semakin baik)'
                ],
                'Deskripsi': [
                    'Kemampuan teknis dalam bekerja',
                    'Kualitas hasil kerja',
                    'Ketepatan dan ketelitian kerja',
                    'Jumlah pelanggaran yang dilakukan',
                    'Tingkat kehadiran karyawan'
                ]
            }

            criteria_df = pd.DataFrame(criteria_analysis)
            criteria_df.to_excel(writer, sheet_name='Analisis_Kriteria', index=False)

    def create_all_employees_comparison_sheet(self, writer, results):
        """Create comparison sheet for all employees"""
        import pandas as pd

        comparison_data = []
        for result in results:
            comparison_data.append({
                'Ranking': result.ranking,
                'Nama_Karyawan': result.nama,
                'NIP': result.nip,
                'Posisi': result.posisi,
                'Score_TOPSIS': round(result.score, 6),
                'Score_Persentase': f"{result.get_score_percentage():.2f}%",
                'Rekomendasi': result.get_recommendation(),
                'Status_Pengangkatan': self.get_promotion_status(result),
                'Keterangan': self.get_promotion_notes(result)
            })

        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_excel(writer, sheet_name='Perbandingan_Semua_Karyawan', index=False)

    def get_promotion_status(self, result):
        """Get promotion status for employee"""
        recommendation = result.get_recommendation()
        if "Sangat" in recommendation or result.ranking <= 3:
            return "🌟 Prioritas Utama"
        elif "Direkomendasikan" in recommendation and "Kurang" not in recommendation:
            return "⭐ Dapat Dipertimbangkan"
        elif "Cukup" in recommendation:
            return "💫 Perlu Evaluasi Lebih Lanjut"
        else:
            return "⚠️ Belum Memenuhi Syarat"

    def get_promotion_notes(self, result):
        """Get promotion notes for employee"""
        score_pct = result.get_score_percentage()
        if score_pct >= 80:
            return "Sangat siap untuk pengangkatan karyawan tetap"
        elif score_pct >= 70:
            return "Siap untuk pengangkatan dengan monitoring"
        elif score_pct >= 60:
            return "Perlu peningkatan sebelum pengangkatan"
        elif score_pct >= 50:
            return "Memerlukan program pengembangan"
        else:
            return "Perlu perbaikan signifikan sebelum dipertimbangkan"

    def on_closing(self):
        """Handle window close event"""
        try:
            # Ask for confirmation before closing
            if messagebox.askokcancel("Keluar Aplikasi",
                                    "Apakah Anda yakin ingin keluar dari aplikasi?\n\n"
                                    "Pastikan semua data sudah tersimpan."):
                # Close database connection if exists
                if hasattr(self, 'db_manager') and self.db_manager:
                    try:
                        # Close any open database connections
                        pass  # DatabaseManager handles this automatically
                    except Exception as e:
                        print(f"Error closing database: {e}")

                # Destroy the window
                self.root.quit()
                self.root.destroy()
        except Exception as e:
            print(f"Error during closing: {e}")
            # Force close if there's an error
            self.root.quit()
            self.root.destroy()

    def run(self):
        """Start the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            self.on_closing()
        except Exception as e:
            print(f"Application error: {e}")
            messagebox.showerror("Error", f"Terjadi kesalahan aplikasi: {str(e)}")
        finally:
            # Ensure proper cleanup
            try:
                if self.root.winfo_exists():
                    self.root.quit()
            except:
                pass
