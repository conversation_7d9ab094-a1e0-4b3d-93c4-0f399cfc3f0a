"""
Enhanced Main Window dengan Login dan <PERSON><PERSON><PERSON>ten<PERSON>, kriteria dinami<PERSON>, dan alternatif dinamis
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.enhanced_database_manager import EnhancedDatabaseManager
from ui.login_window import show_login
from ui.dynamic_criteria_manager import DynamicCriteriaManager
from ui.dynamic_alternatives_manager import DynamicAlternativesManager
from ui.enhanced_dashboard import EnhancedDashboard
from ui.enhanced_results import EnhancedResults
from ui.modern_theme import ModernTheme

class EnhancedMainWindow:
    def __init__(self):
        """Initialize enhanced main window"""
        # Show login first
        login_success, user_data, db_manager = show_login()
        
        if not login_success:
            sys.exit(0)
        
        self.db_manager = db_manager
        self.user_data = user_data
        self.current_frame = None
        
        self.create_main_window()
        self.show_dashboard()
    
    def create_main_window(self):
        """Create modern main application window"""
        self.root = tk.Tk()

        # Modern title with role icon
        role_icon = "👑" if self.user_data['role'] == 'admin' else "⚙️"
        role_name = "Administrator" if self.user_data['role'] == 'admin' else "Data Operator"
        self.root.title(f"🏆 SPK Karyawan TOPSIS Enhanced v2.0 - {role_icon} {self.user_data['full_name']} ({role_name})")

        self.root.geometry("1400x900")
        self.root.state('zoomed')  # Maximize window

        # Set modern background
        self.root.configure(bg='#f8fafc')

        # Initialize modern theme
        self.theme = ModernTheme()
        self.style = self.theme.apply_modern_style(self.root)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create main layout
        self.create_main_layout()
        
        # Create status bar
        self.create_status_bar()
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def configure_styles(self):
        """Configure ttk styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure navigation button style
        style.configure('Nav.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       padding=(15, 8))
        
        # Configure title style
        style.configure('Title.TLabel',
                       font=('Segoe UI', 16, 'bold'),
                       foreground='#2c3e50')
        
        # Configure subtitle style
        style.configure('Subtitle.TLabel',
                       font=('Segoe UI', 10),
                       foreground='#7f8c8d')
    
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📁 File", menu=file_menu)
        file_menu.add_command(label="🏠 Dashboard", command=self.show_dashboard)
        file_menu.add_separator()
        file_menu.add_command(label="🚪 Logout", command=self.logout)
        file_menu.add_command(label="❌ Exit", command=self.on_closing)
        
        # Data menu
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📊 Data", menu=data_menu)
        data_menu.add_command(label="⚖️ Kelola Kriteria", command=self.show_criteria_manager)
        data_menu.add_command(label="👥 Kelola Alternatif", command=self.show_alternatives_manager)
        
        # Analysis menu
        analysis_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="🧮 Analisis", menu=analysis_menu)
        analysis_menu.add_command(label="🏆 Hasil TOPSIS", command=self.show_results)
        analysis_menu.add_command(label="📈 Hitung TOPSIS", command=self.calculate_topsis)
        
        # Admin menu (only for admin users)
        if self.db_manager.has_permission('manage_users'):
            admin_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="👑 Admin", menu=admin_menu)
            admin_menu.add_command(label="👥 Kelola User", command=self.show_user_manager)
            admin_menu.add_command(label="📋 Log Sistem", command=self.show_system_logs)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="❓ Help", menu=help_menu)
        help_menu.add_command(label="📖 Panduan", command=self.show_help)
        help_menu.add_command(label="ℹ️ About", command=self.show_about)
    
    def create_main_layout(self):
        """Create main layout"""
        # Main container
        self.main_container = ttk.Frame(self.root)
        self.main_container.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Navigation frame
        nav_frame = ttk.Frame(self.main_container)
        nav_frame.pack(fill='x', pady=(0, 10))
        
        # Navigation buttons with permission check
        nav_buttons = [
            ("🏠 Dashboard", self.show_dashboard, 'view_dashboard'),
            ("⚖️ Kriteria", self.show_criteria_manager, 'view_criteria'),
            ("👥 Alternatif", self.show_alternatives_manager, 'view_alternatives'),
            ("🏆 Hasil", self.show_results, 'view_results'),
            ("🧮 Hitung", self.calculate_topsis, 'calculate_topsis')
        ]

        for text, command, permission in nav_buttons:
            if self.db_manager.has_permission(permission):
                btn = ttk.Button(nav_frame, text=text, style='Nav.TButton', command=command)
                btn.pack(side='left', padx=(0, 10))
        
        # User info
        user_info = ttk.Label(nav_frame, 
                             text=f"👤 {self.user_data['full_name']} | 🔑 {self.user_data['role'].title()}",
                             style='Subtitle.TLabel')
        user_info.pack(side='right')
        
        # Content frame
        self.content_frame = ttk.Frame(self.main_container)
        self.content_frame.pack(fill='both', expand=True)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill='x', side='bottom')
        
        self.status_var = tk.StringVar()
        self.status_var.set("✅ Ready")
        
        status_label = ttk.Label(self.status_bar, textvariable=self.status_var)
        status_label.pack(side='left', padx=10, pady=5)
        
        # Database info
        db_info = ttk.Label(self.status_bar, 
                           text=f"📊 Database: {os.path.basename(self.db_manager.db_path)}")
        db_info.pack(side='right', padx=10, pady=5)
    
    def clear_content_frame(self):
        """Clear content frame"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        self.current_frame = None
    
    def show_dashboard(self):
        """Show dashboard"""
        self.clear_content_frame()
        self.status_var.set("📊 Dashboard")
        
        try:
            self.current_frame = EnhancedDashboard(self.content_frame, self.db_manager)
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat dashboard: {str(e)}")
    
    def show_criteria_manager(self):
        """Show criteria manager"""
        if not self.db_manager.has_permission('view_criteria'):
            messagebox.showerror("Access Denied", "Anda tidak memiliki permission untuk mengakses Kelola Kriteria!")
            return

        self.clear_content_frame()
        self.status_var.set("⚖️ Kelola Kriteria")

        try:
            self.current_frame = DynamicCriteriaManager(self.content_frame, self.db_manager)
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat kelola kriteria: {str(e)}")
    
    def show_alternatives_manager(self):
        """Show alternatives manager"""
        self.clear_content_frame()
        self.status_var.set("👥 Kelola Alternatif")
        
        try:
            self.current_frame = DynamicAlternativesManager(self.content_frame, self.db_manager)
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat kelola alternatif: {str(e)}")
    
    def show_results(self):
        """Show TOPSIS results"""
        self.clear_content_frame()
        self.status_var.set("🏆 Hasil TOPSIS")
        
        try:
            self.current_frame = EnhancedResults(self.content_frame, self.db_manager)
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat hasil: {str(e)}")
    
    def calculate_topsis(self):
        """Calculate TOPSIS"""
        if not self.db_manager.has_permission('calculate_topsis'):
            messagebox.showerror("Access Denied",
                               "Anda tidak memiliki permission untuk menghitung TOPSIS!\n\n"
                               "Hanya Admin yang dapat melakukan perhitungan TOPSIS.")
            return

        self.status_var.set("🧮 Menghitung TOPSIS...")

        try:
            # Check if we have criteria and alternatives
            criteria = self.db_manager.get_all_criteria()
            alternatives = self.db_manager.get_all_alternatives()

            if not criteria:
                messagebox.showwarning("Peringatan", "Belum ada kriteria yang didefinisikan!")
                return

            if not alternatives:
                messagebox.showwarning("Peringatan", "Belum ada alternatif yang didefinisikan!")
                return

            # Check if total weight = 1.0
            total_weight = sum(c['bobot'] for c in criteria)
            if abs(total_weight - 1.0) > 0.001:
                messagebox.showwarning("Peringatan",
                                     f"Total bobot kriteria harus 100% (1.0)!\n"
                                     f"Saat ini: {total_weight:.3f} ({total_weight*100:.1f}%)")
                return

            # Import and run TOPSIS calculation
            from core.enhanced_topsis_calculator import EnhancedTOPSISCalculator

            calculator = EnhancedTOPSISCalculator(self.db_manager)
            results = calculator.calculate()

            if results:
                messagebox.showinfo("✅ Berhasil",
                                  f"Perhitungan TOPSIS selesai!\n"
                                  f"Total alternatif: {len(results)}")
                self.show_results()
            else:
                messagebox.showerror("❌ Error", "Gagal menghitung TOPSIS!")

        except Exception as e:
            messagebox.showerror("Error", f"Error dalam perhitungan TOPSIS: {str(e)}")
        finally:
            self.status_var.set("✅ Ready")
    
    def show_user_manager(self):
        """Show user manager (admin only)"""
        if not self.db_manager.has_permission('manage_users'):
            messagebox.showerror("Access Denied", "Hanya admin yang dapat mengakses User Management!")
            return

        self.clear_content_frame()
        self.status_var.set("👥 User Management")

        try:
            from ui.user_management import UserManagement
            self.current_frame = UserManagement(self.content_frame, self.db_manager)
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat user management: {str(e)}")
    
    def show_system_logs(self):
        """Show system logs (admin only)"""
        if self.user_data['role'] != 'admin':
            messagebox.showerror("Access Denied", "Hanya admin yang dapat mengakses fitur ini!")
            return
        
        messagebox.showinfo("Info", "Fitur System Logs akan segera tersedia!")
    
    def show_help(self):
        """Show help"""
        help_text = """
🔹 PANDUAN PENGGUNAAN SPK KARYAWAN TOPSIS 🔹

1. 📊 DASHBOARD
   - Melihat ringkasan sistem
   - Statistik kriteria dan alternatif
   - Quick actions

2. ⚖️ KELOLA KRITERIA
   - Tambah/edit/hapus kriteria
   - Atur bobot kriteria
   - Validasi total bobot 100%

3. 👥 KELOLA ALTERNATIF
   - Tambah/edit/hapus alternatif (karyawan)
   - Input nilai untuk setiap kriteria
   - Validasi data input

4. 🧮 HITUNG TOPSIS
   - Jalankan perhitungan TOPSIS
   - Otomatis ranking alternatif
   - Simpan hasil perhitungan

5. 🏆 HASIL
   - Lihat ranking TOPSIS
   - Export hasil ke Excel/CSV
   - Analisis detail

💡 Tips:
- Pastikan total bobot kriteria = 100%
- Input semua nilai kriteria untuk alternatif
- Gunakan nilai 1-15 untuk konsistensi
        """
        
        messagebox.showinfo("📖 Panduan Penggunaan", help_text)
    
    def show_about(self):
        """Show about dialog"""
        about_text = f"""
🏆 SPK Karyawan TOPSIS v2.0

📋 Sistem Pendukung Keputusan untuk Pengangkatan Karyawan Tetap
🔬 Menggunakan Metode TOPSIS (Technique for Order Preference by Similarity to Ideal Solution)

👨‍💻 Pengembang:
Muhammad Bayu Prasetyo Wibowo
NIM: 211011450583
Kelas: 06TPLP003

🆔 User Saat Ini:
Nama: {self.user_data['full_name']}
Username: {self.user_data['username']}
Role: {self.user_data['role'].title()}

🛠️ Teknologi:
- Python 3.8+
- Tkinter GUI
- SQLite Database
- TOPSIS Algorithm

© 2024 - Sistem Penunjang Keputusan
        """
        
        messagebox.showinfo("ℹ️ About", about_text)
    
    def logout(self):
        """Logout user"""
        if messagebox.askyesno("Logout", "Yakin ingin logout?"):
            self.db_manager.logout_user()
            self.root.destroy()
            
            # Show login again
            login_success, user_data, db_manager = show_login()
            if login_success:
                # Create new main window
                new_app = EnhancedMainWindow()
                new_app.run()
    
    def on_closing(self):
        """Handle window closing"""
        if messagebox.askyesno("Exit", "Yakin ingin keluar dari aplikasi?"):
            self.db_manager.logout_user()
            self.root.destroy()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = EnhancedMainWindow()
    app.run()
