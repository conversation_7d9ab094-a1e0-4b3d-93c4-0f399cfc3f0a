import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Tambahkan path parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import DatabaseManager
from models import CRITERIA_MAPPING, CRITERIA_DESCRIPTIONS

class CriteriaForm:
    def __init__(self, parent, db_manager: DatabaseManager):
        self.parent = parent
        self.db_manager = db_manager
        
        # Create window
        self.window = tk.Toplevel(parent)
        self.window.title("Pengaturan Bobot Kriteria")
        self.window.geometry("800x750")
        self.window.resizable(True, True)
        self.window.minsize(750, 650)
        self.window.transient(parent)
        self.window.grab_set()
        
        self.criteria_vars = {}
        self.total_var = tk.DoubleVar()
        
        self.create_widgets()
        self.load_criteria_data()
        self.center_window()
    
    def create_widgets(self):
        """Create form widgets"""
        # Create scrollable main frame
        canvas = tk.Canvas(self.window, highlightthickness=0)
        scrollbar = ttk.Scrollbar(self.window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Enable mouse wheel scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        ttk.Label(main_frame, text="Pengaturan Bobot Kriteria", 
                 font=('Arial', 14, 'bold')).pack(pady=(0, 10))
        
        # Info
        info_text = "Atur bobot untuk setiap kriteria. Total bobot harus sama dengan 1.0 (100%)"
        ttk.Label(main_frame, text=info_text, foreground="blue").pack(pady=(0, 20))
        
        # Criteria frame
        criteria_frame = ttk.LabelFrame(main_frame, text="Kriteria Penilaian", padding="15")
        criteria_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Get criteria from database
        criteria_list = self.db_manager.get_all_criteria()
        
        row = 0
        for criteria in criteria_list:
            criteria_name = criteria['nama_kriteria']
            criteria_key = self.get_criteria_key(criteria_name)
            
            # Criteria label with current percentage
            criteria_label_frame = ttk.Frame(criteria_frame)
            criteria_label_frame.grid(row=row, column=0, sticky=tk.W, pady=5)

            ttk.Label(criteria_label_frame, text=f"{criteria_name}:",
                     font=('Arial', 10, 'bold')).pack(side=tk.LEFT)

            # Current percentage display next to label
            current_percent_label = ttk.Label(criteria_label_frame,
                                             text=f"[{criteria['bobot']*100:.1f}%]",
                                             font=('Arial', 10, 'bold'),
                                             foreground='blue')
            current_percent_label.pack(side=tk.LEFT, padx=(10, 0))

            # Description
            description = CRITERIA_DESCRIPTIONS.get(criteria_key, criteria.get('deskripsi', ''))
            ttk.Label(criteria_frame, text=description,
                     foreground="gray", font=('Arial', 9)).grid(row=row+1, column=0, sticky=tk.W, padx=(20, 0))
            
            # Weight entry
            var = tk.DoubleVar(value=criteria['bobot'])
            self.criteria_vars[criteria['id']] = {
                'var': var,
                'name': criteria_name,
                'key': criteria_key,
                'current_percent_label': current_percent_label
            }

            weight_frame = ttk.Frame(criteria_frame)
            weight_frame.grid(row=row, column=1, sticky=(tk.W, tk.E), padx=(20, 0), pady=5)

            # Scale with better resolution
            scale = ttk.Scale(weight_frame, from_=0, to=1, variable=var,
                             orient=tk.HORIZONTAL, length=300,
                             command=lambda v, cid=criteria['id']: self.update_weight_display(cid, v))
            scale.pack(side=tk.LEFT)

            # Real-time value display
            value_display_frame = ttk.Frame(weight_frame)
            value_display_frame.pack(side=tk.LEFT, padx=(15, 0))

            # Decimal value
            value_label = ttk.Label(value_display_frame, text=f"{var.get():.3f}",
                                   font=('Arial', 11, 'bold'), foreground='navy')
            value_label.pack()

            # Percentage value (large and prominent)
            percent_label = ttk.Label(value_display_frame, text=f"{var.get()*100:.1f}%",
                                     font=('Arial', 12, 'bold'), foreground='darkgreen')
            percent_label.pack()

            # Store reference to labels for updates
            self.criteria_vars[criteria['id']]['label'] = value_label
            self.criteria_vars[criteria['id']]['percent_label'] = percent_label
            
            row += 2
        
        # Configure grid weights
        criteria_frame.columnconfigure(1, weight=1)
        
        # Total weight display - Enhanced
        total_frame = ttk.LabelFrame(main_frame, text="📊 TOTAL BOBOT KRITERIA", padding="20")
        total_frame.pack(fill=tk.X, pady=(0, 20))

        # Main total display
        main_total_frame = ttk.Frame(total_frame)
        main_total_frame.pack(fill=tk.X)

        # Current total (large display)
        self.total_label = ttk.Label(main_total_frame, text="Total: 0.000 (0.0%)",
                                    font=('Arial', 16, 'bold'), foreground='navy')
        self.total_label.pack()

        # Difference indicator
        self.diff_label = ttk.Label(main_total_frame, text="",
                                   font=('Arial', 12, 'bold'))
        self.diff_label.pack(pady=(5, 0))

        # Status with color coding
        self.status_label = ttk.Label(main_total_frame, text="",
                                     font=('Arial', 11, 'bold'))
        self.status_label.pack(pady=(5, 0))

        # Progress bar for visual feedback
        self.progress_frame = ttk.Frame(total_frame)
        self.progress_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(self.progress_frame, text="Progress ke 100%:",
                 font=('Arial', 9)).pack(anchor=tk.W)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.progress_frame, variable=self.progress_var,
                                           maximum=100, length=400)
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="Reset Default", 
                  command=self.reset_default).pack(side=tk.LEFT)
        
        ttk.Button(button_frame, text="Batal", 
                  command=self.window.destroy).pack(side=tk.RIGHT, padx=(10, 0))
        
        ttk.Button(button_frame, text="Simpan", 
                  command=self.save_criteria).pack(side=tk.RIGHT)
        
        # Update total initially
        self.update_total()
    
    def get_criteria_key(self, criteria_name):
        """Get criteria key from name"""
        for key, name in CRITERIA_MAPPING.items():
            if name == criteria_name:
                return key
        return criteria_name.lower().replace(' ', '_')
    
    def load_criteria_data(self):
        """Load criteria data from database"""
        # Data already loaded in create_widgets
        pass
    
    def update_weight_display(self, criteria_id, value):
        """Update weight display when scale changes - Real time feedback"""
        if criteria_id in self.criteria_vars:
            weight = float(value)
            var_info = self.criteria_vars[criteria_id]

            # Update decimal value
            var_info['label'].configure(text=f"{weight:.3f}")

            # Update percentage value (prominent display)
            var_info['percent_label'].configure(text=f"{weight*100:.1f}%")

            # Update current percentage in criteria label
            var_info['current_percent_label'].configure(text=f"[{weight*100:.1f}%]")

        self.update_total()
    
    def update_total(self):
        """Update total weight display with enhanced feedback"""
        total = sum(var_info['var'].get() for var_info in self.criteria_vars.values())
        self.total_var.set(total)
        total_percent = total * 100

        # Update main total display
        self.total_label.configure(text=f"Total: {total:.3f} ({total_percent:.1f}%)")

        # Calculate difference from 100%
        diff = total_percent - 100.0

        # Update difference indicator
        if abs(diff) < 0.1:
            self.diff_label.configure(text="🎯 PERFECT!", foreground="green")
        elif diff > 0:
            self.diff_label.configure(text=f"📈 Kelebihan: +{diff:.1f}%", foreground="red")
        else:
            self.diff_label.configure(text=f"📉 Kekurangan: {diff:.1f}%", foreground="orange")

        # Update status message
        if abs(total - 1.0) < 0.001:
            self.status_label.configure(text="✅ SIAP DISIMPAN - Total bobot valid!", foreground="green")
        elif total > 1.0:
            self.status_label.configure(text="❌ Total terlalu besar - Kurangi beberapa kriteria", foreground="red")
        else:
            self.status_label.configure(text="⚠️ Total terlalu kecil - Tambah beberapa kriteria", foreground="orange")

        # Update progress bar
        progress_value = min(total_percent, 100)  # Cap at 100% for visual
        self.progress_var.set(progress_value)

        # Change progress bar color based on status
        if abs(total - 1.0) < 0.001:
            self.progress_bar.configure(style="green.Horizontal.TProgressbar")
        elif total > 1.0:
            self.progress_bar.configure(style="red.Horizontal.TProgressbar")
        else:
            self.progress_bar.configure(style="orange.Horizontal.TProgressbar")
    
    def reset_default(self):
        """Reset to default weights"""
        default_weights = {
            'Kemampuan Teknik (C1)': 0.14,
            'Kualitas (C2)': 0.19,
            'Presisi (C3)': 0.28,
            'Pelanggaran (C4)': 0.18,
            'Absensi (C5)': 0.21
        }

        for criteria_id, var_info in self.criteria_vars.items():
            criteria_name = var_info['name']
            # Try exact match first, then partial match
            weight = None
            if criteria_name in default_weights:
                weight = default_weights[criteria_name]
            else:
                # Check for partial matches
                for key, val in default_weights.items():
                    if any(part in criteria_name for part in key.split()):
                        weight = val
                        break

            if weight is not None:
                var_info['var'].set(weight)
                self.update_weight_display(criteria_id, weight)
    
    def save_criteria(self):
        """Save criteria weights"""
        try:
            # Validate total weight
            total = sum(var_info['var'].get() for var_info in self.criteria_vars.values())
            if abs(total - 1.0) > 0.001:
                messagebox.showerror("Error", f"Total bobot harus sama dengan 1.0\nSaat ini: {total:.3f}")
                return
            
            # Save each criteria weight
            success_count = 0
            for criteria_id, var_info in self.criteria_vars.items():
                weight = var_info['var'].get()
                if self.db_manager.update_criteria_weight(criteria_id, weight):
                    success_count += 1
            
            if success_count == len(self.criteria_vars):
                messagebox.showinfo("Sukses", "Bobot kriteria berhasil disimpan")
                self.window.destroy()
            else:
                messagebox.showerror("Error", "Gagal menyimpan beberapa bobot kriteria")
                
        except Exception as e:
            messagebox.showerror("Error", f"Terjadi kesalahan: {str(e)}")
    
    def center_window(self):
        """Center the window on parent"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width // 2) - (width // 2)
        y = parent_y + (parent_height // 2) - (height // 2)
        
        self.window.geometry(f'{width}x{height}+{x}+{y}')
