"""
Enhanced Error Handler
Comprehensive error handling and logging system for SPK TOPSIS Application
Author: <PERSON> Wibowo
NIM: 211011450583
Kelas: 06TPLP003
"""

import logging
import traceback
import sys
import os
from datetime import datetime
from tkinter import messagebox
from typing import Optional, Callable, Any
import functools

class ErrorHandler:
    """Enhanced error handler with logging and user-friendly messages"""
    
    def __init__(self, log_file: str = "logs/application.log"):
        self.log_file = log_file
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging configuration"""
        # Create logs directory if not exists
        log_dir = os.path.dirname(self.log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def log_error(self, error: Exception, context: str = ""):
        """Log error with context"""
        error_msg = f"Error in {context}: {str(error)}"
        self.logger.error(error_msg)
        self.logger.error(traceback.format_exc())
    
    def log_info(self, message: str):
        """Log info message"""
        self.logger.info(message)
    
    def log_warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)
    
    def show_user_error(self, title: str, message: str, error: Optional[Exception] = None):
        """Show user-friendly error message"""
        if error:
            self.log_error(error, title)
        
        try:
            messagebox.showerror(title, message)
        except Exception as e:
            print(f"Error showing messagebox: {e}")
            print(f"Original error: {message}")
    
    def show_user_warning(self, title: str, message: str):
        """Show user warning message"""
        self.log_warning(f"{title}: {message}")
        try:
            messagebox.showwarning(title, message)
        except Exception as e:
            print(f"Error showing warning: {e}")
            print(f"Warning: {message}")
    
    def show_user_info(self, title: str, message: str):
        """Show user info message"""
        self.log_info(f"{title}: {message}")
        try:
            messagebox.showinfo(title, message)
        except Exception as e:
            print(f"Error showing info: {e}")
            print(f"Info: {message}")
    
    def handle_database_error(self, error: Exception, operation: str = "database operation"):
        """Handle database-specific errors"""
        error_msg = str(error).lower()
        
        if "no such table" in error_msg:
            user_msg = ("Database belum diinisialisasi dengan benar!\n\n"
                       "Silakan restart aplikasi atau hubungi administrator.")
        elif "database is locked" in error_msg:
            user_msg = ("Database sedang digunakan oleh proses lain!\n\n"
                       "Tutup aplikasi lain yang menggunakan database ini.")
        elif "constraint" in error_msg or "unique" in error_msg:
            user_msg = ("Data yang dimasukkan sudah ada atau tidak valid!\n\n"
                       "Periksa kembali data input Anda.")
        elif "permission denied" in error_msg:
            user_msg = ("Tidak memiliki izin untuk mengakses database!\n\n"
                       "Periksa permission file atau jalankan sebagai administrator.")
        else:
            user_msg = (f"Terjadi kesalahan database saat {operation}!\n\n"
                       f"Detail: {str(error)[:100]}...")
        
        self.show_user_error("❌ Database Error", user_msg, error)
    
    def handle_calculation_error(self, error: Exception, step: str = "calculation"):
        """Handle calculation-specific errors"""
        error_msg = str(error).lower()
        
        if "division by zero" in error_msg or "divide by zero" in error_msg:
            user_msg = ("Terjadi pembagian dengan nol dalam perhitungan!\n\n"
                       "Periksa data input - pastikan tidak ada nilai 0 pada kriteria benefit.")
        elif "invalid value" in error_msg or "nan" in error_msg:
            user_msg = ("Data input tidak valid untuk perhitungan!\n\n"
                       "Pastikan semua nilai numerik dan dalam range yang benar.")
        elif "matrix" in error_msg or "array" in error_msg:
            user_msg = ("Struktur data tidak sesuai untuk perhitungan TOPSIS!\n\n"
                       "Periksa kelengkapan data kriteria dan alternatif.")
        else:
            user_msg = (f"Terjadi kesalahan dalam {step}!\n\n"
                       f"Detail: {str(error)[:100]}...")
        
        self.show_user_error("❌ Calculation Error", user_msg, error)
    
    def handle_file_error(self, error: Exception, operation: str = "file operation"):
        """Handle file-specific errors"""
        error_msg = str(error).lower()
        
        if "permission denied" in error_msg:
            user_msg = ("Tidak memiliki izin untuk mengakses file!\n\n"
                       "Pastikan file tidak sedang dibuka di aplikasi lain.")
        elif "no such file" in error_msg or "not found" in error_msg:
            user_msg = ("File tidak ditemukan!\n\n"
                       "Periksa lokasi file atau pilih file yang benar.")
        elif "disk space" in error_msg or "no space" in error_msg:
            user_msg = ("Ruang disk tidak cukup!\n\n"
                       "Bersihkan ruang disk atau pilih lokasi lain.")
        else:
            user_msg = (f"Terjadi kesalahan file saat {operation}!\n\n"
                       f"Detail: {str(error)[:100]}...")
        
        self.show_user_error("❌ File Error", user_msg, error)

def safe_execute(error_handler: ErrorHandler, context: str = "operation"):
    """Decorator for safe function execution with error handling"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.log_error(e, f"{context} - {func.__name__}")
                
                # Determine error type and handle accordingly
                if "database" in str(e).lower() or "sqlite" in str(e).lower():
                    error_handler.handle_database_error(e, context)
                elif "calculation" in context.lower() or "topsis" in context.lower():
                    error_handler.handle_calculation_error(e, context)
                elif "file" in context.lower() or "export" in context.lower():
                    error_handler.handle_file_error(e, context)
                else:
                    error_handler.show_user_error(
                        f"❌ Error in {context}",
                        f"Terjadi kesalahan saat {context}!\n\n"
                        f"Detail: {str(e)[:100]}...",
                        e
                    )
                return None
        return wrapper
    return decorator

def validate_input(value: Any, field_name: str, data_type: type = str, 
                  min_val: Optional[float] = None, max_val: Optional[float] = None) -> bool:
    """Validate input data with comprehensive checks"""
    try:
        # Check if value is empty
        if value is None or (isinstance(value, str) and value.strip() == ""):
            raise ValueError(f"{field_name} tidak boleh kosong!")
        
        # Type conversion and validation
        if data_type == float:
            try:
                val = float(value)
                if min_val is not None and val < min_val:
                    raise ValueError(f"{field_name} harus >= {min_val}")
                if max_val is not None and val > max_val:
                    raise ValueError(f"{field_name} harus <= {max_val}")
            except ValueError as e:
                if "could not convert" in str(e):
                    raise ValueError(f"{field_name} harus berupa angka!")
                raise
        
        elif data_type == int:
            try:
                val = int(value)
                if min_val is not None and val < min_val:
                    raise ValueError(f"{field_name} harus >= {int(min_val)}")
                if max_val is not None and val > max_val:
                    raise ValueError(f"{field_name} harus <= {int(max_val)}")
            except ValueError as e:
                if "invalid literal" in str(e):
                    raise ValueError(f"{field_name} harus berupa angka bulat!")
                raise
        
        elif data_type == str:
            if len(str(value).strip()) < 2:
                raise ValueError(f"{field_name} minimal 2 karakter!")
        
        return True
        
    except ValueError as e:
        raise e
    except Exception as e:
        raise ValueError(f"Error validating {field_name}: {str(e)}")

# Global error handler instance
global_error_handler = ErrorHandler()
