@echo off
title SPK TOPSIS - Reset Database IDs
color 0C

echo.
echo ============================================================
echo 🔧 SPK TOPSIS Enhanced v2.0 - Reset Database IDs
echo ============================================================
echo Author: <PERSON> Wibowo
echo NIM: 211011450583
echo Kelas: 06TPLP003
echo ============================================================
echo.
echo ⚠️  MASALAH YANG DITEMUKAN:
echo    ID Kriteria dimulai dari 6, 7, 8 (bukan 1, 2, 3)
echo    Ini terjadi karena ada data lama yang dihapus
echo.
echo 🔧 SOLUSI:
echo    Reset database dan buat ulang dengan ID yang bersih
echo.
echo 📊 DATA YANG AKAN DIBUAT:
echo    ✅ Kriteria ID: 1, 2, 3, 4, 5
echo    ✅ Karyawan ID: 1, 2, 3
echo    ✅ Evaluasi: 15 data penilaian
echo.
echo ⚠️  PENTING: Tutup aplikasi desktop terlebih dahulu!
echo.

set /p confirm="Lanjutkan reset database? (Y/N): "
if /i "%confirm%" NEQ "Y" (
    echo Reset dibatalkan.
    pause
    exit /b
)

echo.
echo 🔧 Memulai reset database...

REM Backup database terlebih dahulu
if exist "spk_karyawan_enhanced.db" (
    copy "spk_karyawan_enhanced.db" "spk_karyawan_enhanced_backup_before_reset.db" >nul 2>&1
    echo 📁 Backup database dibuat: spk_karyawan_enhanced_backup_before_reset.db
)

REM Cek apakah Python tersedia
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python ditemukan, menggunakan Python script...
    
    REM Buat Python script untuk reset
    echo import sqlite3 > reset_temp.py
    echo import os >> reset_temp.py
    echo. >> reset_temp.py
    echo if os.path.exists('spk_karyawan_enhanced.db'): >> reset_temp.py
    echo     conn = sqlite3.connect('spk_karyawan_enhanced.db') >> reset_temp.py
    echo     with open('reset_database_ids.sql', 'r') as f: >> reset_temp.py
    echo         sql_script = f.read() >> reset_temp.py
    echo     conn.executescript(sql_script) >> reset_temp.py
    echo     conn.close() >> reset_temp.py
    echo     print('✅ Database berhasil direset!') >> reset_temp.py
    echo else: >> reset_temp.py
    echo     print('❌ Database tidak ditemukan!') >> reset_temp.py
    
    python reset_temp.py
    del reset_temp.py >nul 2>&1
    
    if %errorlevel% == 0 (
        echo ✅ Reset berhasil dengan Python!
        goto :success
    ) else (
        echo ❌ Error saat reset dengan Python
        goto :manual_sql
    )
) else (
    echo ⚠️ Python tidak ditemukan, mencoba SQLite...
    goto :manual_sql
)

:manual_sql
REM Cek apakah SQLite tersedia
sqlite3 -version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ SQLite ditemukan, menjalankan SQL script...
    sqlite3 spk_karyawan_enhanced.db < reset_database_ids.sql
    
    if %errorlevel% == 0 (
        echo ✅ Reset berhasil dengan SQLite!
        goto :success
    ) else (
        echo ❌ Error saat reset dengan SQLite
        goto :manual_instruction
    )
) else (
    echo ⚠️ SQLite tidak ditemukan
    goto :manual_instruction
)

:manual_instruction
echo.
echo 📝 INSTRUKSI MANUAL RESET:
echo.
echo 1. 🗑️ Hapus file database lama:
echo    - Tutup aplikasi
echo    - Hapus file: spk_karyawan_enhanced.db
echo.
echo 2. 🚀 Jalankan aplikasi lagi:
echo    - Database baru akan dibuat otomatis
echo    - ID akan dimulai dari 1
echo.
echo 3. 📊 Tambah data manual:
echo    - Login sebagai admin
echo    - Tambah 5 kriteria (ID akan 1-5)
echo    - Tambah 3 karyawan (ID akan 1-3)
echo    - Input evaluasi
echo.
goto :end

:success
echo.
echo ============================================================
echo ✅ RESET DATABASE BERHASIL!
echo ============================================================
echo.
echo 📊 DATA YANG TELAH DIRESET:
echo.
echo 🏢 KRITERIA (ID: 1-5):
echo    1. C1 - Kemampuan Teknik: 14%% (Benefit)
echo    2. C2 - Kualitas:         19%% (Benefit)
echo    3. C3 - Presisi:          28%% (Benefit)
echo    4. C4 - Pelanggaran:      18%% (Cost)
echo    5. C5 - Absensi:          21%% (Benefit)
echo.
echo 👥 KARYAWAN (ID: 1-3):
echo    1. Rahmat - Karyawan
echo    2. Jaya - Karyawan
echo    3. Bunga - Karyawan
echo.
echo 📝 EVALUASI (15 data):
echo    Rahmat: [10, 9,  10, 2, 15]
echo    Jaya:   [14, 15, 12, 2, 13]
echo    Bunga:  [13, 12, 15, 1, 12]
echo.
echo 🎯 LANGKAH SELANJUTNYA:
echo.
echo 1. 🚀 Buka aplikasi: SPK_TOPSIS_Enhanced_v2.0.exe
echo 2. 🔑 Login sebagai admin: admin / admin123
echo 3. 📊 Cek "Kelola Kriteria" - ID sekarang 1, 2, 3, 4, 5
echo 4. 👥 Cek "Kelola Alternatif" - ID sekarang 1, 2, 3
echo 5. 🧮 Jalankan "Hitung TOPSIS"
echo.
echo 💾 BACKUP: spk_karyawan_enhanced_backup_before_reset.db
echo.

:end
echo ============================================================
echo.
pause
