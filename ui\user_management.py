"""
User Management System
Untuk Admin mengelola user Operator
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class UserManagement:
    def __init__(self, parent, db_manager):
        """Initialize user management"""
        self.parent = parent
        self.db_manager = db_manager
        
        # Check admin permission
        if not self.db_manager.has_permission('manage_users'):
            messagebox.showerror("Access Denied", "Hanya Admin yang dapat mengakses User Management!")
            return
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        self.current_user_id = None
        
        self.create_widgets()
        self.load_users()
    
    def create_widgets(self):
        """Create user management widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="👥 User Management", 
                 style='Title.TLabel').pack(side='left')
        
        ttk.Button(title_frame, text="➕ Tambah User", 
                  command=self.add_user, style='Nav.TButton').pack(side='right')
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=2)
        main_container.grid_columnconfigure(1, weight=1)
        
        # Left side - Users table
        self.create_users_table(main_container)
        
        # Right side - Form
        self.create_user_form(main_container)
    
    def create_users_table(self, parent):
        """Create users table"""
        table_frame = ttk.LabelFrame(parent, text="📋 Daftar User", padding=10)
        table_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))
        
        # Configure grid
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview
        columns = ('ID', 'Username', 'Full Name', 'Role', 'Status', 'Last Login')
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {
            'ID': 50, 'Username': 120, 'Full Name': 200, 
            'Role': 100, 'Status': 80, 'Last Login': 150
        }
        
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.users_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.users_tree.xview)
        self.users_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.users_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Bind events
        self.users_tree.bind('<<TreeviewSelect>>', self.on_user_select)
        self.users_tree.bind('<Double-1>', self.edit_user)
        
        # Context menu
        self.create_context_menu()
        
        # Status
        self.status_var = tk.StringVar()
        status_label = ttk.Label(table_frame, textvariable=self.status_var, 
                               style='Info.TLabel')
        status_label.grid(row=2, column=0, columnspan=2, pady=5)
    
    def create_context_menu(self):
        """Create context menu for users table"""
        self.context_menu = tk.Menu(self.users_tree, tearoff=0)
        self.context_menu.add_command(label="✏️ Edit", command=self.edit_user)
        self.context_menu.add_command(label="🔒 Toggle Status", command=self.toggle_user_status)
        self.context_menu.add_command(label="🔑 Reset Password", command=self.reset_password)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ Hapus", command=self.delete_user)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🔄 Refresh", command=self.load_users)
        
        def show_context_menu(event):
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()
        
        self.users_tree.bind("<Button-3>", show_context_menu)
    
    def create_user_form(self, parent):
        """Create user form"""
        form_frame = ttk.LabelFrame(parent, text="📝 Form User", padding=15)
        form_frame.grid(row=0, column=1, sticky='nsew')
        
        # Form fields
        self.create_form_fields(form_frame)
        
        # Buttons
        self.create_form_buttons(form_frame)
        
        # Permissions info
        self.create_permissions_info(form_frame)
    
    def create_form_fields(self, parent):
        """Create form input fields"""
        # Username
        ttk.Label(parent, text="👤 Username:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(parent, textvariable=self.username_var, width=30)
        self.username_entry.pack(fill='x', pady=(0, 10))
        
        # Full Name
        ttk.Label(parent, text="📝 Nama Lengkap:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.full_name_var = tk.StringVar()
        self.full_name_entry = ttk.Entry(parent, textvariable=self.full_name_var, width=30)
        self.full_name_entry.pack(fill='x', pady=(0, 10))
        
        # Password
        ttk.Label(parent, text="🔒 Password:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(parent, textvariable=self.password_var, width=30, show='*')
        self.password_entry.pack(fill='x', pady=(0, 10))
        
        # Role
        ttk.Label(parent, text="🎭 Role:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.role_var = tk.StringVar(value='operator')
        role_frame = ttk.Frame(parent)
        role_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Radiobutton(role_frame, text="👑 Admin (Full Access)", 
                       variable=self.role_var, value='admin',
                       command=self.update_permissions_display).pack(anchor='w')
        ttk.Radiobutton(role_frame, text="⚙️ Operator (Input Data)", 
                       variable=self.role_var, value='operator',
                       command=self.update_permissions_display).pack(anchor='w')
        
        # Status
        ttk.Label(parent, text="📊 Status:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.is_active_var = tk.BooleanVar(value=True)
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Checkbutton(status_frame, text="✅ User Aktif", 
                       variable=self.is_active_var).pack(anchor='w')
    
    def create_form_buttons(self, parent):
        """Create form buttons"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill='x', pady=10)
        
        self.save_btn = ttk.Button(button_frame, text="💾 Simpan", 
                                  command=self.save_user, style='Action.TButton')
        self.save_btn.pack(side='left', padx=(0, 5))
        
        self.clear_btn = ttk.Button(button_frame, text="🗑️ Clear", 
                                   command=self.clear_form, style='Nav.TButton')
        self.clear_btn.pack(side='left', padx=5)
    
    def create_permissions_info(self, parent):
        """Create permissions info"""
        info_frame = ttk.LabelFrame(parent, text="🔑 Permissions", padding=10)
        info_frame.pack(fill='x', pady=(10, 0))
        
        self.permissions_text = tk.Text(info_frame, height=8, width=30, wrap='word',
                                       font=('Consolas', 9), state='disabled')
        
        permissions_scrollbar = ttk.Scrollbar(info_frame, orient='vertical', 
                                             command=self.permissions_text.yview)
        self.permissions_text.configure(yscrollcommand=permissions_scrollbar.set)
        
        self.permissions_text.pack(side='left', fill='both', expand=True)
        permissions_scrollbar.pack(side='right', fill='y')
        
        # Initial permissions display
        self.update_permissions_display()
    
    def update_permissions_display(self):
        """Update permissions display based on selected role"""
        role = self.role_var.get()
        
        if role == 'admin':
            permissions = [
                "✅ Dashboard Management",
                "✅ Kriteria Management (CRUD)",
                "✅ Alternatif Management (CRUD)",
                "✅ TOPSIS Calculation",
                "✅ Results & Export",
                "✅ User Management",
                "✅ System Logs",
                "✅ All Features"
            ]
        elif role == 'operator':
            permissions = [
                "✅ View Dashboard",
                "👁️ View Kriteria (Read Only)",
                "✅ Alternatif Management (CRUD)",
                "✅ Input Criteria Values",
                "👁️ View Results (Read Only)",
                "❌ No Kriteria Management",
                "❌ No TOPSIS Calculation",
                "❌ No User Management"
            ]
        else:
            permissions = [
                "👁️ View Dashboard (Read Only)",
                "👁️ View Results (Read Only)",
                "❌ No Data Management",
                "❌ No Calculations"
            ]
        
        permissions_text = "\n".join(permissions)
        
        self.permissions_text.configure(state='normal')
        self.permissions_text.delete(1.0, tk.END)
        self.permissions_text.insert(tk.END, permissions_text)
        self.permissions_text.configure(state='disabled')
    
    def load_users(self):
        """Load users data"""
        try:
            # Clear existing items
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)
            
            # Load users from database
            users = self.db_manager.get_all_users()
            
            if not users:
                self.status_var.set("Belum ada user")
                return
            
            # Populate table
            for user in users:
                # Format last login
                last_login = user.get('last_login', '')
                if last_login:
                    try:
                        # Format datetime if needed
                        last_login = last_login[:16] if len(last_login) > 16 else last_login
                    except:
                        last_login = 'Never'
                else:
                    last_login = 'Never'
                
                # Status
                status = "✅ Active" if user.get('is_active', 1) else "❌ Inactive"
                
                # Role with emoji
                role = user.get('role', 'user')
                if role == 'admin':
                    role_display = "👑 Admin"
                elif role == 'operator':
                    role_display = "⚙️ Operator"
                else:
                    role_display = "👤 User"
                
                self.users_tree.insert('', 'end', values=(
                    user.get('id', ''),
                    user.get('username', ''),
                    user.get('full_name', ''),
                    role_display,
                    status,
                    last_login
                ))
            
            self.status_var.set(f"Menampilkan {len(users)} user")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat user: {str(e)}")
            self.status_var.set("Error memuat data")

    def on_user_select(self, event):
        """Handle user selection"""
        selection = self.users_tree.selection()
        if selection:
            self.load_user_to_form(selection[0])

    def load_user_to_form(self, item):
        """Load selected user to form"""
        try:
            values = self.users_tree.item(item, 'values')
            user_id = int(values[0])

            # Get full user data
            users = self.db_manager.get_all_users()
            user = next((u for u in users if u['id'] == user_id), None)

            if user:
                self.current_user_id = user_id
                self.username_var.set(user['username'])
                self.full_name_var.set(user['full_name'])
                self.password_var.set('')  # Don't show password

                # Extract role from display
                role = user.get('role', 'user')
                self.role_var.set(role)

                self.is_active_var.set(bool(user.get('is_active', 1)))

                self.update_permissions_display()
                self.save_btn.configure(text="💾 Update")

        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat data user: {str(e)}")

    def clear_form(self):
        """Clear form fields"""
        self.current_user_id = None
        self.username_var.set('')
        self.full_name_var.set('')
        self.password_var.set('')
        self.role_var.set('operator')
        self.is_active_var.set(True)
        self.update_permissions_display()
        self.save_btn.configure(text="💾 Simpan")

    def validate_form(self) -> bool:
        """Validate form input"""
        if not self.username_var.get().strip():
            messagebox.showerror("Error", "Username harus diisi!")
            return False

        if not self.full_name_var.get().strip():
            messagebox.showerror("Error", "Nama lengkap harus diisi!")
            return False

        if not self.current_user_id and not self.password_var.get().strip():
            messagebox.showerror("Error", "Password harus diisi untuk user baru!")
            return False

        if len(self.username_var.get().strip()) < 3:
            messagebox.showerror("Error", "Username minimal 3 karakter!")
            return False

        if self.password_var.get() and len(self.password_var.get()) < 6:
            messagebox.showerror("Error", "Password minimal 6 karakter!")
            return False

        return True

    def save_user(self):
        """Save user"""
        if not self.validate_form():
            return

        try:
            user_data = {
                'username': self.username_var.get().strip(),
                'full_name': self.full_name_var.get().strip(),
                'role': self.role_var.get()
            }

            if self.password_var.get().strip():
                user_data['password'] = self.password_var.get().strip()

            if self.current_user_id:
                # Update existing user (would need update_user method in db_manager)
                messagebox.showinfo("Info", "Update user belum diimplementasi")
                return
            else:
                # Add new user
                success = self.db_manager.create_user(user_data)
                action = "ditambahkan"

            if success:
                messagebox.showinfo("✅ Berhasil", f"User berhasil {action}!")
                self.clear_form()
                self.load_users()
            else:
                messagebox.showerror("❌ Gagal", f"Gagal {action.replace('di', 'men')} user!\nUsername mungkin sudah ada.")

        except Exception as e:
            messagebox.showerror("Error", f"Error menyimpan user: {str(e)}")

    def add_user(self):
        """Add new user"""
        self.clear_form()
        self.username_entry.focus()

    def edit_user(self):
        """Edit selected user"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih user yang akan diedit!")
            return

        self.load_user_to_form(selection[0])

    def toggle_user_status(self):
        """Toggle user active status"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih user untuk toggle status!")
            return

        messagebox.showinfo("Info", "Toggle status user belum diimplementasi")

    def reset_password(self):
        """Reset user password"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih user untuk reset password!")
            return

        values = self.users_tree.item(selection[0], 'values')
        username = values[1]

        if messagebox.askyesno("Konfirmasi",
                              f"Reset password untuk user '{username}'?\n\n"
                              f"Password akan direset ke default: 'password123'"):
            messagebox.showinfo("Info", "Reset password belum diimplementasi")

    def delete_user(self):
        """Delete selected user"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih user yang akan dihapus!")
            return

        values = self.users_tree.item(selection[0], 'values')
        username = values[1]
        user_id = int(values[0])

        # Prevent deleting current user
        if user_id == self.db_manager.current_user_id:
            messagebox.showerror("Error", "Tidak dapat menghapus user yang sedang login!")
            return

        # Prevent deleting admin if it's the last admin
        if values[3].startswith("👑"):  # Admin role
            users = self.db_manager.get_all_users()
            admin_count = sum(1 for u in users if u.get('role') == 'admin' and u.get('is_active', 1))
            if admin_count <= 1:
                messagebox.showerror("Error", "Tidak dapat menghapus admin terakhir!")
                return

        if messagebox.askyesno("Konfirmasi",
                              f"Yakin ingin menghapus user '{username}'?\n\n"
                              f"Tindakan ini tidak dapat dibatalkan!"):
            messagebox.showinfo("Info", "Delete user belum diimplementasi")
