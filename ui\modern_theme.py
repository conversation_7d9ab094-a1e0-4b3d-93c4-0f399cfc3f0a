"""
Modern Theme System untuk <PERSON><PERSON> TOPSIS
Sistem styling yang sangat menarik dengan gradients, shadows, dan visual effects
"""

import tkinter as tk
from tkinter import ttk
import tkinter.font as tkFont

class ModernTheme:
    """Modern theme dengan visual effects yang menarik"""
    
    def __init__(self):
        """Initialize modern theme"""
        self.setup_colors()
        self.setup_fonts()
    
    def setup_colors(self):
        """Setup modern color palette"""
        # Primary Colors - Modern Blue Gradient
        self.colors = {
            # Primary Palette
            'primary': '#2563eb',           # Modern Blue
            'primary_dark': '#1d4ed8',      # Darker Blue
            'primary_light': '#3b82f6',     # Lighter Blue
            'primary_gradient': '#1e40af',   # Gradient Blue
            
            # Secondary Palette
            'secondary': '#7c3aed',         # Purple
            'secondary_dark': '#6d28d9',    # Dark Purple
            'secondary_light': '#8b5cf6',   # Light Purple
            
            # Accent Colors
            'accent': '#06b6d4',            # Cyan
            'accent_dark': '#0891b2',       # Dark Cyan
            'accent_light': '#22d3ee',      # <PERSON> Cyan
            
            # Success/Warning/Error
            'success': '#10b981',           # Green
            'success_light': '#34d399',     # Light Green
            'warning': '#f59e0b',           # Orange
            'warning_light': '#fbbf24',     # Light Orange
            'error': '#ef4444',             # Red
            'error_light': '#f87171',       # Light Red
            
            # Neutral Colors
            'white': '#ffffff',
            'gray_50': '#f9fafb',
            'gray_100': '#f3f4f6',
            'gray_200': '#e5e7eb',
            'gray_300': '#d1d5db',
            'gray_400': '#9ca3af',
            'gray_500': '#6b7280',
            'gray_600': '#4b5563',
            'gray_700': '#374151',
            'gray_800': '#1f2937',
            'gray_900': '#111827',
            
            # Background Gradients
            'bg_gradient_1': '#667eea',     # Purple-Blue
            'bg_gradient_2': '#764ba2',     # Purple
            'bg_gradient_3': '#f093fb',     # Pink
            'bg_gradient_4': '#f5576c',     # Red-Pink
            
            # Card Colors
            'card_bg': '#ffffff',
            'card_shadow': '#00000010',
            'card_border': '#e5e7eb',
            
            # Text Colors
            'text_primary': '#111827',
            'text_secondary': '#6b7280',
            'text_muted': '#9ca3af',
            'text_white': '#ffffff',
            
            # Interactive States
            'hover': '#f3f4f6',
            'active': '#e5e7eb',
            'focus': '#dbeafe',
            'disabled': '#f9fafb',
        }
    
    def setup_fonts(self):
        """Setup modern font system"""
        self.fonts = {
            # Headings
            'heading_xl': ('Segoe UI', 28, 'bold'),      # Main titles
            'heading_lg': ('Segoe UI', 24, 'bold'),      # Section titles
            'heading_md': ('Segoe UI', 20, 'bold'),      # Subsection titles
            'heading_sm': ('Segoe UI', 16, 'bold'),      # Card titles
            
            # Body Text
            'body_lg': ('Segoe UI', 14, 'normal'),       # Large body text
            'body_md': ('Segoe UI', 12, 'normal'),       # Regular body text
            'body_sm': ('Segoe UI', 10, 'normal'),       # Small body text
            
            # UI Elements
            'button_lg': ('Segoe UI', 14, 'bold'),       # Large buttons
            'button_md': ('Segoe UI', 12, 'bold'),       # Regular buttons
            'button_sm': ('Segoe UI', 10, 'bold'),       # Small buttons
            
            # Special
            'mono': ('Consolas', 11, 'normal'),          # Monospace
            'caption': ('Segoe UI', 9, 'normal'),        # Captions
            'label': ('Segoe UI', 11, 'bold'),           # Form labels
        }
    
    def apply_modern_style(self, root):
        """Apply modern styling to the application"""
        style = ttk.Style(root)
        
        # Configure modern theme
        style.theme_use('clam')  # Base theme
        
        # Configure modern styles
        self.configure_window_style(style)
        self.configure_button_styles(style)
        self.configure_frame_styles(style)
        self.configure_label_styles(style)
        self.configure_entry_styles(style)
        self.configure_treeview_styles(style)
        self.configure_notebook_styles(style)
        self.configure_progressbar_styles(style)
        
        return style
    
    def configure_window_style(self, style):
        """Configure window and background styles"""
        # Main window background
        style.configure('Modern.TFrame',
                       background=self.colors['gray_50'],
                       relief='flat',
                       borderwidth=0)
        
        # Card-like frames
        style.configure('Card.TFrame',
                       background=self.colors['card_bg'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['card_border'])
        
        # Header frames
        style.configure('Header.TFrame',
                       background=self.colors['primary'],
                       relief='flat',
                       borderwidth=0)
    
    def configure_button_styles(self, style):
        """Configure modern button styles"""
        # Primary Button
        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground=self.colors['text_white'],
                       font=self.fonts['button_md'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(20, 10))
        
        style.map('Primary.TButton',
                 background=[('active', self.colors['primary_dark']),
                           ('pressed', self.colors['primary_gradient'])])
        
        # Secondary Button
        style.configure('Secondary.TButton',
                       background=self.colors['gray_100'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['button_md'],
                       borderwidth=1,
                       bordercolor=self.colors['gray_300'],
                       focuscolor='none',
                       relief='solid',
                       padding=(20, 10))
        
        style.map('Secondary.TButton',
                 background=[('active', self.colors['gray_200']),
                           ('pressed', self.colors['gray_300'])])
        
        # Success Button
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground=self.colors['text_white'],
                       font=self.fonts['button_md'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(20, 10))
        
        style.map('Success.TButton',
                 background=[('active', self.colors['success_light'])])
        
        # Warning Button
        style.configure('Warning.TButton',
                       background=self.colors['warning'],
                       foreground=self.colors['text_white'],
                       font=self.fonts['button_md'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(20, 10))
        
        # Error Button
        style.configure('Error.TButton',
                       background=self.colors['error'],
                       foreground=self.colors['text_white'],
                       font=self.fonts['button_md'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(20, 10))
        
        # Navigation Button
        style.configure('Nav.TButton',
                       background=self.colors['accent'],
                       foreground=self.colors['text_white'],
                       font=self.fonts['button_lg'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(25, 12))
        
        style.map('Nav.TButton',
                 background=[('active', self.colors['accent_dark'])])
        
        # Action Button (Large)
        style.configure('Action.TButton',
                       background=self.colors['secondary'],
                       foreground=self.colors['text_white'],
                       font=self.fonts['button_lg'],
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(30, 15))
        
        style.map('Action.TButton',
                 background=[('active', self.colors['secondary_dark'])])
    
    def configure_frame_styles(self, style):
        """Configure frame styles"""
        # Sidebar frame
        style.configure('Sidebar.TFrame',
                       background=self.colors['gray_800'],
                       relief='flat',
                       borderwidth=0)
        
        # Content frame
        style.configure('Content.TFrame',
                       background=self.colors['white'],
                       relief='flat',
                       borderwidth=0)
        
        # Stats card frame
        style.configure('StatsCard.TFrame',
                       background=self.colors['white'],
                       relief='solid',
                       borderwidth=1,
                       bordercolor=self.colors['gray_200'])
    
    def configure_label_styles(self, style):
        """Configure label styles"""
        # Title labels
        style.configure('Title.TLabel',
                       background=self.colors['white'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['heading_lg'])
        
        # Subtitle labels
        style.configure('Subtitle.TLabel',
                       background=self.colors['white'],
                       foreground=self.colors['text_secondary'],
                       font=self.fonts['heading_sm'])
        
        # Header labels (for dark backgrounds)
        style.configure('Header.TLabel',
                       background=self.colors['primary'],
                       foreground=self.colors['text_white'],
                       font=self.fonts['heading_md'])
        
        # Info labels
        style.configure('Info.TLabel',
                       background=self.colors['white'],
                       foreground=self.colors['text_muted'],
                       font=self.fonts['body_sm'])
        
        # Stats labels
        style.configure('Stats.TLabel',
                       background=self.colors['white'],
                       foreground=self.colors['primary'],
                       font=self.fonts['heading_xl'])
        
        # Success labels
        style.configure('Success.TLabel',
                       background=self.colors['white'],
                       foreground=self.colors['success'],
                       font=self.fonts['body_md'])
        
        # Warning labels
        style.configure('Warning.TLabel',
                       background=self.colors['white'],
                       foreground=self.colors['warning'],
                       font=self.fonts['body_md'])
        
        # Error labels
        style.configure('Error.TLabel',
                       background=self.colors['white'],
                       foreground=self.colors['error'],
                       font=self.fonts['body_md'])
    
    def configure_entry_styles(self, style):
        """Configure entry/input styles"""
        style.configure('Modern.TEntry',
                       fieldbackground=self.colors['white'],
                       bordercolor=self.colors['gray_300'],
                       focuscolor=self.colors['primary'],
                       borderwidth=2,
                       relief='solid',
                       padding=(12, 8))
        
        style.map('Modern.TEntry',
                 bordercolor=[('focus', self.colors['primary'])])
    
    def configure_treeview_styles(self, style):
        """Configure treeview/table styles"""
        style.configure('Modern.Treeview',
                       background=self.colors['white'],
                       foreground=self.colors['text_primary'],
                       fieldbackground=self.colors['white'],
                       bordercolor=self.colors['gray_200'],
                       borderwidth=1,
                       relief='solid')
        
        style.configure('Modern.Treeview.Heading',
                       background=self.colors['gray_100'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['label'],
                       borderwidth=1,
                       relief='solid')
        
        style.map('Modern.Treeview',
                 background=[('selected', self.colors['primary']),
                           ('focus', self.colors['focus'])])
    
    def configure_notebook_styles(self, style):
        """Configure notebook/tab styles"""
        style.configure('Modern.TNotebook',
                       background=self.colors['white'],
                       borderwidth=0)
        
        style.configure('Modern.TNotebook.Tab',
                       background=self.colors['gray_100'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['button_md'],
                       padding=(20, 10),
                       borderwidth=1,
                       focuscolor='none')
        
        style.map('Modern.TNotebook.Tab',
                 background=[('selected', self.colors['primary']),
                           ('active', self.colors['gray_200'])],
                 foreground=[('selected', self.colors['text_white'])])
    
    def configure_progressbar_styles(self, style):
        """Configure progressbar styles"""
        style.configure('Modern.Horizontal.TProgressbar',
                       background=self.colors['primary'],
                       troughcolor=self.colors['gray_200'],
                       borderwidth=0,
                       lightcolor=self.colors['primary'],
                       darkcolor=self.colors['primary'])
    
    def create_gradient_frame(self, parent, width=400, height=100, color1='#667eea', color2='#764ba2'):
        """Create a frame with gradient background effect"""
        # Note: Tkinter doesn't support real gradients, but we can simulate with multiple frames
        gradient_frame = tk.Frame(parent, width=width, height=height)
        gradient_frame.pack_propagate(False)
        
        # Create gradient effect with multiple colored frames
        steps = 20
        for i in range(steps):
            # Calculate color interpolation
            ratio = i / (steps - 1)
            # Simple color interpolation (would need more complex for real gradients)
            frame = tk.Frame(gradient_frame, 
                           bg=color1 if i < steps//2 else color2,
                           height=height//steps)
            frame.pack(fill='x')
        
        return gradient_frame
    
    def add_shadow_effect(self, widget, shadow_color='#00000020'):
        """Add shadow effect to widget (simulated)"""
        # Create shadow frame behind the widget
        shadow_frame = tk.Frame(widget.master, 
                              bg=shadow_color,
                              height=widget.winfo_reqheight() + 4,
                              width=widget.winfo_reqwidth() + 4)
        
        # Position shadow slightly offset
        shadow_frame.place(x=widget.winfo_x() + 2, 
                          y=widget.winfo_y() + 2)
        
        # Ensure original widget is on top
        widget.lift()
        
        return shadow_frame
