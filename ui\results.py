"""
Results Frame untuk SPK Karyawan TOPSIS
Menampilkan hasil perhitungan TOPSIS dengan tabel ranking
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class ResultsFrame:
    def __init__(self, parent, db_manager, topsis_calculator):
        """Initialize results frame"""
        self.parent = parent
        self.db_manager = db_manager
        self.topsis_calculator = topsis_calculator
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        self.create_widgets()
        self.load_results()
    
    def create_widgets(self):
        """Create results display widgets"""
        # Title and actions
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="Hasil Perhitungan TOPSIS", 
                 style='Title.TLabel').pack(side='left')
        
        # Action buttons
        button_frame = ttk.Frame(title_frame)
        button_frame.pack(side='right')
        
        ttk.Button(button_frame, text="🧮 Hitung Ulang", 
                  command=self.recalculate_topsis, style='Action.TButton').pack(side='left', padx=5)
        
        ttk.Button(button_frame, text="🔄 Refresh", 
                  command=self.load_results, style='Nav.TButton').pack(side='left', padx=5)
        
        ttk.Button(button_frame, text="📊 Export", 
                  command=self.export_results, style='Success.TButton').pack(side='left', padx=5)
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=2)
        main_container.grid_rowconfigure(1, weight=1)
        main_container.grid_columnconfigure(0, weight=1)
        
        # Top section - Results table
        self.create_results_table(main_container)
        
        # Bottom section - Charts and details
        self.create_analysis_section(main_container)
    
    def create_results_table(self, parent):
        """Create results table"""
        table_frame = ttk.LabelFrame(parent, text="Ranking Karyawan", padding=10)
        table_frame.grid(row=0, column=0, sticky='nsew', pady=(0, 10))
        
        # Configure grid
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview
        columns = ('Rank', 'Nama', 'NIP', 'Posisi', 'Score', 'Status', 'Tanggal')
        self.results_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)
        
        # Configure columns
        column_widths = {
            'Rank': 60, 'Nama': 150, 'NIP': 100, 'Posisi': 120, 
            'Score': 80, 'Status': 150, 'Tanggal': 130
        }
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Bind events
        self.results_tree.bind('<Double-1>', self.on_item_double_click)
        self.results_tree.bind('<Button-3>', self.on_right_click)
        
        # Context menu
        self.create_context_menu()
        
        # Status info
        self.status_var = tk.StringVar()
        status_label = ttk.Label(table_frame, textvariable=self.status_var, 
                               style='Info.TLabel')
        status_label.grid(row=2, column=0, columnspan=2, pady=5)
    
    def create_analysis_section(self, parent):
        """Create analysis section with charts"""
        analysis_frame = ttk.Frame(parent)
        analysis_frame.grid(row=1, column=0, sticky='nsew')
        
        # Configure grid
        analysis_frame.grid_rowconfigure(0, weight=1)
        analysis_frame.grid_columnconfigure(0, weight=1)
        analysis_frame.grid_columnconfigure(1, weight=1)
        
        # Left chart - Score distribution
        self.create_score_chart(analysis_frame)
        
        # Right panel - Statistics and details
        self.create_statistics_panel(analysis_frame)
    
    def create_score_chart(self, parent):
        """Create score distribution chart"""
        chart_frame = ttk.LabelFrame(parent, text="Distribusi Skor TOPSIS", padding=10)
        chart_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 5))
        
        # Configure grid
        chart_frame.grid_rowconfigure(0, weight=1)
        chart_frame.grid_columnconfigure(0, weight=1)
        
        # Create matplotlib figure
        plt.style.use('default')
        self.score_fig, self.score_ax = plt.subplots(figsize=(6, 4))
        self.score_canvas = FigureCanvasTkAgg(self.score_fig, chart_frame)
        self.score_canvas.get_tk_widget().grid(row=0, column=0, sticky='nsew')
        
        # Initial empty chart
        self.update_score_chart()
    
    def create_statistics_panel(self, parent):
        """Create statistics panel"""
        stats_frame = ttk.LabelFrame(parent, text="Statistik Hasil", padding=15)
        stats_frame.grid(row=0, column=1, sticky='nsew', padx=(5, 0))
        
        # Statistics display
        self.stats_text = tk.Text(stats_frame, height=10, width=40, wrap='word', 
                                 font=('Consolas', 10), state='disabled')
        
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient='vertical', command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.grid(row=0, column=0, sticky='nsew')
        stats_scrollbar.grid(row=0, column=1, sticky='ns')
        
        # Configure grid
        stats_frame.grid_rowconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(0, weight=1)
        
        # Action buttons
        action_frame = ttk.Frame(stats_frame)
        action_frame.grid(row=1, column=0, columnspan=2, pady=10)
        
        ttk.Button(action_frame, text="📋 Detail Perhitungan", 
                  command=self.show_calculation_details, style='Action.TButton').pack(side='left', padx=5)
        
        ttk.Button(action_frame, text="📈 Analisis Lanjut", 
                  command=self.show_advanced_analysis, style='Nav.TButton').pack(side='left', padx=5)
    
    def create_context_menu(self):
        """Create right-click context menu"""
        self.context_menu = tk.Menu(self.results_tree, tearoff=0)
        self.context_menu.add_command(label="Lihat Detail", command=self.view_employee_detail)
        self.context_menu.add_command(label="Bandingkan", command=self.compare_employees)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Export Individu", command=self.export_individual)
    
    def load_results(self):
        """Load TOPSIS results"""
        try:
            # Clear existing items
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
            
            # Load results from database
            results = self.db_manager.get_topsis_results()
            
            if not results:
                self.status_var.set("Belum ada hasil TOPSIS. Silakan hitung terlebih dahulu.")
                self.update_statistics([])
                self.update_score_chart()
                return
            
            # Populate table
            for result in results:
                # Format tanggal
                tanggal = result.get('calculated_at', '')
                if tanggal:
                    try:
                        dt = datetime.fromisoformat(tanggal.replace('Z', '+00:00'))
                        tanggal = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        pass
                
                # Color coding based on status
                item_id = self.results_tree.insert('', 'end', values=(
                    result.get('ranking', ''),
                    result.get('nama', ''),
                    result.get('nip', ''),
                    result.get('posisi', ''),
                    f"{result.get('score', 0):.4f}",
                    result.get('status', ''),
                    tanggal
                ))
                
                # Apply color based on status
                status = result.get('status', '')
                if 'Sangat Direkomendasikan' in status:
                    self.results_tree.set(item_id, 'Status', '🟢 ' + status)
                elif 'Direkomendasikan' in status:
                    self.results_tree.set(item_id, 'Status', '🔵 ' + status)
                elif 'Pertimbangan' in status:
                    self.results_tree.set(item_id, 'Status', '🟡 ' + status)
                else:
                    self.results_tree.set(item_id, 'Status', '🔴 ' + status)
            
            # Update status
            self.status_var.set(f"Menampilkan {len(results)} hasil perhitungan TOPSIS")
            
            # Update statistics and chart
            self.update_statistics(results)
            self.update_score_chart()
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat hasil: {str(e)}")
            self.status_var.set("Error memuat data")
    
    def update_statistics(self, results):
        """Update statistics display"""
        try:
            self.stats_text.configure(state='normal')
            self.stats_text.delete(1.0, tk.END)
            
            if not results:
                self.stats_text.insert(tk.END, "Tidak ada data untuk ditampilkan")
                self.stats_text.configure(state='disabled')
                return
            
            # Calculate statistics
            scores = [r.get('score', 0) for r in results]
            
            stats_text = f"""STATISTIK HASIL TOPSIS
{'='*30}

Total Karyawan: {len(results)}

SKOR TOPSIS:
• Tertinggi: {max(scores):.4f}
• Terendah: {min(scores):.4f}
• Rata-rata: {sum(scores)/len(scores):.4f}
• Median: {sorted(scores)[len(scores)//2]:.4f}

DISTRIBUSI STATUS:
"""
            
            # Count status distribution
            status_counts = {}
            for result in results:
                status = result.get('status', 'Unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            for status, count in status_counts.items():
                percentage = (count / len(results)) * 100
                stats_text += f"• {status}: {count} ({percentage:.1f}%)\n"
            
            # Top 3 performers
            stats_text += f"\nTOP 3 KARYAWAN:\n"
            for i, result in enumerate(results[:3], 1):
                stats_text += f"{i}. {result.get('nama', '')} ({result.get('score', 0):.4f})\n"
            
            # Criteria analysis
            stats_text += f"\nINFORMASI KRITERIA:\n"
            criteria = self.db_manager.get_all_criteria()
            for criterion in criteria:
                stats_text += f"• {criterion['kode']}: {criterion['nama']} ({criterion['bobot']*100:.1f}%)\n"
            
            self.stats_text.insert(tk.END, stats_text)
            self.stats_text.configure(state='disabled')
            
        except Exception as e:
            print(f"Error updating statistics: {e}")
    
    def update_score_chart(self):
        """Update score distribution chart"""
        try:
            self.score_ax.clear()
            
            results = self.db_manager.get_topsis_results()
            
            if not results:
                self.score_ax.text(0.5, 0.5, 'Belum ada data hasil\nSilakan hitung TOPSIS terlebih dahulu', 
                                 ha='center', va='center', transform=self.score_ax.transAxes,
                                 fontsize=12, style='italic')
            else:
                # Get scores and names
                scores = [r.get('score', 0) for r in results]
                names = [r.get('nama', '')[:10] + '...' if len(r.get('nama', '')) > 10 
                        else r.get('nama', '') for r in results]
                
                # Create bar chart
                bars = self.score_ax.bar(range(len(scores)), scores, 
                                       color=['#2ECC71' if s >= 0.7 else '#3498DB' if s >= 0.5 
                                             else '#F39C12' if s >= 0.3 else '#E74C3C' for s in scores])
                
                # Customize chart
                self.score_ax.set_xlabel('Karyawan')
                self.score_ax.set_ylabel('Skor TOPSIS')
                self.score_ax.set_title('Distribusi Skor TOPSIS', fontweight='bold')
                self.score_ax.set_xticks(range(len(names)))
                self.score_ax.set_xticklabels(names, rotation=45, ha='right', fontsize=8)
                
                # Add value labels on bars
                for bar, score in zip(bars, scores):
                    height = bar.get_height()
                    self.score_ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                                     f'{score:.3f}', ha='center', va='bottom', fontsize=8)
                
                # Add threshold lines
                self.score_ax.axhline(y=0.7, color='green', linestyle='--', alpha=0.7, label='Sangat Direkomendasikan')
                self.score_ax.axhline(y=0.5, color='blue', linestyle='--', alpha=0.7, label='Direkomendasikan')
                self.score_ax.axhline(y=0.3, color='orange', linestyle='--', alpha=0.7, label='Perlu Pertimbangan')
                
                self.score_ax.legend(fontsize=8)
            
            plt.tight_layout()
            self.score_canvas.draw()
            
        except Exception as e:
            print(f"Error updating score chart: {e}")
    
    def recalculate_topsis(self):
        """Recalculate TOPSIS"""
        try:
            # Get employees and criteria
            employees = self.db_manager.get_all_employees()
            criteria = self.db_manager.get_all_criteria()
            
            if not employees:
                messagebox.showwarning("Peringatan", "Tidak ada data karyawan untuk dihitung")
                return
            
            if len(employees) < 2:
                messagebox.showwarning("Peringatan", "Minimal 2 karyawan diperlukan untuk perhitungan TOPSIS")
                return
            
            # Confirm recalculation
            if not messagebox.askyesno("Konfirmasi", 
                                     f"Yakin ingin menghitung ulang TOPSIS untuk {len(employees)} karyawan?\n\nHasil sebelumnya akan ditimpa."):
                return
            
            # Prepare criteria weights
            criteria_weights = {c['kode']: c['bobot'] for c in criteria}
            
            # Validate data
            is_valid, message = self.topsis_calculator.validate_data(employees, criteria_weights)
            if not is_valid:
                messagebox.showerror("Error Validasi", message)
                return
            
            # Calculate TOPSIS
            results = self.topsis_calculator.calculate_topsis(employees, criteria_weights)
            
            # Save results
            session_name = f"Perhitungan {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            success = self.db_manager.save_topsis_results(results, session_name)
            
            if success:
                messagebox.showinfo("Sukses", f"Perhitungan TOPSIS berhasil!\n\nTotal karyawan: {len(results)}\nSesi: {session_name}")
                self.load_results()
            else:
                messagebox.showerror("Error", "Gagal menyimpan hasil perhitungan")
                
        except Exception as e:
            messagebox.showerror("Error", f"Terjadi kesalahan saat perhitungan: {str(e)}")
    
    def export_results(self):
        """Export results to file"""
        # This will be implemented in export frame
        main_window = self.get_main_window()
        if main_window:
            main_window.show_export()
    
    def on_item_double_click(self, event):
        """Handle double click on table item"""
        self.view_employee_detail()
    
    def on_right_click(self, event):
        """Handle right click on table"""
        item = self.results_tree.identify_row(event.y)
        if item:
            self.results_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def view_employee_detail(self):
        """View detailed employee information"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih karyawan untuk melihat detail!")
            return
        
        item = selection[0]
        values = self.results_tree.item(item, 'values')
        
        detail_text = f"""
DETAIL HASIL TOPSIS

Ranking: #{values[0]}
Nama: {values[1]}
NIP: {values[2]}
Posisi: {values[3]}
Skor TOPSIS: {values[4]}
Status: {values[5].replace('🟢 ', '').replace('🔵 ', '').replace('🟡 ', '').replace('🔴 ', '')}
Tanggal Perhitungan: {values[6]}

Interpretasi Skor:
• 0.7 - 1.0: Sangat Direkomendasikan
• 0.5 - 0.7: Direkomendasikan  
• 0.3 - 0.5: Perlu Pertimbangan
• 0.0 - 0.3: Tidak Direkomendasikan
        """
        
        messagebox.showinfo("Detail Karyawan", detail_text.strip())
    
    def compare_employees(self):
        """Compare selected employees"""
        messagebox.showinfo("Info", "Fitur perbandingan akan tersedia dalam versi mendatang")
    
    def export_individual(self):
        """Export individual employee data"""
        messagebox.showinfo("Info", "Fitur export individu akan tersedia dalam versi mendatang")
    
    def show_calculation_details(self):
        """Show detailed calculation steps"""
        messagebox.showinfo("Info", "Fitur detail perhitungan akan tersedia dalam versi mendatang")
    
    def show_advanced_analysis(self):
        """Show advanced analysis"""
        messagebox.showinfo("Info", "Fitur analisis lanjut akan tersedia dalam versi mendatang")
    
    def get_main_window(self):
        """Get main window instance"""
        widget = self.parent
        while widget:
            if hasattr(widget, 'show_export'):
                return widget
            widget = widget.master
        return None
