# 🚀 QUICK START - Database SPK TOPSIS

## 📁 File Penting (4 file saja!)

```
database/
├── 📖 README_DATABASE.md              # Dokumentasi lengkap
├── 🗄️ SPK_TOPSIS_DATABASE_COMPLETE.sql # File SQL utama ⭐
├── 🐍 setup_database.py               # Setup otomatis
└── 👁️ view_database.py                # Lihat isi database
```

---

## ⚡ Setup Cepat (30 detik!)

### **Metode 1: SQL File (Tercepat)**
```bash
sqlite3 spk_karyawan_enhanced.db < database/SPK_TOPSIS_DATABASE_COMPLETE.sql
```

### **Metode 2: <PERSON>**
```bash
cd database
python setup_database.py
```

---

## 🔍 Lihat Database

```bash
cd database
python view_database.py
```

---

## 📊 Yang Didapat

✅ **Database lengkap** dengan 6 tabel + 4 views  
✅ **Users**: admin/admin123, operator/operator123  
✅ **5 Kriteria** dengan bobot total 100%  
✅ **3 Karyawan** dengan data lengkap  
✅ **15 Evaluasi** siap untuk TOPSIS  

---

## 🎯 Langkah Selanjutnya

1. **Setup database** (pilih salah satu metode di atas)
2. **Jalankan aplikasi** SPK TOPSIS
3. **Login** dengan admin/admin123
4. **Hitung TOPSIS** untuk melihat ranking

---

**© 2024 Muhammad Bayu Prasetyo Wibowo - SPK TOPSIS Database**
