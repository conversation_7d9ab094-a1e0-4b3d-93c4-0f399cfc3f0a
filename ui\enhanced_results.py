"""
Enhanced Results untuk SPK Karyawan TOPSIS
Menampilkan hasil perhitungan TOPSIS dengan fitur enhanced
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class EnhancedResults:
    def __init__(self, parent, db_manager):
        """Initialize enhanced results"""
        self.parent = parent
        self.db_manager = db_manager
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        self.create_widgets()
        self.load_results()
    
    def create_widgets(self):
        """Create results widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="🏆 Hasil TOPSIS", 
                 style='Title.TLabel').pack(side='left')
        
        ttk.Button(title_frame, text="🔄 Refresh", 
                  command=self.load_results, style='Nav.TButton').pack(side='right', padx=(10, 0))
        
        ttk.Button(title_frame, text="📤 Export", 
                  command=self.export_results, style='Action.TButton').pack(side='right')
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=2)
        main_container.grid_columnconfigure(1, weight=1)
        
        # Left side - Results table
        self.create_results_table(main_container)
        
        # Right side - Details
        self.create_details_panel(main_container)
    
    def create_results_table(self, parent):
        """Create results table"""
        table_frame = ttk.LabelFrame(parent, text="📊 Ranking TOPSIS", padding=10)
        table_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))
        
        # Configure grid
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview
        columns = ('Rank', 'Kode', 'Nama', 'Posisi', 'Score', 'Status')
        self.results_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {
            'Rank': 60, 'Kode': 80, 'Nama': 200, 
            'Posisi': 150, 'Score': 100, 'Status': 150
        }
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Bind events
        self.results_tree.bind('<<TreeviewSelect>>', self.on_selection_change)
        
        # Status
        self.status_var = tk.StringVar()
        status_label = ttk.Label(table_frame, textvariable=self.status_var, 
                               style='Info.TLabel')
        status_label.grid(row=2, column=0, columnspan=2, pady=5)
    
    def create_details_panel(self, parent):
        """Create details panel"""
        details_frame = ttk.LabelFrame(parent, text="📋 Detail Alternatif", padding=15)
        details_frame.grid(row=0, column=1, sticky='nsew')
        
        # Configure grid
        details_frame.grid_rowconfigure(0, weight=1)
        details_frame.grid_columnconfigure(0, weight=1)
        
        # Details text
        self.details_text = tk.Text(details_frame, wrap='word', font=('Consolas', 10), 
                                   state='disabled', height=20)
        
        details_scrollbar = ttk.Scrollbar(details_frame, orient='vertical', command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scrollbar.set)
        
        # Grid layout
        self.details_text.grid(row=0, column=0, sticky='nsew')
        details_scrollbar.grid(row=0, column=1, sticky='ns')
        
        # Summary info
        summary_frame = ttk.LabelFrame(details_frame, text="📈 Ringkasan", padding=10)
        summary_frame.grid(row=1, column=0, columnspan=2, sticky='ew', pady=(10, 0))
        
        self.summary_var = tk.StringVar()
        summary_label = ttk.Label(summary_frame, textvariable=self.summary_var, 
                                style='Info.TLabel', justify='left')
        summary_label.pack(anchor='w')
    
    def load_results(self):
        """Load TOPSIS results"""
        try:
            # Clear existing items
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
            
            # Load results from database
            results = self.db_manager.get_latest_topsis_results()
            
            if not results:
                self.status_var.set("Belum ada hasil perhitungan TOPSIS")
                self.clear_details()
                return
            
            # Populate table
            for result in results:
                # Determine status based on score
                score = result.get('score', 0)
                if score >= 0.7:
                    status = "🟢 Sangat Direkomendasikan"
                elif score >= 0.5:
                    status = "🔵 Direkomendasikan"
                elif score >= 0.3:
                    status = "🟡 Perlu Pertimbangan"
                else:
                    status = "🔴 Tidak Direkomendasikan"
                
                # Add ranking prefix
                rank = result.get('ranking', 0)
                if rank == 1:
                    rank_display = "🥇 1"
                elif rank == 2:
                    rank_display = "🥈 2"
                elif rank == 3:
                    rank_display = "🥉 3"
                else:
                    rank_display = f"#{rank}"
                
                self.results_tree.insert('', 'end', values=(
                    rank_display,
                    result.get('kode', ''),
                    result.get('nama', ''),
                    result.get('posisi', ''),
                    f"{score:.4f}",
                    status
                ))
            
            self.status_var.set(f"Menampilkan {len(results)} hasil ranking")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat hasil: {str(e)}")
            self.status_var.set("Error memuat data")
    
    def on_selection_change(self, event):
        """Handle selection change"""
        selection = self.results_tree.selection()
        if selection:
            self.show_details(selection[0])
        else:
            self.clear_details()
    
    def show_details(self, item):
        """Show details for selected result"""
        try:
            values = self.results_tree.item(item, 'values')
            
            # Get result data
            results = self.db_manager.get_latest_topsis_results()
            if not results:
                return
            
            # Find the selected result
            rank_text = values[0]
            rank = int(rank_text.split()[-1]) if rank_text.split()[-1].isdigit() else int(rank_text[1:])
            
            result = next((r for r in results if r.get('ranking') == rank), None)
            if not result:
                return
            
            # Get criteria values for this alternative
            alternative_id = result.get('alternative_id')
            criteria_values = self.db_manager.get_alternative_criteria_values(alternative_id)
            criteria = self.db_manager.get_all_criteria()
            
            # Build details text
            details_text = f"""DETAIL ALTERNATIF TOPSIS
{'='*40}

INFORMASI UMUM:
Ranking: #{result.get('ranking', 0)}
Kode: {result.get('kode', '')}
Nama: {result.get('nama', '')}
Posisi: {result.get('posisi', '')}

HASIL TOPSIS:
Score: {result.get('score', 0):.6f}
Distance to Positive: {result.get('distance_positive', 0):.6f}
Distance to Negative: {result.get('distance_negative', 0):.6f}

NILAI KRITERIA:
{'='*40}
"""
            
            # Add criteria values
            for criterion in criteria:
                criteria_id = criterion['id']
                value = criteria_values.get(criteria_id, 0)
                jenis = criterion['jenis']
                bobot = criterion['bobot']
                
                details_text += f"{criterion['kode']} - {criterion['nama']:<20}: {value:>6.2f} ({jenis}, bobot: {bobot:.3f})\n"
            
            # Add interpretation
            score = result.get('score', 0)
            details_text += f"""
INTERPRETASI HASIL:
{'='*40}
Score TOPSIS: {score:.4f} ({score*100:.2f}%)

Status: """
            
            if score >= 0.7:
                details_text += "🟢 SANGAT DIREKOMENDASIKAN\n"
                details_text += "Alternatif ini sangat layak untuk dipilih.\n"
            elif score >= 0.5:
                details_text += "🔵 DIREKOMENDASIKAN\n"
                details_text += "Alternatif ini layak untuk dipertimbangkan.\n"
            elif score >= 0.3:
                details_text += "🟡 PERLU PERTIMBANGAN\n"
                details_text += "Alternatif ini memerlukan evaluasi lebih lanjut.\n"
            else:
                details_text += "🔴 TIDAK DIREKOMENDASIKAN\n"
                details_text += "Alternatif ini kurang layak untuk dipilih.\n"
            
            details_text += f"""
CATATAN:
- Score TOPSIS berkisar 0.0 - 1.0
- Semakin tinggi score, semakin baik
- Ranking berdasarkan score tertinggi
            """
            
            # Update details display
            self.details_text.configure(state='normal')
            self.details_text.delete(1.0, tk.END)
            self.details_text.insert(tk.END, details_text)
            self.details_text.configure(state='disabled')
            
            # Update summary
            summary_text = f"Rank #{rank} | Score: {score:.4f} | {result.get('nama', '')}"
            self.summary_var.set(summary_text)
            
        except Exception as e:
            print(f"Error showing details: {e}")
            self.clear_details()
    
    def clear_details(self):
        """Clear details display"""
        self.details_text.configure(state='normal')
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(tk.END, "Pilih alternatif untuk melihat detail")
        self.details_text.configure(state='disabled')
        
        self.summary_var.set("Tidak ada data dipilih")
    
    def export_results(self):
        """Export results to file - Super Simple and Robust"""
        try:
            # Check permission
            if not self.db_manager.has_permission('export_results'):
                messagebox.showerror("Access Denied",
                                   "Anda tidak memiliki permission untuk export hasil!\n\n"
                                   "Hanya Admin yang dapat melakukan export.")
                return

            # Get results data
            results = self.db_manager.get_latest_topsis_results()
            if not results:
                messagebox.showwarning("Peringatan", "Belum ada hasil TOPSIS untuk diekspor!")
                return

            # Ask for file location - ONLY CSV to avoid Excel issues
            file_path = filedialog.asksaveasfilename(
                title="Export TOPSIS Results",
                defaultextension=".csv",
                filetypes=[
                    ("CSV files", "*.csv"),
                    ("Text files", "*.txt"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return  # User cancelled

            # Always export as CSV for maximum compatibility
            self.robust_csv_export(file_path, results)

            messagebox.showinfo("✅ Export Berhasil",
                              f"Hasil TOPSIS berhasil diekspor ke:\n{file_path}\n\n"
                              f"Total data: {len(results)} alternatif\n\n"
                              f"💡 File CSV dapat dibuka dengan Excel")

        except Exception as e:
            messagebox.showerror("Error", f"Gagal export hasil: {str(e)}")
            import traceback
            print("Export Error Details:")
            traceback.print_exc()

    def robust_csv_export(self, file_path, results):
        """Super robust CSV export - guaranteed to work"""
        try:
            # Open file with basic text writing
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                # Write header
                header = "Rank,Kode,Nama_Karyawan,Posisi,TOPSIS_Score,Distance_Positive,Distance_Negative,Status_Rekomendasi,Session,Tanggal_Perhitungan\n"
                f.write(header)

                # Write data rows
                for i, result in enumerate(results, 1):
                    score = result.get('score', 0)

                    # Determine status
                    if score >= 0.7:
                        status = "Sangat Direkomendasikan"
                    elif score >= 0.5:
                        status = "Direkomendasikan"
                    elif score >= 0.3:
                        status = "Perlu Pertimbangan"
                    else:
                        status = "Tidak Direkomendasikan"

                    # Clean data to avoid CSV issues - use correct field names from JOIN
                    kode = str(result.get('kode', '')).replace(',', ';')
                    nama = str(result.get('nama', '')).replace(',', ';')
                    posisi = str(result.get('posisi', '')).replace(',', ';')
                    session = str(result.get('session_name', '')).replace(',', ';')
                    tanggal = str(result.get('calculated_at', '')).replace(',', ';')

                    # Format numbers
                    score_str = f"{score:.4f}"
                    dist_pos = f"{result.get('distance_positive', 0):.4f}"
                    dist_neg = f"{result.get('distance_negative', 0):.4f}"

                    # Write row
                    row = f"{i},{kode},{nama},{posisi},{score_str},{dist_pos},{dist_neg},{status},{session},{tanggal}\n"
                    f.write(row)

            print(f"✅ CSV export successful: {file_path}")

        except Exception as e:
            # If even this fails, try the most basic approach
            try:
                self.emergency_text_export(file_path, results)
            except:
                raise Exception(f"All export methods failed: {str(e)}")

    def emergency_text_export(self, file_path, results):
        """Emergency text export if everything else fails"""
        try:
            # Change extension to .txt if needed
            if file_path.endswith('.csv'):
                file_path = file_path.replace('.csv', '.txt')

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("HASIL RANKING TOPSIS\n")
                f.write("=" * 50 + "\n\n")

                for i, result in enumerate(results, 1):
                    score = result.get('score', 0)

                    if score >= 0.7:
                        status = "Sangat Direkomendasikan"
                    elif score >= 0.5:
                        status = "Direkomendasikan"
                    elif score >= 0.3:
                        status = "Perlu Pertimbangan"
                    else:
                        status = "Tidak Direkomendasikan"

                    f.write(f"Rank {i}:\n")
                    f.write(f"  Kode: {result.get('kode', '')}\n")
                    f.write(f"  Nama: {result.get('nama', '')}\n")
                    f.write(f"  Posisi: {result.get('posisi', '')}\n")
                    f.write(f"  TOPSIS Score: {score:.4f}\n")
                    f.write(f"  Status: {status}\n")
                    f.write(f"  Distance+: {result.get('distance_positive', 0):.4f}\n")
                    f.write(f"  Distance-: {result.get('distance_negative', 0):.4f}\n")
                    f.write(f"  Session: {result.get('session_name', '')}\n")
                    f.write(f"  Tanggal: {result.get('calculated_at', '')}\n")
                    f.write("-" * 30 + "\n\n")

                f.write(f"\nDiekspor pada: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("Muhammad Bayu Prasetyo Wibowo - 211011450583\n")

            print(f"✅ Emergency text export successful: {file_path}")

        except Exception as e:
            raise Exception(f"Emergency export failed: {str(e)}")


