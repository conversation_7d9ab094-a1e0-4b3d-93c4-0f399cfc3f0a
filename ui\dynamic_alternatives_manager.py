"""
Dynamic Alternatives Manager
Mengel<PERSON> alternatif (<PERSON><PERSON><PERSON>) secara dinamis dengan CRUD operations
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class DynamicAlternativesManager:
    def __init__(self, parent, db_manager):
        """Initialize dynamic alternatives manager"""
        self.parent = parent
        self.db_manager = db_manager
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        self.current_alternative_id = None
        self.criteria_entries = {}
        
        self.create_widgets()
        self.load_alternatives()
    
    def create_widgets(self):
        """Create alternatives manager widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="👥 Kelola Alternatif (Karyawan)", 
                 style='Title.TLabel').pack(side='left')
        
        # Show add button based on permission
        if self.db_manager.has_permission('add_alternative'):
            ttk.Button(title_frame, text="➕ Tambah Alternatif",
                      command=self.add_alternative, style='Nav.TButton').pack(side='right')
        else:
            ttk.Label(title_frame, text="👁️ View Only",
                     style='Subtitle.TLabel').pack(side='right')
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=1)
        main_container.grid_columnconfigure(1, weight=1)
        
        # Left side - Alternatives table
        self.create_alternatives_table(main_container)
        
        # Right side - Form
        self.create_alternatives_form(main_container)
    
    def create_alternatives_table(self, parent):
        """Create alternatives table"""
        table_frame = ttk.LabelFrame(parent, text="📋 Daftar Alternatif", padding=10)
        table_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))
        
        # Configure grid
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview
        columns = ('ID', 'Kode', 'Nama', 'Posisi', 'Deskripsi')
        self.alternatives_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {'ID': 50, 'Kode': 80, 'Nama': 200, 'Posisi': 150, 'Deskripsi': 200}
        
        for col in columns:
            self.alternatives_tree.heading(col, text=col)
            self.alternatives_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.alternatives_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.alternatives_tree.xview)
        self.alternatives_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.alternatives_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Bind events
        self.alternatives_tree.bind('<<TreeviewSelect>>', self.on_alternative_select)
        self.alternatives_tree.bind('<Double-1>', self.edit_alternative)
        
        # Context menu
        self.create_context_menu()
        
        # Status
        self.status_var = tk.StringVar()
        status_label = ttk.Label(table_frame, textvariable=self.status_var, 
                               style='Info.TLabel')
        status_label.grid(row=2, column=0, columnspan=2, pady=5)
    
    def create_context_menu(self):
        """Create context menu for alternatives table"""
        self.context_menu = tk.Menu(self.alternatives_tree, tearoff=0)
        self.context_menu.add_command(label="✏️ Edit", command=self.edit_alternative)
        self.context_menu.add_command(label="📊 Input Nilai", command=self.input_criteria_values)
        self.context_menu.add_command(label="🗑️ Hapus", command=self.delete_alternative)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🔄 Refresh", command=self.load_alternatives)
        
        def show_context_menu(event):
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()
        
        self.alternatives_tree.bind("<Button-3>", show_context_menu)
    
    def create_alternatives_form(self, parent):
        """Create alternatives form"""
        form_frame = ttk.LabelFrame(parent, text="📝 Form Alternatif", padding=15)
        form_frame.grid(row=0, column=1, sticky='nsew')
        
        # Basic info form
        self.create_basic_form(form_frame)
        
        # Criteria values form
        self.create_criteria_form(form_frame)
        
        # Buttons
        self.create_form_buttons(form_frame)
    
    def create_basic_form(self, parent):
        """Create basic information form"""
        basic_frame = ttk.LabelFrame(parent, text="ℹ️ Informasi Dasar", padding=10)
        basic_frame.pack(fill='x', pady=(0, 10))
        
        # Kode
        ttk.Label(basic_frame, text="🔤 Kode:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.kode_var = tk.StringVar()
        self.kode_entry = ttk.Entry(basic_frame, textvariable=self.kode_var, width=30)
        self.kode_entry.pack(fill='x', pady=(0, 10))
        
        # Nama
        ttk.Label(basic_frame, text="👤 Nama:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.nama_var = tk.StringVar()
        self.nama_entry = ttk.Entry(basic_frame, textvariable=self.nama_var, width=30)
        self.nama_entry.pack(fill='x', pady=(0, 10))
        
        # Posisi
        ttk.Label(basic_frame, text="💼 Posisi:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.posisi_var = tk.StringVar()
        posisi_options = [
            "Staff Produksi", "Staff Administrasi", "Staff Keuangan",
            "Staff Marketing", "Staff IT", "Staff HRD", "Staff Logistik",
            "Staff Quality Control", "Staff Maintenance"
        ]
        self.posisi_combo = ttk.Combobox(basic_frame, textvariable=self.posisi_var, 
                                        values=posisi_options, state='readonly', width=27)
        self.posisi_combo.pack(fill='x', pady=(0, 10))
        
        # Deskripsi
        ttk.Label(basic_frame, text="📄 Deskripsi:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.deskripsi_text = tk.Text(basic_frame, height=3, width=30, wrap='word')
        self.deskripsi_text.pack(fill='x', pady=(0, 5))
    
    def create_criteria_form(self, parent):
        """Create criteria values form"""
        self.criteria_frame = ttk.LabelFrame(parent, text="📊 Nilai Kriteria", padding=10)
        self.criteria_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # This will be populated dynamically based on available criteria
        self.refresh_criteria_form()
    
    def refresh_criteria_form(self):
        """Refresh criteria form based on current criteria"""
        # Clear existing widgets
        for widget in self.criteria_frame.winfo_children():
            widget.destroy()
        
        self.criteria_entries = {}
        
        try:
            criteria = self.db_manager.get_all_criteria()
            
            if not criteria:
                ttk.Label(self.criteria_frame, text="⚠️ Belum ada kriteria yang didefinisikan",
                         style='Warning.TLabel').pack(pady=20)
                return
            
            # Create scrollable frame for criteria
            canvas = tk.Canvas(self.criteria_frame, height=200)
            scrollbar = ttk.Scrollbar(self.criteria_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)
            
            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            
            # Add criteria entries
            for i, criterion in enumerate(criteria):
                criterion_frame = ttk.Frame(scrollable_frame)
                criterion_frame.pack(fill='x', pady=2)
                
                # Label
                label_text = f"{criterion['kode']} - {criterion['nama']}"
                if criterion['jenis'] == 'cost':
                    label_text += " (Cost)"
                
                ttk.Label(criterion_frame, text=label_text, 
                         font=('Segoe UI', 9)).pack(side='left', anchor='w')
                
                # Entry
                var = tk.DoubleVar()
                entry = ttk.Entry(criterion_frame, textvariable=var, width=10)
                entry.pack(side='right')
                
                # Range info
                range_text = f"({criterion.get('min_value', 1)}-{criterion.get('max_value', 15)})"
                ttk.Label(criterion_frame, text=range_text, 
                         font=('Segoe UI', 8), foreground='gray').pack(side='right', padx=(0, 5))
                
                self.criteria_entries[criterion['id']] = {
                    'var': var,
                    'entry': entry,
                    'criterion': criterion
                }
            
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
            
        except Exception as e:
            ttk.Label(self.criteria_frame, text=f"❌ Error: {str(e)}",
                     style='Error.TLabel').pack(pady=20)
    
    def create_form_buttons(self, parent):
        """Create form buttons"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill='x', pady=10)
        
        self.save_btn = ttk.Button(button_frame, text="💾 Simpan", 
                                  command=self.save_alternative, style='Action.TButton')
        self.save_btn.pack(side='left', padx=(0, 5))
        
        self.clear_btn = ttk.Button(button_frame, text="🗑️ Clear", 
                                   command=self.clear_form, style='Nav.TButton')
        self.clear_btn.pack(side='left', padx=5)
        
        self.refresh_criteria_btn = ttk.Button(button_frame, text="🔄 Refresh Kriteria", 
                                              command=self.refresh_criteria_form, style='Nav.TButton')
        self.refresh_criteria_btn.pack(side='left', padx=5)
    
    def load_alternatives(self):
        """Load alternatives data"""
        try:
            # Clear existing items
            for item in self.alternatives_tree.get_children():
                self.alternatives_tree.delete(item)
            
            # Load alternatives from database
            alternatives = self.db_manager.get_all_alternatives()
            
            if not alternatives:
                self.status_var.set("Belum ada alternatif")
                return
            
            # Populate table
            for alternative in alternatives:
                self.alternatives_tree.insert('', 'end', values=(
                    alternative['id'],
                    alternative['kode'],
                    alternative['nama'],
                    alternative['posisi'],
                    alternative.get('deskripsi', '')[:50] + '...' if len(alternative.get('deskripsi', '')) > 50 else alternative.get('deskripsi', '')
                ))
            
            self.status_var.set(f"Menampilkan {len(alternatives)} alternatif")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat alternatif: {str(e)}")
            self.status_var.set("Error memuat data")
    
    def on_alternative_select(self, event):
        """Handle alternative selection"""
        selection = self.alternatives_tree.selection()
        if selection:
            self.load_alternative_to_form(selection[0])
    
    def load_alternative_to_form(self, item):
        """Load selected alternative to form"""
        try:
            values = self.alternatives_tree.item(item, 'values')
            alternative_id = int(values[0])
            
            # Get full alternative data
            alternatives = self.db_manager.get_all_alternatives()
            alternative = next((a for a in alternatives if a['id'] == alternative_id), None)
            
            if alternative:
                self.current_alternative_id = alternative_id
                self.kode_var.set(alternative['kode'])
                self.nama_var.set(alternative['nama'])
                self.posisi_var.set(alternative['posisi'])
                
                self.deskripsi_text.delete(1.0, tk.END)
                self.deskripsi_text.insert(1.0, alternative.get('deskripsi', ''))
                
                # Load criteria values
                criteria_values = self.db_manager.get_alternative_criteria_values(alternative_id)
                for criteria_id, entry_data in self.criteria_entries.items():
                    value = criteria_values.get(criteria_id, 0.0)
                    entry_data['var'].set(value)
                
                self.save_btn.configure(text="💾 Update")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat data alternatif: {str(e)}")
    
    def clear_form(self):
        """Clear form fields"""
        self.current_alternative_id = None
        self.kode_var.set('')
        self.nama_var.set('')
        self.posisi_var.set('')
        self.deskripsi_text.delete(1.0, tk.END)
        
        # Clear criteria values
        for entry_data in self.criteria_entries.values():
            entry_data['var'].set(0.0)
        
        self.save_btn.configure(text="💾 Simpan")
    
    def validate_form(self) -> bool:
        """Validate form input with comprehensive checks"""
        try:
            # Import validation utility
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils'))
            from error_handler import validate_input

            # Validate kode alternatif
            kode = self.kode_var.get().strip()
            validate_input(kode, 'Kode Alternatif', str)
            if len(kode) < 2:
                raise ValueError("Kode alternatif minimal 2 karakter!")
            if not kode.replace('_', '').replace('-', '').isalnum():
                raise ValueError("Kode alternatif hanya boleh mengandung huruf, angka, underscore, dan dash!")

            # Validate nama alternatif
            nama = self.nama_var.get().strip()
            validate_input(nama, 'Nama Alternatif', str)
            if len(nama) < 3:
                raise ValueError("Nama alternatif minimal 3 karakter!")

            # Validate posisi
            posisi = self.posisi_var.get().strip()
            validate_input(posisi, 'Posisi', str)
            if len(posisi) < 3:
                raise ValueError("Posisi minimal 3 karakter!")

            # Validate criteria values
            for criteria_id, entry_data in self.criteria_entries.items():
                value = entry_data['var'].get()
                criterion = entry_data['criterion']
                min_val = criterion.get('min_value', 1.0)
                max_val = criterion.get('max_value', 15.0)

                validate_input(value, f"Nilai {criterion['nama']}", float, min_val, max_val)

                # Additional validation for specific criteria types
                if criterion.get('jenis') == 'cost' and value > (max_val * 0.8):
                    import tkinter.messagebox as mb
                    result = mb.askyesno("⚠️ Konfirmasi",
                                       f"Nilai {criterion['nama']} cukup tinggi ({value}) untuk kriteria cost.\n\n"
                                       f"Apakah Anda yakin ingin melanjutkan?")
                    if not result:
                        return False

            return True

        except ValueError as e:
            messagebox.showerror("❌ Data Tidak Valid", str(e))
            return False
        except Exception as e:
            messagebox.showerror("❌ Error Validasi", f"Terjadi kesalahan validasi: {str(e)}")
            return False

    def save_alternative(self):
        """Save alternative"""
        if not self.validate_form():
            return

        try:
            alternative_data = {
                'kode': self.kode_var.get().strip(),
                'nama': self.nama_var.get().strip(),
                'posisi': self.posisi_var.get().strip(),
                'deskripsi': self.deskripsi_text.get(1.0, tk.END).strip()
            }

            if self.current_alternative_id:
                # Update existing alternative
                success = self.db_manager.update_alternative(self.current_alternative_id, alternative_data)
                alternative_id = self.current_alternative_id
                action = "diupdate"
            else:
                # Add new alternative
                alternative_id = self.db_manager.add_alternative(alternative_data)
                success = alternative_id is not None
                action = "ditambahkan"

            if success:
                # Save criteria values
                for criteria_id, entry_data in self.criteria_entries.items():
                    value = entry_data['var'].get()
                    self.db_manager.set_alternative_criteria_value(alternative_id, criteria_id, value)

                messagebox.showinfo("✅ Berhasil", f"Alternatif berhasil {action}!")
                self.clear_form()
                self.load_alternatives()
            else:
                messagebox.showerror("❌ Gagal", f"Gagal {action.replace('di', 'men')} alternatif!")

        except Exception as e:
            messagebox.showerror("Error", f"Error menyimpan alternatif: {str(e)}")

    def add_alternative(self):
        """Add new alternative"""
        self.clear_form()
        self.kode_entry.focus()

    def edit_alternative(self):
        """Edit selected alternative"""
        selection = self.alternatives_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih alternatif yang akan diedit!")
            return

        self.load_alternative_to_form(selection[0])

    def delete_alternative(self):
        """Delete selected alternative"""
        selection = self.alternatives_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih alternatif yang akan dihapus!")
            return

        values = self.alternatives_tree.item(selection[0], 'values')
        alternative_name = values[2]

        if messagebox.askyesno("Konfirmasi",
                              f"Yakin ingin menghapus alternatif '{alternative_name}'?\n\n"
                              f"Semua data nilai kriteria juga akan terhapus!\n"
                              f"Tindakan ini tidak dapat dibatalkan!"):
            try:
                alternative_id = int(values[0])
                success = self.db_manager.delete_alternative(alternative_id)

                if success:
                    messagebox.showinfo("✅ Berhasil", "Alternatif berhasil dihapus!")
                    self.clear_form()
                    self.load_alternatives()
                else:
                    messagebox.showerror("❌ Gagal", "Gagal menghapus alternatif!")

            except Exception as e:
                messagebox.showerror("Error", f"Error menghapus alternatif: {str(e)}")

    def input_criteria_values(self):
        """Input criteria values for selected alternative"""
        selection = self.alternatives_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih alternatif untuk input nilai!")
            return

        self.load_alternative_to_form(selection[0])

        # Focus on first criteria entry
        if self.criteria_entries:
            first_entry = list(self.criteria_entries.values())[0]['entry']
            first_entry.focus()
