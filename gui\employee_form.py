import tkinter as tk
from tkinter import ttk, messagebox
from typing import Optional
import sys
import os

# Tambahkan path parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import DatabaseManager
from models import Employee, POSISI_OPTIONS

class EmployeeForm:
    def __init__(self, parent, db_manager: DatabaseManager, employee_id: Optional[int] = None):
        self.parent = parent
        self.db_manager = db_manager
        self.employee_id = employee_id
        self.employee = None
        
        # Create window with modern styling
        self.window = tk.Toplevel(parent)
        title = "➕ Tambah Karyawan Baru" if employee_id is None else "✏️ Edit Data Karyawan"
        self.window.title(title)
        self.window.geometry("650x750")  # Lebih lebar dan tinggi
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.grab_set()
        self.window.configure(bg='#ffffff')  # Background putih bersih

        # Configure close protocol
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Setup modern styles
        self.setup_styles()
        
        # Load employee data if editing
        if employee_id:
            employee_data = self.db_manager.get_employee_by_id(employee_id)
            if employee_data:
                self.employee = Employee.from_dict(employee_data)
        
        self.create_widgets()
        self.center_window()
        
        # Focus on first field
        self.nama_entry.focus()

    def setup_styles(self):
        """Setup modern styling"""
        style = ttk.Style()

        # Configure modern button styles
        style.configure('Modern.TButton',
                       padding=(15, 10),
                       font=('Segoe UI', 10, 'bold'))

        # Configure entry styles
        style.configure('Modern.TEntry',
                       padding=8,
                       font=('Segoe UI', 10))

        # Configure combobox styles
        style.configure('Modern.TCombobox',
                       padding=8,
                       font=('Segoe UI', 10))

        # Configure spinbox styles
        style.configure('Modern.TSpinbox',
                       padding=8,
                       font=('Segoe UI', 10))

    def create_widgets(self):
        """Create form widgets with scrollable content"""
        # Create main container
        main_container = ttk.Frame(self.window)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create canvas and scrollbar for scrolling
        canvas = tk.Canvas(main_container, bg='#ffffff', highlightthickness=0, bd=0)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        # Configure scrolling
        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas - improved version
        def _on_mousewheel(event):
            try:
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except tk.TclError:
                pass  # Canvas might be destroyed

        # Store canvas reference for proper cleanup
        self.canvas = canvas
        canvas.bind("<MouseWheel>", _on_mousewheel)
        scrollable_frame.bind("<MouseWheel>", _on_mousewheel)

        # Main content frame (now inside scrollable frame)
        main_frame = ttk.Frame(scrollable_frame, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Header dengan gradient effect
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 30))

        # Modern title with better styling
        title = "✏️ Edit Data Karyawan" if self.employee_id else "➕ Tambah Karyawan Baru"
        title_label = ttk.Label(header_frame, text=title,
                               font=('Segoe UI', 20, 'bold'),
                               foreground='#2c3e50')
        title_label.pack()

        # Subtitle dengan styling yang lebih baik
        subtitle = "Perbarui informasi karyawan" if self.employee_id else "Masukkan data karyawan baru"
        subtitle_label = ttk.Label(header_frame, text=subtitle,
                                  font=('Segoe UI', 11),
                                  foreground='#7f8c8d')
        subtitle_label.pack(pady=(5, 0))

        # Separator line yang lebih elegan
        separator1 = ttk.Separator(main_frame, orient='horizontal')
        separator1.pack(fill=tk.X, pady=(0, 25))
        
        # Form fields
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.BOTH, expand=True)
        
        # Personal Information Section dengan styling modern
        personal_frame = ttk.LabelFrame(form_frame, text="👤 Informasi Personal", padding="20")
        personal_frame.pack(fill=tk.X, pady=(0, 20))
        personal_frame.columnconfigure(1, weight=1)

        # Nama dengan styling yang lebih baik
        ttk.Label(personal_frame, text="📝 Nama Lengkap:",
                 font=('Segoe UI', 11, 'bold'),
                 foreground='#34495e').grid(row=0, column=0, sticky=tk.W, pady=12)
        self.nama_entry = ttk.Entry(personal_frame, width=35,
                                   font=('Segoe UI', 11),
                                   style='Modern.TEntry')
        self.nama_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=12, padx=(20, 0))

        # NIP
        ttk.Label(personal_frame, text="🆔 NIP:",
                 font=('Segoe UI', 11, 'bold'),
                 foreground='#34495e').grid(row=1, column=0, sticky=tk.W, pady=12)
        self.nip_entry = ttk.Entry(personal_frame, width=35,
                                  font=('Segoe UI', 11),
                                  style='Modern.TEntry')
        self.nip_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=12, padx=(20, 0))

        # Posisi
        ttk.Label(personal_frame, text="💼 Posisi:",
                 font=('Segoe UI', 11, 'bold'),
                 foreground='#34495e').grid(row=2, column=0, sticky=tk.W, pady=12)
        self.posisi_var = tk.StringVar()
        self.posisi_combo = ttk.Combobox(personal_frame, textvariable=self.posisi_var,
                                         values=POSISI_OPTIONS, state="readonly",
                                         width=32, font=('Segoe UI', 11),
                                         style='Modern.TCombobox')
        self.posisi_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=12, padx=(20, 0))

        # Kriteria penilaian section dengan styling modern
        criteria_frame = ttk.LabelFrame(form_frame, text="⭐ Kriteria Penilaian (Skala 1-15)", padding="20")
        criteria_frame.pack(fill=tk.X, pady=(0, 20))
        criteria_frame.columnconfigure(1, weight=1)
        
        # Kemampuan Teknik (C1) dengan styling yang lebih baik
        ttk.Label(criteria_frame, text="🔧 Kemampuan Teknik (C1):",
                 font=('Segoe UI', 11, 'bold'),
                 foreground='#34495e').grid(row=0, column=0, sticky=tk.W, pady=15)
        kemampuan_frame = ttk.Frame(criteria_frame)
        kemampuan_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=15, padx=(20, 0))
        self.kemampuan_var = tk.DoubleVar()
        self.kemampuan_scale = ttk.Scale(kemampuan_frame, from_=1, to=15, variable=self.kemampuan_var,
                                      orient=tk.HORIZONTAL, length=250)
        self.kemampuan_scale.pack(side=tk.LEFT)
        self.kemampuan_label = ttk.Label(kemampuan_frame, text="8",
                                      font=('Segoe UI', 12, 'bold'),
                                      foreground='#3498db',
                                      width=4)
        self.kemampuan_label.pack(side=tk.LEFT, padx=(15, 0))
        self.kemampuan_scale.configure(command=lambda v: self.kemampuan_label.configure(text=f"{int(float(v))}"))

        # Kualitas (C2)
        ttk.Label(criteria_frame, text="⭐ Kualitas (C2):",
                 font=('Segoe UI', 11, 'bold'),
                 foreground='#34495e').grid(row=1, column=0, sticky=tk.W, pady=15)
        kualitas_frame = ttk.Frame(criteria_frame)
        kualitas_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=15, padx=(20, 0))
        self.kualitas_var = tk.DoubleVar()
        self.kualitas_scale = ttk.Scale(kualitas_frame, from_=1, to=15, variable=self.kualitas_var,
                                           orient=tk.HORIZONTAL, length=250)
        self.kualitas_scale.pack(side=tk.LEFT)
        self.kualitas_label = ttk.Label(kualitas_frame, text="8",
                                           font=('Segoe UI', 12, 'bold'),
                                           foreground='#27ae60',
                                           width=4)
        self.kualitas_label.pack(side=tk.LEFT, padx=(15, 0))
        self.kualitas_scale.configure(command=lambda v: self.kualitas_label.configure(text=f"{int(float(v))}"))

        # Presisi (C3)
        ttk.Label(criteria_frame, text="🎯 Presisi (C3):",
                 font=('Segoe UI', 11, 'bold'),
                 foreground='#34495e').grid(row=2, column=0, sticky=tk.W, pady=15)
        presisi_frame = ttk.Frame(criteria_frame)
        presisi_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=15, padx=(20, 0))
        self.presisi_var = tk.DoubleVar()
        self.presisi_scale = ttk.Scale(presisi_frame, from_=1, to=15, variable=self.presisi_var,
                                     orient=tk.HORIZONTAL, length=250)
        self.presisi_scale.pack(side=tk.LEFT)
        self.presisi_label = ttk.Label(presisi_frame, text="8",
                                     font=('Segoe UI', 12, 'bold'),
                                     foreground='#f39c12',
                                     width=4)
        self.presisi_label.pack(side=tk.LEFT, padx=(15, 0))
        self.presisi_scale.configure(command=lambda v: self.presisi_label.configure(text=f"{int(float(v))}"))

        # Pelanggaran (C4)
        ttk.Label(criteria_frame, text="⚠️ Pelanggaran (C4):",
                 font=('Segoe UI', 11, 'bold'),
                 foreground='#34495e').grid(row=3, column=0, sticky=tk.W, pady=15)
        pelanggaran_frame = ttk.Frame(criteria_frame)
        pelanggaran_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=15, padx=(20, 0))
        self.pelanggaran_var = tk.DoubleVar()
        self.pelanggaran_scale = ttk.Scale(pelanggaran_frame, from_=1, to=15, variable=self.pelanggaran_var,
                                         orient=tk.HORIZONTAL, length=250)
        self.pelanggaran_scale.pack(side=tk.LEFT)
        self.pelanggaran_label = ttk.Label(pelanggaran_frame, text="3",
                                         font=('Segoe UI', 12, 'bold'),
                                         foreground='#e74c3c',
                                         width=4)
        self.pelanggaran_label.pack(side=tk.LEFT, padx=(15, 0))
        self.pelanggaran_scale.configure(command=lambda v: self.pelanggaran_label.configure(text=f"{int(float(v))}"))

        # Absensi (C5)
        ttk.Label(criteria_frame, text="📊 Absensi (C5):",
                 font=('Segoe UI', 11, 'bold'),
                 foreground='#34495e').grid(row=4, column=0, sticky=tk.W, pady=15)
        absensi_frame = ttk.Frame(criteria_frame)
        absensi_frame.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=15, padx=(20, 0))
        self.absensi_var = tk.DoubleVar()
        self.absensi_scale = ttk.Scale(absensi_frame, from_=1, to=15, variable=self.absensi_var,
                                      orient=tk.HORIZONTAL, length=250)
        self.absensi_scale.pack(side=tk.LEFT)
        self.absensi_label = ttk.Label(absensi_frame, text="8",
                                      font=('Segoe UI', 12, 'bold'),
                                      foreground='#17a2b8',
                                      width=4)
        self.absensi_label.pack(side=tk.LEFT, padx=(15, 0))
        self.absensi_scale.configure(command=lambda v: self.absensi_label.configure(text=f"{int(float(v))}"))
        
        # Load data if editing
        if self.employee:
            self.load_employee_data()
        else:
            # Set default values
            self.kemampuan_var.set(8)
            self.kualitas_var.set(8)
            self.presisi_var.set(8)
            self.pelanggaran_var.set(3)
            self.absensi_var.set(8)

        # Help text dengan styling yang lebih baik
        help_frame = ttk.Frame(main_frame)
        help_frame.pack(fill=tk.X, pady=(20, 0))

        help_text = "💡 Tips: Gunakan skala 1-15 untuk penilaian. C4 (Pelanggaran) adalah Cost - semakin rendah semakin baik"
        ttk.Label(help_frame, text=help_text,
                 font=('Segoe UI', 10, 'italic'),
                 foreground='#7f8c8d',
                 justify=tk.CENTER).pack()

        # Separator line yang lebih elegan
        separator2 = ttk.Separator(main_frame, orient='horizontal')
        separator2.pack(fill=tk.X, pady=(25, 20))

        # Modern buttons dengan styling yang lebih baik
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 30))

        # Save button (prominent dan modern)
        save_text = "💾 PERBARUI DATA KARYAWAN" if self.employee_id else "💾 SIMPAN DATA KARYAWAN"
        save_btn = ttk.Button(button_frame, text=save_text,
                             command=self.save_employee,
                             style='Modern.TButton')
        save_btn.configure(width=28)
        save_btn.pack(side=tk.RIGHT, padx=(15, 0))

        # Cancel button
        cancel_btn = ttk.Button(button_frame, text="❌ BATAL",
                               command=self.window.destroy,
                               style='Modern.TButton')
        cancel_btn.configure(width=18)
        cancel_btn.pack(side=tk.RIGHT)

        # Action instruction dengan styling yang lebih baik
        action_text = "Klik tombol SIMPAN untuk menyimpan data karyawan" if not self.employee_id else "Klik tombol PERBARUI untuk menyimpan perubahan"
        instruction_frame = ttk.Frame(main_frame)
        instruction_frame.pack(fill=tk.X, pady=(15, 0))

        ttk.Label(instruction_frame, text=action_text,
                 font=('Segoe UI', 10, 'bold'),
                 foreground='#2980b9',
                 justify=tk.CENTER).pack()
    
    def load_employee_data(self):
        """Load employee data into form fields"""
        if not self.employee:
            return

        self.nama_entry.insert(0, self.employee.nama)
        self.nip_entry.insert(0, self.employee.nip)
        self.posisi_var.set(self.employee.posisi)
        self.kemampuan_var.set(self.employee.kemampuan_teknik)
        self.kualitas_var.set(self.employee.kualitas)
        self.presisi_var.set(self.employee.presisi)
        self.pelanggaran_var.set(self.employee.pelanggaran)
        self.absensi_var.set(self.employee.absensi)
    
    def save_employee(self):
        """Save employee data"""
        try:
            # Create employee object
            employee = Employee(
                id=self.employee_id,
                nama=self.nama_entry.get().strip(),
                nip=self.nip_entry.get().strip(),
                posisi=self.posisi_var.get(),
                kemampuan_teknik=self.kemampuan_var.get(),
                kualitas=self.kualitas_var.get(),
                presisi=self.presisi_var.get(),
                pelanggaran=self.pelanggaran_var.get(),
                absensi=self.absensi_var.get()
            )
            
            # Validate data
            is_valid, message = employee.validate()
            if not is_valid:
                messagebox.showerror("Error", message)
                return
            
            # Save to database
            if self.employee_id:
                # Update existing employee
                success = self.db_manager.update_employee(self.employee_id, employee.to_dict())
                action = "diperbarui"
            else:
                # Add new employee
                success = self.db_manager.add_employee(employee.to_dict())
                action = "ditambahkan"
            
            if success:
                messagebox.showinfo("🎉 Berhasil!",
                                  f"✅ Data karyawan berhasil {action}!\n\n"
                                  f"👤 Nama: {employee.nama}\n"
                                  f"🆔 NIP: {employee.nip}\n"
                                  f"💼 Posisi: {employee.posisi}")
                self.window.destroy()
            else:
                messagebox.showerror("❌ Gagal Menyimpan",
                                   f"Tidak dapat menyimpan data karyawan.\n\n"
                                   f"Kemungkinan penyebab:\n"
                                   f"• NIP '{employee.nip}' sudah digunakan\n"
                                   f"• Koneksi database bermasalah\n\n"
                                   f"Silakan periksa dan coba lagi.")
                
        except Exception as e:
            messagebox.showerror("Error", f"Terjadi kesalahan: {str(e)}")
    
    def center_window(self):
        """Center the window on parent"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width // 2) - (width // 2)
        y = parent_y + (parent_height // 2) - (height // 2)
        
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def on_closing(self):
        """Handle window close event"""
        # Unbind mousewheel events to prevent errors
        try:
            if hasattr(self, 'canvas'):
                self.canvas.unbind("<MouseWheel>")
        except:
            pass
        self.window.destroy()
