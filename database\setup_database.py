#!/usr/bin/env python3
"""
SPK TOPSIS Enhanced v2.0 - Database Setup Script
Author: <PERSON> Wibowo
NIM: 211011450583
Kelas: 06TPLP003

Script untuk membuat dan setup database SQLite untuk sistem TOPSIS
"""

import sqlite3
import os
import sys
from datetime import datetime
import hashlib

class DatabaseSetup:
    def __init__(self, db_path='spk_karyawan_enhanced.db'):
        self.db_path = db_path
        self.conn = None
        
    def connect(self):
        """Koneksi ke database"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute("PRAGMA foreign_keys = ON")
            return True
        except Exception as e:
            print(f"❌ Error koneksi database: {e}")
            return False
    
    def close(self):
        """Tutup koneksi database"""
        if self.conn:
            self.conn.close()
    
    def hash_password(self, password):
        """Hash password menggunakan SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def execute_sql_file(self, sql_file_path):
        """Execute SQL file"""
        try:
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            # Remove .print commands for Python execution
            sql_lines = []
            for line in sql_content.split('\n'):
                if not line.strip().startswith('.print') and not line.strip().startswith('.headers') and not line.strip().startswith('.mode'):
                    sql_lines.append(line)
            
            sql_content = '\n'.join(sql_lines)
            
            # Execute SQL
            self.conn.executescript(sql_content)
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error executing SQL file: {e}")
            return False
    
    def create_tables(self):
        """Buat semua tabel yang diperlukan"""
        try:
            cursor = self.conn.cursor()
            
            print("📊 Membuat tabel database...")
            
            # 1. Tabel Users
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password TEXT NOT NULL,
                    role TEXT NOT NULL CHECK(role IN ('admin', 'operator')),
                    full_name TEXT,
                    email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            
            # 2. Tabel Dynamic_Criteria
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dynamic_criteria (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    weight REAL NOT NULL CHECK(weight >= 0 AND weight <= 1),
                    type TEXT NOT NULL CHECK(type IN ('benefit', 'cost')),
                    description TEXT,
                    min_value REAL DEFAULT 1.0,
                    max_value REAL DEFAULT 15.0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 3. Tabel Dynamic_Alternatives
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dynamic_alternatives (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    position TEXT DEFAULT 'Karyawan',
                    department TEXT,
                    employee_id TEXT UNIQUE,
                    description TEXT,
                    hire_date DATE,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 4. Tabel Dynamic_Evaluations
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dynamic_evaluations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alternative_id INTEGER NOT NULL,
                    criteria_id INTEGER NOT NULL,
                    score REAL NOT NULL CHECK(score >= 0),
                    evaluator TEXT,
                    evaluation_date DATE DEFAULT CURRENT_DATE,
                    evaluation_period TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (alternative_id) REFERENCES dynamic_alternatives(id) ON DELETE CASCADE,
                    FOREIGN KEY (criteria_id) REFERENCES dynamic_criteria(id) ON DELETE CASCADE,
                    UNIQUE(alternative_id, criteria_id, evaluation_period)
                )
            ''')
            
            # 5. Tabel TOPSIS_Results
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS topsis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    calculation_id TEXT NOT NULL,
                    alternative_id INTEGER NOT NULL,
                    normalized_scores TEXT,
                    weighted_scores TEXT,
                    positive_distance REAL NOT NULL,
                    negative_distance REAL NOT NULL,
                    preference_value REAL NOT NULL CHECK(preference_value >= 0 AND preference_value <= 1),
                    ranking INTEGER NOT NULL,
                    calculation_method TEXT DEFAULT 'TOPSIS',
                    calculated_by TEXT,
                    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (alternative_id) REFERENCES dynamic_alternatives(id) ON DELETE CASCADE
                )
            ''')
            
            # 6. Tabel Calculation_History
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS calculation_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    calculation_id TEXT UNIQUE NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    evaluation_period TEXT,
                    total_alternatives INTEGER NOT NULL,
                    total_criteria INTEGER NOT NULL,
                    criteria_weights TEXT,
                    calculation_status TEXT DEFAULT 'completed' CHECK(calculation_status IN ('pending', 'completed', 'failed')),
                    created_by TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.conn.commit()
            print("✅ Tabel berhasil dibuat")
            return True
            
        except Exception as e:
            print(f"❌ Error membuat tabel: {e}")
            return False
    
    def create_indexes(self):
        """Buat index untuk optimasi query"""
        try:
            cursor = self.conn.cursor()
            
            print("🔍 Membuat index database...")
            
            indexes = [
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username ON users(username)",
                "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_criteria_code ON dynamic_criteria(code)",
                "CREATE INDEX IF NOT EXISTS idx_criteria_type ON dynamic_criteria(type)",
                "CREATE INDEX IF NOT EXISTS idx_alternatives_name ON dynamic_alternatives(name)",
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_alternatives_employee_id ON dynamic_alternatives(employee_id)",
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_eval_alt_crit_period ON dynamic_evaluations(alternative_id, criteria_id, evaluation_period)",
                "CREATE INDEX IF NOT EXISTS idx_eval_alternative ON dynamic_evaluations(alternative_id)",
                "CREATE INDEX IF NOT EXISTS idx_eval_criteria ON dynamic_evaluations(criteria_id)",
                "CREATE INDEX IF NOT EXISTS idx_results_calculation_id ON topsis_results(calculation_id)",
                "CREATE INDEX IF NOT EXISTS idx_results_ranking ON topsis_results(ranking)"
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            self.conn.commit()
            print("✅ Index berhasil dibuat")
            return True
            
        except Exception as e:
            print(f"❌ Error membuat index: {e}")
            return False
    
    def insert_initial_data(self):
        """Insert data awal sistem"""
        try:
            cursor = self.conn.cursor()
            
            print("📝 Memasukkan data awal...")
            
            # 1. Insert default users
            admin_password = self.hash_password('admin123')
            operator_password = self.hash_password('operator123')
            
            cursor.execute('''
                INSERT OR REPLACE INTO users (username, password, role, full_name, email, created_at) VALUES 
                (?, ?, 'admin', 'Administrator', '<EMAIL>', datetime('now'))
            ''', ('admin', admin_password))
            
            cursor.execute('''
                INSERT OR REPLACE INTO users (username, password, role, full_name, email, created_at) VALUES 
                (?, ?, 'operator', 'Data Operator', '<EMAIL>', datetime('now'))
            ''', ('operator', operator_password))
            
            # 2. Insert default criteria
            criteria_data = [
                ('C1', 'Kemampuan Teknik', 0.14, 'benefit', 'Kemampuan teknis dalam menjalankan tugas pekerjaan'),
                ('C2', 'Kualitas', 0.19, 'benefit', 'Kualitas hasil kerja yang dihasilkan'),
                ('C3', 'Presisi', 0.28, 'benefit', 'Tingkat ketelitian dan presisi dalam bekerja'),
                ('C4', 'Pelanggaran', 0.18, 'cost', 'Jumlah pelanggaran (semakin rendah semakin baik)'),
                ('C5', 'Absensi', 0.21, 'benefit', 'Tingkat kehadiran karyawan')
            ]
            
            for code, name, weight, type_val, desc in criteria_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO dynamic_criteria 
                    (code, name, weight, type, description, min_value, max_value, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, ?, 1.0, 15.0, datetime('now'), datetime('now'))
                ''', (code, name, weight, type_val, desc))
            
            # 3. Insert sample alternatives
            alternatives_data = [
                ('Rahmat', 'Karyawan', 'Produksi', 'EMP001', 'Karyawan dengan komitmen kehadiran tinggi', '2022-01-15'),
                ('Jaya', 'Karyawan', 'Produksi', 'EMP002', 'Karyawan dengan kemampuan teknik dan kualitas kerja baik', '2022-03-10'),
                ('Bunga', 'Karyawan', 'Produksi', 'EMP003', 'Karyawan dengan presisi dan ketelitian kerja tinggi', '2022-02-20')
            ]
            
            for name, position, dept, emp_id, desc, hire_date in alternatives_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO dynamic_alternatives 
                    (name, position, department, employee_id, description, hire_date, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
                ''', (name, position, dept, emp_id, desc, hire_date))
            
            # 4. Insert sample evaluations
            evaluations_data = [
                # Rahmat (ID=1)
                (1, 1, 10.0), (1, 2, 9.0), (1, 3, 10.0), (1, 4, 2.0), (1, 5, 15.0),
                # Jaya (ID=2)
                (2, 1, 14.0), (2, 2, 15.0), (2, 3, 12.0), (2, 4, 2.0), (2, 5, 13.0),
                # Bunga (ID=3)
                (3, 1, 13.0), (3, 2, 12.0), (3, 3, 15.0), (3, 4, 1.0), (3, 5, 12.0)
            ]
            
            for alt_id, crit_id, score in evaluations_data:
                cursor.execute('''
                    INSERT OR REPLACE INTO dynamic_evaluations 
                    (alternative_id, criteria_id, score, evaluator, evaluation_date, evaluation_period, created_at, updated_at) 
                    VALUES (?, ?, ?, 'admin', date('now'), '2024-Q1', datetime('now'), datetime('now'))
                ''', (alt_id, crit_id, score))
            
            self.conn.commit()
            print("✅ Data awal berhasil dimasukkan")
            return True
            
        except Exception as e:
            print(f"❌ Error memasukkan data awal: {e}")
            return False
    
    def verify_database(self):
        """Verifikasi database yang telah dibuat"""
        try:
            cursor = self.conn.cursor()
            
            print("\n" + "="*50)
            print("📊 VERIFIKASI DATABASE")
            print("="*50)
            
            # 1. Cek users
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"👤 Users: {user_count}")
            
            # 2. Cek criteria
            cursor.execute("SELECT COUNT(*) FROM dynamic_criteria")
            criteria_count = cursor.fetchone()[0]
            cursor.execute("SELECT SUM(weight) FROM dynamic_criteria")
            total_weight = cursor.fetchone()[0] or 0
            print(f"📊 Kriteria: {criteria_count} (Total bobot: {total_weight*100:.0f}%)")
            
            # 3. Cek alternatives
            cursor.execute("SELECT COUNT(*) FROM dynamic_alternatives")
            alt_count = cursor.fetchone()[0]
            print(f"👥 Alternatif: {alt_count}")
            
            # 4. Cek evaluations
            cursor.execute("SELECT COUNT(*) FROM dynamic_evaluations")
            eval_count = cursor.fetchone()[0]
            print(f"📝 Evaluasi: {eval_count}")
            
            # 5. Detail data
            print(f"\n📋 DETAIL DATA:")
            
            cursor.execute("SELECT code, name, ROUND(weight*100) || '%' as bobot, type FROM dynamic_criteria ORDER BY code")
            criteria = cursor.fetchall()
            print("Kriteria:")
            for crit in criteria:
                print(f"   {crit[0]} - {crit[1]}: {crit[2]} ({crit[3]})")
            
            cursor.execute("SELECT name, position FROM dynamic_alternatives ORDER BY name")
            alternatives = cursor.fetchall()
            print("Karyawan:")
            for alt in alternatives:
                print(f"   - {alt[0]} ({alt[1]})")
            
            print(f"\n✅ Database siap digunakan!")
            print(f"📁 File: {self.db_path}")
            print(f"📏 Ukuran: {os.path.getsize(self.db_path) / 1024:.1f} KB")
            
            return True
            
        except Exception as e:
            print(f"❌ Error verifikasi database: {e}")
            return False

def main():
    """Main function untuk setup database"""
    print("="*60)
    print("🗄️ SPK TOPSIS Enhanced v2.0 - Database Setup")
    print("="*60)
    print("Author: Muhammad Bayu Prasetyo Wibowo")
    print("NIM: 211011450583")
    print("Kelas: 06TPLP003")
    print("="*60)
    print()
    
    # Inisialisasi setup
    db_setup = DatabaseSetup()
    
    try:
        # 1. Koneksi ke database
        print("🔌 Menghubungkan ke database...")
        if not db_setup.connect():
            return False
        
        # 2. Cek apakah ada file SQL lengkap
        sql_file = 'SPK_TOPSIS_DATABASE_COMPLETE.sql'
        if os.path.exists(sql_file):
            print(f"📄 File SQL ditemukan: {sql_file}")
            print("🔧 Menggunakan file SQL lengkap...")
            if db_setup.execute_sql_file(sql_file):
                print("✅ Database berhasil dibuat dari file SQL!")
            else:
                print("⚠️ Gagal dari file SQL, menggunakan metode manual...")
                # Fallback ke metode manual
                if not (db_setup.create_tables() and db_setup.create_indexes() and db_setup.insert_initial_data()):
                    return False
        else:
            print("📄 File SQL tidak ditemukan, menggunakan metode manual...")
            # 2. Buat tabel
            if not db_setup.create_tables():
                return False
            
            # 3. Buat index
            if not db_setup.create_indexes():
                return False
            
            # 4. Insert data awal
            if not db_setup.insert_initial_data():
                return False
        
        # 5. Verifikasi
        if not db_setup.verify_database():
            return False
        
        print("\n🎉 Setup database berhasil!")
        print("\n🎯 Langkah selanjutnya:")
        print("   1. Jalankan aplikasi SPK TOPSIS")
        print("   2. Login dengan admin/admin123 atau operator/operator123")
        print("   3. Mulai evaluasi karyawan")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setup database: {e}")
        return False
        
    finally:
        db_setup.close()

if __name__ == "__main__":
    success = main()
    input(f"\nPress Enter to exit...")
    sys.exit(0 if success else 1)
