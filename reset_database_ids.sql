-- Reset Database IDs - <PERSON>K TOPSIS Enhanced v2.0
-- Mengembalikan ID ke urutan 1, 2, 3, dst.

-- Hapus semua data
DELETE FROM dynamic_evaluations;
DELETE FROM topsis_results;
DELETE FROM dynamic_alternatives;
DELETE FROM dynamic_criteria;

-- Reset auto increment counter
DELETE FROM sqlite_sequence WHERE name IN ('dynamic_criteria', 'dynamic_alternatives', 'dynamic_evaluations', 'topsis_results');

-- Insert kriteria dengan ID yang bersih (dimulai dari 1)
INSERT INTO dynamic_criteria (code, name, weight, type, created_at, updated_at) VALUES
('C1', 'Kemampuan Teknik', 0.14, 'benefit', datetime('now'), datetime('now')),
('C2', 'Kualitas', 0.19, 'benefit', datetime('now'), datetime('now')),
('C3', 'Presisi', 0.28, 'benefit', datetime('now'), datetime('now')),
('C4', '<PERSON><PERSON><PERSON>garan', 0.18, 'cost', datetime('now'), datetime('now')),
('C5', 'Absensi', 0.21, 'benefit', datetime('now'), datetime('now'));

-- Insert alternatif dengan ID yang bersih (dimulai dari 1)
INSERT INTO dynamic_alternatives (name, position, created_at, updated_at) VALUES
('Rahmat', 'Karyawan', datetime('now'), datetime('now')),
('Jaya', 'Karyawan', datetime('now'), datetime('now')),
('Bunga', 'Karyawan', datetime('now'), datetime('now'));

-- Insert evaluasi dengan nilai yang benar
-- Rahmat (ID=1)
INSERT INTO dynamic_evaluations (alternative_id, criteria_id, score, created_at, updated_at) VALUES
(1, 1, 10, datetime('now'), datetime('now')),  -- C1: 10
(1, 2, 9, datetime('now'), datetime('now')),   -- C2: 9
(1, 3, 10, datetime('now'), datetime('now')),  -- C3: 10
(1, 4, 2, datetime('now'), datetime('now')),   -- C4: 2
(1, 5, 15, datetime('now'), datetime('now'));  -- C5: 15

-- Jaya (ID=2)
INSERT INTO dynamic_evaluations (alternative_id, criteria_id, score, created_at, updated_at) VALUES
(2, 1, 14, datetime('now'), datetime('now')),  -- C1: 14
(2, 2, 15, datetime('now'), datetime('now')),  -- C2: 15
(2, 3, 12, datetime('now'), datetime('now')),  -- C3: 12
(2, 4, 2, datetime('now'), datetime('now')),   -- C4: 2
(2, 5, 13, datetime('now'), datetime('now'));  -- C5: 13

-- Bunga (ID=3)
INSERT INTO dynamic_evaluations (alternative_id, criteria_id, score, created_at, updated_at) VALUES
(3, 1, 13, datetime('now'), datetime('now')),  -- C1: 13
(3, 2, 12, datetime('now'), datetime('now')),  -- C2: 12
(3, 3, 15, datetime('now'), datetime('now')),  -- C3: 15
(3, 4, 1, datetime('now'), datetime('now')),   -- C4: 1
(3, 5, 12, datetime('now'), datetime('now'));  -- C5: 12

-- Verifikasi hasil
.print "=== VERIFIKASI SETELAH RESET ==="
.print ""
.print "KRITERIA (ID harus 1-5):"
SELECT id, code, name, ROUND(weight*100) || '%' as bobot, type FROM dynamic_criteria ORDER BY id;

.print ""
.print "ALTERNATIF (ID harus 1-3):"
SELECT id, name, position FROM dynamic_alternatives ORDER BY id;

.print ""
.print "EVALUASI (15 records):"
SELECT COUNT(*) as total_evaluasi FROM dynamic_evaluations;

.print ""
.print "=== RESET SELESAI ==="
