"""
Employees Frame untuk SPK Karyawan TOPSIS
Mengelola data karyawan dengan form input dan tabel
"""

import tkinter as tk
from tkinter import ttk, messagebox
import re

class EmployeesFrame:
    def __init__(self, parent, db_manager):
        """Initialize employees frame"""
        self.parent = parent
        self.db_manager = db_manager
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        # Current editing employee ID
        self.editing_id = None
        
        self.create_widgets()
        self.load_employees()
    
    def create_widgets(self):
        """Create employees management widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="Manajemen Data Karyawan", 
                 style='Title.TLabel').pack(side='left')
        
        ttk.Button(title_frame, text="🔄 Refresh", 
                  command=self.load_employees, style='Action.TButton').pack(side='right')
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=1)
        main_container.grid_columnconfigure(1, weight=1)
        
        # Left side - Form
        self.create_form(main_container)
        
        # Right side - Table
        self.create_table(main_container)
    
    def create_form(self, parent):
        """Create employee input form"""
        form_frame = ttk.LabelFrame(parent, text="Form Data Karyawan", padding=15)
        form_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))
        
        # Form fields
        fields = [
            ("Nama Lengkap:", "nama"),
            ("NIP:", "nip"),
            ("Posisi/Divisi:", "posisi")
        ]
        
        self.form_vars = {}
        row = 0
        
        for label_text, var_name in fields:
            ttk.Label(form_frame, text=label_text, style='Header.TLabel').grid(
                row=row, column=0, sticky='w', pady=5)
            
            var = tk.StringVar()
            entry = ttk.Entry(form_frame, textvariable=var, width=30)
            entry.grid(row=row, column=1, sticky='ew', pady=5, padx=(10, 0))
            
            self.form_vars[var_name] = var
            row += 1
        
        # Separator
        ttk.Separator(form_frame, orient='horizontal').grid(
            row=row, column=0, columnspan=2, sticky='ew', pady=15)
        row += 1
        
        # Criteria section
        ttk.Label(form_frame, text="Penilaian Kriteria (Skala 1-15):", 
                 style='Subtitle.TLabel').grid(row=row, column=0, columnspan=2, sticky='w', pady=(0, 10))
        row += 1
        
        # Criteria fields
        criteria_fields = [
            ("C1 - Kemampuan Teknik:", "kemampuan_teknik", "Kemampuan teknis dalam bekerja"),
            ("C2 - Kualitas:", "kualitas", "Kualitas hasil kerja"),
            ("C3 - Presisi:", "presisi", "Ketepatan dan ketelitian kerja"),
            ("C4 - Pelanggaran:", "pelanggaran", "Jumlah pelanggaran (1=banyak, 15=sedikit)"),
            ("C5 - Absensi:", "absensi", "Tingkat kehadiran (1=rendah, 15=tinggi)")
        ]
        
        for label_text, var_name, tooltip in criteria_fields:
            # Label with tooltip
            label_frame = ttk.Frame(form_frame)
            label_frame.grid(row=row, column=0, sticky='w', pady=5)
            
            ttk.Label(label_frame, text=label_text, style='Header.TLabel').pack(side='left')
            
            # Spinbox for numeric input
            var = tk.DoubleVar(value=8.0)  # Default middle value
            spinbox = ttk.Spinbox(form_frame, from_=1, to=15, textvariable=var, 
                                width=10, increment=0.5)
            spinbox.grid(row=row, column=1, sticky='w', pady=5, padx=(10, 0))
            
            self.form_vars[var_name] = var
            
            # Tooltip
            ttk.Label(form_frame, text=tooltip, style='Info.TLabel', 
                     foreground='gray').grid(row=row+1, column=0, columnspan=2, sticky='w', pady=(0, 5))
            
            row += 2
        
        # Configure column weights
        form_frame.grid_columnconfigure(1, weight=1)
        
        # Buttons
        button_frame = ttk.Frame(form_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=20)
        
        self.save_button = ttk.Button(button_frame, text="💾 Simpan", 
                                     command=self.save_employee, style='Success.TButton')
        self.save_button.pack(side='left', padx=(0, 10))
        
        self.update_button = ttk.Button(button_frame, text="✏️ Update", 
                                       command=self.update_employee, style='Action.TButton')
        self.update_button.pack(side='left', padx=(0, 10))
        self.update_button.configure(state='disabled')
        
        ttk.Button(button_frame, text="🗑️ Clear", 
                  command=self.clear_form, style='Nav.TButton').pack(side='left')
    
    def create_table(self, parent):
        """Create employees table"""
        table_frame = ttk.LabelFrame(parent, text="Daftar Karyawan", padding=10)
        table_frame.grid(row=0, column=1, sticky='nsew', padx=(10, 0))
        
        # Configure grid
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview
        columns = ('ID', 'Nama', 'NIP', 'Posisi', 'C1', 'C2', 'C3', 'C4', 'C5')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {'ID': 50, 'Nama': 150, 'NIP': 100, 'Posisi': 120, 
                        'C1': 50, 'C2': 50, 'C3': 50, 'C4': 50, 'C5': 50}
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Bind events
        self.tree.bind('<Double-1>', self.on_item_double_click)
        self.tree.bind('<Button-3>', self.on_right_click)  # Right click menu
        
        # Context menu
        self.create_context_menu()
        
        # Action buttons
        action_frame = ttk.Frame(table_frame)
        action_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        ttk.Button(action_frame, text="✏️ Edit", 
                  command=self.edit_selected, style='Action.TButton').pack(side='left', padx=5)
        
        ttk.Button(action_frame, text="🗑️ Hapus", 
                  command=self.delete_selected, style='Nav.TButton').pack(side='left', padx=5)
        
        ttk.Button(action_frame, text="📊 Lihat Detail", 
                  command=self.view_detail, style='Nav.TButton').pack(side='left', padx=5)
    
    def create_context_menu(self):
        """Create right-click context menu"""
        self.context_menu = tk.Menu(self.tree, tearoff=0)
        self.context_menu.add_command(label="Edit", command=self.edit_selected)
        self.context_menu.add_command(label="Hapus", command=self.delete_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Lihat Detail", command=self.view_detail)
    
    def validate_form(self):
        """Validate form input"""
        # Check required fields
        required_fields = ['nama', 'nip', 'posisi']
        for field in required_fields:
            if not self.form_vars[field].get().strip():
                messagebox.showerror("Error Validasi", f"Field {field} harus diisi!")
                return False
        
        # Validate NIP format (alphanumeric)
        nip = self.form_vars['nip'].get().strip()
        if not re.match(r'^[A-Za-z0-9]+$', nip):
            messagebox.showerror("Error Validasi", "NIP harus berupa kombinasi huruf dan angka!")
            return False
        
        # Validate criteria values
        criteria_fields = ['kemampuan_teknik', 'kualitas', 'presisi', 'pelanggaran', 'absensi']
        for field in criteria_fields:
            try:
                value = float(self.form_vars[field].get())
                if not (1 <= value <= 15):
                    messagebox.showerror("Error Validasi", f"Nilai {field} harus antara 1-15!")
                    return False
            except ValueError:
                messagebox.showerror("Error Validasi", f"Nilai {field} harus berupa angka!")
                return False
        
        return True
    
    def save_employee(self):
        """Save new employee"""
        if not self.validate_form():
            return
        
        try:
            success = self.db_manager.add_employee(
                nama=self.form_vars['nama'].get().strip(),
                nip=self.form_vars['nip'].get().strip(),
                posisi=self.form_vars['posisi'].get().strip(),
                kemampuan_teknik=float(self.form_vars['kemampuan_teknik'].get()),
                kualitas=float(self.form_vars['kualitas'].get()),
                presisi=float(self.form_vars['presisi'].get()),
                pelanggaran=float(self.form_vars['pelanggaran'].get()),
                absensi=float(self.form_vars['absensi'].get())
            )
            
            if success:
                messagebox.showinfo("Sukses", "Data karyawan berhasil disimpan!")
                self.clear_form()
                self.load_employees()
            else:
                messagebox.showerror("Error", "Gagal menyimpan data. NIP mungkin sudah ada!")
                
        except Exception as e:
            messagebox.showerror("Error", f"Terjadi kesalahan: {str(e)}")
    
    def update_employee(self):
        """Update existing employee"""
        if not self.editing_id:
            return
        
        if not self.validate_form():
            return
        
        try:
            success = self.db_manager.update_employee(
                employee_id=self.editing_id,
                nama=self.form_vars['nama'].get().strip(),
                nip=self.form_vars['nip'].get().strip(),
                posisi=self.form_vars['posisi'].get().strip(),
                kemampuan_teknik=float(self.form_vars['kemampuan_teknik'].get()),
                kualitas=float(self.form_vars['kualitas'].get()),
                presisi=float(self.form_vars['presisi'].get()),
                pelanggaran=float(self.form_vars['pelanggaran'].get()),
                absensi=float(self.form_vars['absensi'].get())
            )
            
            if success:
                messagebox.showinfo("Sukses", "Data karyawan berhasil diupdate!")
                self.clear_form()
                self.load_employees()
            else:
                messagebox.showerror("Error", "Gagal mengupdate data!")
                
        except Exception as e:
            messagebox.showerror("Error", f"Terjadi kesalahan: {str(e)}")
    
    def clear_form(self):
        """Clear form fields"""
        self.form_vars['nama'].set('')
        self.form_vars['nip'].set('')
        self.form_vars['posisi'].set('')
        self.form_vars['kemampuan_teknik'].set(8.0)
        self.form_vars['kualitas'].set(8.0)
        self.form_vars['presisi'].set(8.0)
        self.form_vars['pelanggaran'].set(8.0)
        self.form_vars['absensi'].set(8.0)
        
        self.editing_id = None
        self.save_button.configure(state='normal')
        self.update_button.configure(state='disabled')
    
    def load_employees(self):
        """Load employees data into table"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Load data
        employees = self.db_manager.get_all_employees()
        
        for emp in employees:
            self.tree.insert('', 'end', values=(
                emp['id'],
                emp['nama'],
                emp['nip'],
                emp['posisi'],
                f"{emp['kemampuan_teknik']:.1f}",
                f"{emp['kualitas']:.1f}",
                f"{emp['presisi']:.1f}",
                f"{emp['pelanggaran']:.1f}",
                f"{emp['absensi']:.1f}"
            ))
    
    def on_item_double_click(self, event):
        """Handle double click on table item"""
        self.edit_selected()
    
    def on_right_click(self, event):
        """Handle right click on table"""
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def edit_selected(self):
        """Edit selected employee"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih karyawan yang akan diedit!")
            return
        
        item = selection[0]
        values = self.tree.item(item, 'values')
        employee_id = int(values[0])
        
        # Get full employee data
        employee = self.db_manager.get_employee_by_id(employee_id)
        if not employee:
            messagebox.showerror("Error", "Data karyawan tidak ditemukan!")
            return
        
        # Fill form
        self.form_vars['nama'].set(employee['nama'])
        self.form_vars['nip'].set(employee['nip'])
        self.form_vars['posisi'].set(employee['posisi'])
        self.form_vars['kemampuan_teknik'].set(employee['kemampuan_teknik'])
        self.form_vars['kualitas'].set(employee['kualitas'])
        self.form_vars['presisi'].set(employee['presisi'])
        self.form_vars['pelanggaran'].set(employee['pelanggaran'])
        self.form_vars['absensi'].set(employee['absensi'])
        
        # Set editing mode
        self.editing_id = employee_id
        self.save_button.configure(state='disabled')
        self.update_button.configure(state='normal')
    
    def delete_selected(self):
        """Delete selected employee"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih karyawan yang akan dihapus!")
            return
        
        item = selection[0]
        values = self.tree.item(item, 'values')
        employee_id = int(values[0])
        employee_name = values[1]
        
        # Confirm deletion
        if messagebox.askyesno("Konfirmasi", f"Yakin ingin menghapus karyawan '{employee_name}'?"):
            try:
                success = self.db_manager.delete_employee(employee_id)
                if success:
                    messagebox.showinfo("Sukses", "Data karyawan berhasil dihapus!")
                    self.load_employees()
                else:
                    messagebox.showerror("Error", "Gagal menghapus data!")
            except Exception as e:
                messagebox.showerror("Error", f"Terjadi kesalahan: {str(e)}")
    
    def view_detail(self):
        """View employee detail"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih karyawan untuk melihat detail!")
            return
        
        item = selection[0]
        values = self.tree.item(item, 'values')
        
        detail_text = f"""
Detail Karyawan:

ID: {values[0]}
Nama: {values[1]}
NIP: {values[2]}
Posisi: {values[3]}

Penilaian Kriteria:
• C1 - Kemampuan Teknik: {values[4]}
• C2 - Kualitas: {values[5]}
• C3 - Presisi: {values[6]}
• C4 - Pelanggaran: {values[7]}
• C5 - Absensi: {values[8]}
        """
        
        messagebox.showinfo("Detail Karyawan", detail_text)
