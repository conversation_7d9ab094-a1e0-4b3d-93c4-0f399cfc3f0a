@echo off
title SPK TOPSIS - Copy Database with Sample Data
color 0B

echo.
echo ============================================================
echo 🗄️ SPK TOPSIS Enhanced v2.0 - Copy Database with Data
echo ============================================================
echo Author: <PERSON> Wibowo
echo NIM: ************
echo Kelas: 06TPLP003
echo ============================================================
echo.
echo This will copy the database file that already contains sample data.
echo.
echo 📊 Sample Data Includes:
echo    - 5 Criteria (C1-C5) with proper weights (100%)
echo    - 8 Employee Records with complete evaluation scores
echo    - User accounts: admin/admin123, operator/operator123
echo.

REM Check if source database exists
if exist "spk_karyawan_enhanced.db" (
    echo ✅ Found source database with sample data
    echo.
    
    REM Copy to portable folder if we're in root
    if exist "SPK_TOPSIS_Enhanced_v2.0_Portable" (
        echo 📁 Copying database to portable folder...
        copy "spk_karyawan_enhanced.db" "SPK_TOPSIS_Enhanced_v2.0_Portable\"
        echo ✅ Database copied to portable folder
    )
    
    echo ✅ Database with sample data is ready!
    echo.
    echo 🚀 You can now run the application:
    echo    - Double-click SPK_TOPSIS_Enhanced_v2.0.exe
    echo    - Or run: Run_SPK_TOPSIS.bat
    echo.
    echo 🔑 Login with:
    echo    👑 Admin: admin / admin123
    echo    ⚙️ Operator: operator / operator123
    
) else (
    echo ❌ Source database not found!
    echo.
    echo 💡 Trying to find database in other locations...
    
    REM Check in database folder
    if exist "database\spk_karyawan_enhanced.db" (
        echo ✅ Found database in database folder
        copy "database\spk_karyawan_enhanced.db" "."
        echo ✅ Database copied to current folder
    ) else (
        echo ❌ No database found with sample data
        echo.
        echo 🔧 Please run one of these:
        echo    1. Setup_Database.bat
        echo    2. python setup_database_with_sample_data.py
        echo.
    )
)

echo.
pause
