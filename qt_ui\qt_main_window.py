"""
Qt5 Main Window - Modern and Beautiful Main Interface
SPK Karyawan TOPSIS Enhanced v2.0
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QFrame, QStackedWidget,
                            QScrollArea, QGridLayout, QMessageBox, QMenuBar, QMenu,
                            QAction, QStatusBar, QSplitter, QTabWidget)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.enhanced_database_manager import EnhancedDatabaseManager
from core.enhanced_topsis_calculator import EnhancedTOPSISCalculator

class ModernMainWindow(QMainWindow):
    """Modern Qt5 Main Window with beautiful design"""

    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.db_manager = EnhancedDatabaseManager()
        self.db_manager.current_user = user_data
        self.topsis_calculator = EnhancedTOPSISCalculator(self.db_manager)

        self.setup_ui()
        self.apply_styles()
        self.load_dashboard()

    def setup_ui(self):
        """Setup the user interface"""
        # Window configuration
        role_icon = "👑" if self.user_data['role'] == 'admin' else "⚙️"
        role_name = "Administrator" if self.user_data['role'] == 'admin' else "Data Operator"
        self.setWindowTitle(f"🏆 SPK Karyawan TOPSIS Enhanced v2.0 - {role_icon} {self.user_data['full_name']} ({role_name})")

        self.setMinimumSize(1400, 900)
        self.showMaximized()

        # Create menu bar
        self.create_menu_bar()

        # Create status bar
        self.create_status_bar()

        # Main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)

        # Main layout
        main_layout = QHBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create sidebar
        self.create_sidebar(main_layout)

        # Create content area
        self.create_content_area(main_layout)

    def create_menu_bar(self):
        """Create modern menu bar"""
        menubar = self.menuBar()
        menubar.setObjectName("modernMenuBar")

        # File menu
        file_menu = menubar.addMenu("📁 File")
        file_menu.addAction("🏠 Dashboard", self.show_dashboard)
        file_menu.addSeparator()
        file_menu.addAction("🚪 Logout", self.logout)
        file_menu.addAction("❌ Exit", self.close)

        # Data menu
        data_menu = menubar.addMenu("📊 Data")
        if self.db_manager.has_permission('view_criteria'):
            data_menu.addAction("⚖️ Kelola Kriteria", self.show_criteria)
        if self.db_manager.has_permission('view_alternatives'):
            data_menu.addAction("👥 Kelola Alternatif", self.show_alternatives)

        # Analysis menu
        analysis_menu = menubar.addMenu("🧮 Analysis")
        if self.db_manager.has_permission('calculate_topsis'):
            analysis_menu.addAction("🧮 Hitung TOPSIS", self.calculate_topsis)
        if self.db_manager.has_permission('view_results'):
            analysis_menu.addAction("🏆 Lihat Hasil", self.show_results)
        if self.db_manager.has_permission('export_results'):
            analysis_menu.addAction("📤 Export Data", self.export_results)

        # Admin menu
        if self.user_data['role'] == 'admin':
            admin_menu = menubar.addMenu("👑 Admin")
            admin_menu.addAction("👥 User Management", self.show_user_management)
            admin_menu.addAction("📊 System Stats", self.show_system_stats)

        # Help menu
        help_menu = menubar.addMenu("❓ Help")
        help_menu.addAction("📖 User Guide", self.show_help)
        help_menu.addAction("ℹ️ About", self.show_about)

    def create_status_bar(self):
        """Create modern status bar"""
        status_bar = self.statusBar()
        status_bar.setObjectName("modernStatusBar")

        # User info
        user_info = f"👤 {self.user_data['full_name']} | 🔑 {self.user_data['role'].title()}"
        status_bar.showMessage(user_info)

        # Add permanent widgets
        self.status_label = QLabel("Ready")
        self.status_label.setObjectName("statusLabel")
        status_bar.addPermanentWidget(self.status_label)

    def create_sidebar(self, parent_layout):
        """Create modern sidebar navigation"""
        sidebar_frame = QFrame()
        sidebar_frame.setObjectName("sidebarFrame")
        sidebar_frame.setFixedWidth(280)
        parent_layout.addWidget(sidebar_frame)

        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # Sidebar header
        header_frame = QFrame()
        header_frame.setObjectName("sidebarHeader")
        header_frame.setFixedHeight(120)
        sidebar_layout.addWidget(header_frame)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 20, 20, 20)

        # App title
        app_title = QLabel("🏆 SPK TOPSIS")
        app_title.setObjectName("appTitle")
        app_title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(app_title)

        # App subtitle
        app_subtitle = QLabel("Enhanced v2.0")
        app_subtitle.setObjectName("appSubtitle")
        app_subtitle.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(app_subtitle)

        # Navigation buttons
        nav_scroll = QScrollArea()
        nav_scroll.setObjectName("navScroll")
        nav_scroll.setWidgetResizable(True)
        nav_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        sidebar_layout.addWidget(nav_scroll)

        nav_widget = QWidget()
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(10, 10, 10, 10)
        nav_layout.setSpacing(5)

        # Navigation items with permission check
        nav_items = [
            ("🏠 Dashboard", self.show_dashboard, 'view_dashboard'),
            ("⚖️ Kriteria", self.show_criteria, 'view_criteria'),
            ("👥 Alternatif", self.show_alternatives, 'view_alternatives'),
            ("🏆 Hasil", self.show_results, 'view_results'),
            ("🧮 Hitung TOPSIS", self.calculate_topsis, 'calculate_topsis'),
            ("📤 Export", self.export_results, 'export_results'),
        ]

        if self.user_data['role'] == 'admin':
            nav_items.extend([
                ("👥 Users", self.show_user_management, 'manage_users'),
                ("📊 Statistics", self.show_system_stats, 'view_logs'),
            ])

        self.nav_buttons = []
        for text, command, permission in nav_items:
            if self.db_manager.has_permission(permission):
                btn = QPushButton(text)
                btn.setObjectName("navButton")
                btn.setFixedHeight(50)
                btn.clicked.connect(command)
                nav_layout.addWidget(btn)
                self.nav_buttons.append(btn)

        nav_layout.addStretch()
        nav_scroll.setWidget(nav_widget)

        # User info at bottom
        user_frame = QFrame()
        user_frame.setObjectName("userFrame")
        user_frame.setFixedHeight(80)
        sidebar_layout.addWidget(user_frame)

        user_layout = QVBoxLayout(user_frame)
        user_layout.setContentsMargins(15, 10, 15, 10)

        role_icon = "👑" if self.user_data['role'] == 'admin' else "⚙️"
        user_name = QLabel(f"{role_icon} {self.user_data['full_name']}")
        user_name.setObjectName("userName")
        user_layout.addWidget(user_name)

        user_role = QLabel(f"🔑 {self.user_data['role'].title()}")
        user_role.setObjectName("userRole")
        user_layout.addWidget(user_role)

    def create_content_area(self, parent_layout):
        """Create main content area"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        parent_layout.addWidget(content_frame)

        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(0)

        # Content header
        self.content_header = QLabel("🏠 Dashboard")
        self.content_header.setObjectName("contentHeader")
        content_layout.addWidget(self.content_header)

        # Content area
        self.content_stack = QStackedWidget()
        self.content_stack.setObjectName("contentStack")
        content_layout.addWidget(self.content_stack)

        # Create content pages
        self.create_content_pages()

    def create_content_pages(self):
        """Create different content pages"""
        # Dashboard page
        self.dashboard_page = self.create_dashboard_page()
        self.content_stack.addWidget(self.dashboard_page)

        # Criteria page
        if self.db_manager.has_permission('view_criteria'):
            self.criteria_page = self.create_criteria_page()
            self.content_stack.addWidget(self.criteria_page)

        # Alternatives page
        if self.db_manager.has_permission('view_alternatives'):
            self.alternatives_page = self.create_alternatives_page()
            self.content_stack.addWidget(self.alternatives_page)

        # Results page
        if self.db_manager.has_permission('view_results'):
            self.results_page = self.create_results_page()
            self.content_stack.addWidget(self.results_page)

    def create_dashboard_page(self):
        """Create dashboard page"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(0, 20, 0, 0)

        # Stats cards
        stats_frame = QFrame()
        stats_frame.setObjectName("statsFrame")
        layout.addWidget(stats_frame)

        stats_layout = QGridLayout(stats_frame)
        stats_layout.setSpacing(20)

        # Get statistics
        try:
            criteria_count = len(self.db_manager.get_all_criteria())
            alternatives_count = len(self.db_manager.get_all_alternatives())
            results_count = len(self.db_manager.get_latest_topsis_results())
            users_count = len(self.db_manager.get_all_users()) if self.user_data['role'] == 'admin' else 0
        except:
            criteria_count = alternatives_count = results_count = users_count = 0

        # Create stat cards
        self.create_stat_card(stats_layout, "⚖️ Kriteria", str(criteria_count), "#3b82f6", 0, 0)
        self.create_stat_card(stats_layout, "👥 Alternatif", str(alternatives_count), "#10b981", 0, 1)
        self.create_stat_card(stats_layout, "🏆 Hasil TOPSIS", str(results_count), "#f59e0b", 1, 0)
        if self.user_data['role'] == 'admin':
            self.create_stat_card(stats_layout, "👤 Users", str(users_count), "#ef4444", 1, 1)

        layout.addStretch()
        return page

    def create_stat_card(self, parent_layout, title, value, color, row, col):
        """Create a statistics card"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(120)
        parent_layout.addWidget(card, row, col)

        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(20, 15, 20, 15)

        # Title
        title_label = QLabel(title)
        title_label.setObjectName("statTitle")
        card_layout.addWidget(title_label)

        # Value
        value_label = QLabel(value)
        value_label.setObjectName("statValue")
        card_layout.addWidget(value_label)

        # Apply color
        card.setStyleSheet(f"""
            #statCard {{
                background-color: white;
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                border-left: 4px solid {color};
            }}
            #statCard:hover {{
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }}
        """)

    def create_criteria_page(self):
        """Create criteria management page"""
        page = QWidget()
        layout = QVBoxLayout(page)

        label = QLabel("⚖️ Kriteria Management - Coming Soon")
        label.setAlignment(Qt.AlignCenter)
        label.setObjectName("placeholderLabel")
        layout.addWidget(label)

        return page

    def create_alternatives_page(self):
        """Create alternatives management page"""
        page = QWidget()
        layout = QVBoxLayout(page)

        label = QLabel("👥 Alternatif Management - Coming Soon")
        label.setAlignment(Qt.AlignCenter)
        label.setObjectName("placeholderLabel")
        layout.addWidget(label)

        return page

    def create_results_page(self):
        """Create results page"""
        page = QWidget()
        layout = QVBoxLayout(page)

        label = QLabel("🏆 TOPSIS Results - Coming Soon")
        label.setAlignment(Qt.AlignCenter)
        label.setObjectName("placeholderLabel")
        layout.addWidget(label)

        return page

    # Navigation methods
    def show_dashboard(self):
        """Show dashboard"""
        self.content_header.setText("🏠 Dashboard")
        self.content_stack.setCurrentIndex(0)
        self.update_status("Dashboard loaded")

    def show_criteria(self):
        """Show criteria management"""
        self.content_header.setText("⚖️ Kelola Kriteria")
        if hasattr(self, 'criteria_page'):
            self.content_stack.setCurrentWidget(self.criteria_page)
        self.update_status("Criteria management")

    def show_alternatives(self):
        """Show alternatives management"""
        self.content_header.setText("👥 Kelola Alternatif")
        if hasattr(self, 'alternatives_page'):
            self.content_stack.setCurrentWidget(self.alternatives_page)
        self.update_status("Alternatives management")

    def show_results(self):
        """Show results"""
        self.content_header.setText("🏆 Hasil TOPSIS")
        if hasattr(self, 'results_page'):
            self.content_stack.setCurrentWidget(self.results_page)
        self.update_status("TOPSIS results")

    def calculate_topsis(self):
        """Calculate TOPSIS"""
        if not self.db_manager.has_permission('calculate_topsis'):
            self.show_permission_error()
            return

        try:
            self.update_status("Calculating TOPSIS...")
            results = self.topsis_calculator.calculate()

            if results:
                QMessageBox.information(self, "✅ Success",
                                      f"TOPSIS calculation completed!\n\n"
                                      f"📊 {len(results)} results generated\n"
                                      f"🏆 Check Results page for details")
                self.update_status(f"TOPSIS calculated - {len(results)} results")
            else:
                QMessageBox.warning(self, "⚠️ Warning",
                                  "No results generated. Please check your data.")
                self.update_status("TOPSIS calculation failed")
        except Exception as e:
            QMessageBox.critical(self, "❌ Error", f"TOPSIS calculation failed:\n{str(e)}")
            self.update_status("TOPSIS calculation error")

    def export_results(self):
        """Export results"""
        if not self.db_manager.has_permission('export_results'):
            self.show_permission_error()
            return

        QMessageBox.information(self, "📤 Export", "Export functionality - Coming Soon")
        self.update_status("Export requested")

    def show_user_management(self):
        """Show user management"""
        QMessageBox.information(self, "👥 Users", "User Management - Coming Soon")
        self.update_status("User management")

    def show_system_stats(self):
        """Show system statistics"""
        QMessageBox.information(self, "📊 Stats", "System Statistics - Coming Soon")
        self.update_status("System statistics")

    def show_help(self):
        """Show help"""
        QMessageBox.information(self, "📖 Help",
                              "SPK Karyawan TOPSIS Enhanced v2.0\n\n"
                              "Quick Guide:\n"
                              "1. 🏠 Dashboard - Overview sistem\n"
                              "2. ⚖️ Kriteria - Kelola kriteria penilaian\n"
                              "3. 👥 Alternatif - Kelola data karyawan\n"
                              "4. 🧮 Hitung - Jalankan TOPSIS\n"
                              "5. 🏆 Hasil - Lihat ranking\n"
                              "6. 📤 Export - Download laporan")

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(self, "ℹ️ About",
                         "🏆 SPK Karyawan TOPSIS Enhanced v2.0\n\n"
                         "Sistem Pendukung Keputusan untuk Pengangkatan\n"
                         "Karyawan Tetap menggunakan metode TOPSIS\n\n"
                         "👨‍💻 Author: Muhammad Bayu Prasetyo Wibowo\n"
                         "🎓 NIM: ************\n"
                         "📚 Kelas: 06TPLP003\n\n"
                         "🛠️ Built with PyQt5 & Python\n"
                         "📅 December 2024")

    def show_permission_error(self):
        """Show permission error"""
        QMessageBox.warning(self, "🔒 Access Denied",
                          f"Your role ({self.user_data['role']}) doesn't have permission for this action.\n\n"
                          f"Please contact an Administrator for access.")

    def update_status(self, message):
        """Update status bar message"""
        self.status_label.setText(message)

        # Auto clear after 3 seconds
        QTimer.singleShot(3000, lambda: self.status_label.setText("Ready"))

    def load_dashboard(self):
        """Load dashboard data"""
        self.show_dashboard()

    def logout(self):
        """Logout user"""
        reply = QMessageBox.question(self, "🚪 Logout",
                                   "Are you sure you want to logout?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.close()

    def closeEvent(self, event):
        """Handle window close event"""
        reply = QMessageBox.question(self, "❌ Exit",
                                   "Are you sure you want to exit?",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()

    def apply_styles(self):
        """Apply modern styles to the window"""
        self.setStyleSheet("""
            /* Main Window */
            QMainWindow {
                background-color: #f8fafc;
            }

            /* Menu Bar */
            #modernMenuBar {
                background-color: white;
                border-bottom: 1px solid #e5e7eb;
                padding: 5px;
            }

            #modernMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                margin: 2px;
                border-radius: 6px;
            }

            #modernMenuBar::item:selected {
                background-color: #f3f4f6;
            }

            /* Status Bar */
            #modernStatusBar {
                background-color: white;
                border-top: 1px solid #e5e7eb;
                padding: 5px;
            }

            #statusLabel {
                color: #6b7280;
                font-size: 12px;
                padding: 0px 10px;
            }

            /* Sidebar */
            #sidebarFrame {
                background-color: #1f2937;
                border-right: 1px solid #374151;
            }

            #sidebarHeader {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #1d4ed8);
            }

            #appTitle {
                color: white;
                font-size: 18px;
                font-weight: bold;
            }

            #appSubtitle {
                color: #dbeafe;
                font-size: 12px;
            }

            #navScroll {
                background-color: transparent;
                border: none;
            }

            #navButton {
                background-color: transparent;
                color: #d1d5db;
                border: none;
                text-align: left;
                padding: 12px 15px;
                font-size: 14px;
                border-radius: 8px;
                margin: 2px 0px;
            }

            #navButton:hover {
                background-color: #374151;
                color: white;
            }

            #navButton:pressed {
                background-color: #4b5563;
            }

            #userFrame {
                background-color: #111827;
                border-top: 1px solid #374151;
            }

            #userName {
                color: white;
                font-size: 13px;
                font-weight: bold;
            }

            #userRole {
                color: #9ca3af;
                font-size: 11px;
            }

            /* Content Area */
            #contentFrame {
                background-color: #f8fafc;
            }

            #contentHeader {
                color: #1f2937;
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #e5e7eb;
            }

            #contentStack {
                background-color: transparent;
            }

            /* Stats Frame */
            #statsFrame {
                background-color: transparent;
            }

            #statTitle {
                color: #6b7280;
                font-size: 14px;
                font-weight: 500;
            }

            #statValue {
                color: #1f2937;
                font-size: 32px;
                font-weight: bold;
            }

            /* Placeholder */
            #placeholderLabel {
                color: #6b7280;
                font-size: 18px;
                font-style: italic;
            }
        """)

if __name__ == "__main__":
    # Test with dummy user data
    app = QApplication(sys.argv)

    user_data = {
        'id': 1,
        'username': 'admin',
        'full_name': 'Administrator',
        'role': 'admin'
    }

    window = ModernMainWindow(user_data)
    window.show()

    sys.exit(app.exec_())