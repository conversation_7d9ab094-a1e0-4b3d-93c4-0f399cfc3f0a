"""
Modern Login Window using CustomTkinter
SP<PERSON>wan TOPSIS Enhanced v2.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import customtkinter as ctk
    CTK_AVAILABLE = True
except ImportError:
    CTK_AVAILABLE = False
    print("⚠️ CustomTkinter not available, using standard tkinter")

from database.enhanced_database_manager import EnhancedDatabaseManager

class ModernLoginWindow:
    """Modern Login Window with CustomTkinter"""
    
    def __init__(self):
        self.db_manager = EnhancedDatabaseManager()
        self.user_data = None
        self.login_successful = False
        
        if CTK_AVAILABLE:
            self.setup_ctk_ui()
        else:
            self.setup_tk_ui()
    
    def setup_ctk_ui(self):
        """Setup CustomTkinter UI"""
        # Set appearance mode and color theme
        ctk.set_appearance_mode("light")  # "light" or "dark"
        ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("🏢 SPK Karyawan TOPSIS - Login")
        self.root.geometry("700x900")  # Optimal size
        self.root.resizable(True, True)  # Allow resizing
        self.root.minsize(600, 700)  # Minimum size
        self.root.maxsize(1200, 1000)  # Maximum size

        # Configure window attributes for proper controls
        self.root.wm_attributes("-topmost", False)
        self.root.wm_attributes("-disabled", False)
        self.root.wm_attributes("-toolwindow", False)  # Enable minimize/maximize buttons

        # Set window state and protocol
        self.root.wm_state('normal')
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Force proper window behavior
        self.root.lift()  # Bring to front
        self.root.focus_force()  # Give focus
        
        # Center window
        self.center_window()
        
        # Configure root grid
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        # Create scrollable frame
        self.scrollable_frame = ctk.CTkScrollableFrame(self.root, corner_radius=20)
        self.scrollable_frame.grid(row=0, column=0, sticky="nsew", padx=30, pady=30)

        # Configure scrollable frame grid
        self.scrollable_frame.grid_columnconfigure(0, weight=1)

        # Create main frame inside scrollable frame
        self.main_frame = ctk.CTkFrame(self.scrollable_frame, corner_radius=0, fg_color="transparent")
        self.main_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)

        # Center window
        self.center_window()

        # Create header
        self.create_ctk_header()

        # Create form
        self.create_ctk_form()

        # Create footer
        self.create_ctk_footer()

        # Bind Enter key
        self.root.bind('<Return>', lambda e: self.handle_login())

        # Bind window resize event for responsive layout
        self.root.bind('<Configure>', self.on_window_resize)
    
    def setup_tk_ui(self):
        """Setup standard Tkinter UI as fallback"""
        self.root = tk.Tk()
        self.root.title("🏢 SPK Karyawan TOPSIS - Login")
        self.root.geometry("700x900")  # Optimal size
        self.root.resizable(True, True)  # Allow resizing
        self.root.minsize(600, 700)  # Minimum size
        self.root.maxsize(1200, 1000)  # Maximum size
        self.root.configure(bg='#f0f0f0')
        
        # Center window
        self.center_window()
        
        # Create main frame
        self.main_frame = tk.Frame(self.root, bg='white', relief='solid', bd=1)
        self.main_frame.pack(fill="both", expand=True, padx=30, pady=30)
        
        # Create header
        self.create_tk_header()
        
        # Create form
        self.create_tk_form()
        
        # Create footer
        self.create_tk_footer()
        
        # Bind Enter key
        self.root.bind('<Return>', lambda e: self.handle_login())
    
    def center_window(self):
        """Center window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def on_window_resize(self, event):
        """Handle window resize events for responsive layout"""
        # Only handle resize events for the main window, not child widgets
        if event.widget == self.root:
            # Update scrollable frame configuration if needed
            pass

    def on_closing(self):
        """Handle window closing event"""
        self.login_successful = False
        self.user_data = None
        self.root.destroy()
    
    def create_ctk_header(self):
        """Create CustomTkinter header"""
        # Header frame
        header_frame = ctk.CTkFrame(self.main_frame, height=160, corner_radius=15)
        header_frame.pack(fill="x", padx=20, pady=(15, 10))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = ctk.CTkLabel(header_frame, 
                                  text="🏆 SPK TOPSIS",
                                  font=ctk.CTkFont(size=32, weight="bold"))
        title_label.pack(pady=(30, 5))
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(header_frame,
                                     text="Sistem Pendukung Keputusan Enhanced v2.0",
                                     font=ctk.CTkFont(size=14))
        subtitle_label.pack(pady=2)
        
        # Description
        desc_label = ctk.CTkLabel(header_frame,
                                 text="Pengangkatan Karyawan Tetap - Metode TOPSIS",
                                 font=ctk.CTkFont(size=12))
        desc_label.pack(pady=2)
    
    def create_ctk_form(self):
        """Create CustomTkinter form"""
        # Form frame
        form_frame = ctk.CTkFrame(self.main_frame, corner_radius=15)
        form_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Login title
        login_title = ctk.CTkLabel(form_frame,
                                  text="🔐 Login to Continue",
                                  font=ctk.CTkFont(size=20, weight="bold"))
        login_title.pack(pady=(30, 20))
        
        # Username section
        username_label = ctk.CTkLabel(form_frame,
                                     text="👤 Username",
                                     font=ctk.CTkFont(size=14, weight="bold"))
        username_label.pack(anchor="w", padx=40, pady=(10, 5))
        
        self.username_entry = ctk.CTkEntry(form_frame,
                                          placeholder_text="Enter your username",
                                          height=45,
                                          font=ctk.CTkFont(size=14))
        self.username_entry.pack(fill="x", padx=40, pady=(0, 15))
        
        # Password section
        password_label = ctk.CTkLabel(form_frame,
                                     text="🔒 Password",
                                     font=ctk.CTkFont(size=14, weight="bold"))
        password_label.pack(anchor="w", padx=40, pady=(0, 5))
        
        self.password_entry = ctk.CTkEntry(form_frame,
                                          placeholder_text="Enter your password",
                                          show="*",
                                          height=45,
                                          font=ctk.CTkFont(size=14))
        self.password_entry.pack(fill="x", padx=40, pady=(0, 15))
        
        # Show password checkbox
        self.show_password_var = ctk.BooleanVar()
        self.show_password_cb = ctk.CTkCheckBox(form_frame,
                                               text="👁️ Show Password",
                                               variable=self.show_password_var,
                                               command=self.toggle_password_visibility,
                                               font=ctk.CTkFont(size=12))
        self.show_password_cb.pack(anchor="w", padx=40, pady=(0, 20))
        
        # Login button
        self.login_btn = ctk.CTkButton(form_frame,
                                      text="🚀 Login",
                                      height=50,
                                      font=ctk.CTkFont(size=16, weight="bold"),
                                      command=self.handle_login)
        self.login_btn.pack(fill="x", padx=40, pady=(0, 30))
        
        # Set focus
        self.username_entry.focus()
    
    def create_ctk_footer(self):
        """Create CustomTkinter footer"""
        # Footer frame
        footer_frame = ctk.CTkFrame(self.main_frame, height=280, corner_radius=15)
        footer_frame.pack(fill="x", padx=20, pady=(10, 20))
        footer_frame.pack_propagate(False)
        
        # Credentials title
        creds_title = ctk.CTkLabel(footer_frame,
                                  text="💡 Demo Credentials:",
                                  font=ctk.CTkFont(size=14, weight="bold"))
        creds_title.pack(pady=(20, 12))
        
        # Admin credentials
        admin_frame = ctk.CTkFrame(footer_frame, corner_radius=10)
        admin_frame.pack(fill="x", padx=25, pady=6)

        admin_title = ctk.CTkLabel(admin_frame,
                                  text="👑 Administrator (Full Access)",
                                  font=ctk.CTkFont(size=12, weight="bold"))
        admin_title.pack(pady=(10, 3))

        admin_creds = ctk.CTkLabel(admin_frame,
                                  text="Username: admin | Password: admin123",
                                  font=ctk.CTkFont(family="Consolas", size=11))
        admin_creds.pack(pady=(0, 10))

        # Operator credentials
        operator_frame = ctk.CTkFrame(footer_frame, corner_radius=10)
        operator_frame.pack(fill="x", padx=25, pady=6)

        operator_title = ctk.CTkLabel(operator_frame,
                                     text="⚙️ Data Operator (Input Only)",
                                     font=ctk.CTkFont(size=12, weight="bold"))
        operator_title.pack(pady=(10, 3))

        operator_creds = ctk.CTkLabel(operator_frame,
                                     text="Username: operator | Password: operator123",
                                     font=ctk.CTkFont(family="Consolas", size=11))
        operator_creds.pack(pady=(0, 10))
        
        # Author info
        author_label = ctk.CTkLabel(footer_frame,
                                   text="© 2024 Muhammad Bayu Prasetyo Wibowo - 211011450583 - 06TPLP003",
                                   font=ctk.CTkFont(size=10))
        author_label.pack(pady=(12, 15))
    
    def create_tk_header(self):
        """Create standard Tkinter header"""
        header_frame = tk.Frame(self.main_frame, bg='#3b82f6', height=180)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame,
                              text="🏆 SPK TOPSIS",
                              font=('Segoe UI', 24, 'bold'),
                              bg='#3b82f6', fg='white')
        title_label.pack(pady=(30, 5))
        
        subtitle_label = tk.Label(header_frame,
                                 text="Sistem Pendukung Keputusan Enhanced v2.0",
                                 font=('Segoe UI', 12),
                                 bg='#3b82f6', fg='#dbeafe')
        subtitle_label.pack(pady=2)
    
    def create_tk_form(self):
        """Create standard Tkinter form"""
        form_frame = tk.Frame(self.main_frame, bg='white')
        form_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        login_title = tk.Label(form_frame,
                              text="🔐 Login to Continue",
                              font=('Segoe UI', 16, 'bold'),
                              bg='white', fg='#1f2937')
        login_title.pack(pady=(30, 20))
        
        # Username
        username_label = tk.Label(form_frame,
                                 text="👤 Username",
                                 font=('Segoe UI', 12, 'bold'),
                                 bg='white', fg='#374151')
        username_label.pack(anchor="w", padx=40, pady=(10, 5))
        
        self.username_entry = tk.Entry(form_frame,
                                      font=('Segoe UI', 12),
                                      relief='solid', bd=1)
        self.username_entry.pack(fill="x", padx=40, pady=(0, 15), ipady=8)
        
        # Password
        password_label = tk.Label(form_frame,
                                 text="🔒 Password",
                                 font=('Segoe UI', 12, 'bold'),
                                 bg='white', fg='#374151')
        password_label.pack(anchor="w", padx=40, pady=(0, 5))
        
        self.password_entry = tk.Entry(form_frame,
                                      show="*",
                                      font=('Segoe UI', 12),
                                      relief='solid', bd=1)
        self.password_entry.pack(fill="x", padx=40, pady=(0, 15), ipady=8)
        
        # Login button
        self.login_btn = tk.Button(form_frame,
                                  text="🚀 Login",
                                  font=('Segoe UI', 14, 'bold'),
                                  bg='#3b82f6', fg='white',
                                  relief='flat', bd=0,
                                  command=self.handle_login)
        self.login_btn.pack(fill="x", padx=40, pady=(20, 30), ipady=12)
        
        self.username_entry.focus()
    
    def create_tk_footer(self):
        """Create standard Tkinter footer"""
        footer_frame = tk.Frame(self.main_frame, bg='#f8fafc', height=150)
        footer_frame.pack(fill="x", padx=20, pady=(10, 20))
        footer_frame.pack_propagate(False)
        
        creds_title = tk.Label(footer_frame,
                              text="💡 Demo Credentials:",
                              font=('Segoe UI', 12, 'bold'),
                              bg='#f8fafc', fg='#374151')
        creds_title.pack(pady=(20, 10))
        
        admin_text = tk.Label(footer_frame,
                             text="👑 Admin: admin / admin123",
                             font=('Consolas', 10),
                             bg='#f8fafc', fg='#6b7280')
        admin_text.pack(pady=2)
        
        operator_text = tk.Label(footer_frame,
                                text="⚙️ Operator: operator / operator123",
                                font=('Consolas', 10),
                                bg='#f8fafc', fg='#6b7280')
        operator_text.pack(pady=2)
    
    def toggle_password_visibility(self):
        """Toggle password visibility"""
        if CTK_AVAILABLE:
            if self.show_password_var.get():
                self.password_entry.configure(show="")
            else:
                self.password_entry.configure(show="*")
    
    def handle_login(self):
        """Handle login attempt"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            self.show_error("❌ Login Failed", "Please enter both username and password!")
            return
        
        # Authenticate user
        user_data = self.db_manager.authenticate_user(username, password)
        
        if user_data:
            self.user_data = user_data
            self.login_successful = True
            
            # Show success message
            role_icon = "👑" if user_data['role'] == 'admin' else "⚙️"
            role_name = "Administrator" if user_data['role'] == 'admin' else "Data Operator"
            
            self.show_success("✅ Login Successful!", 
                            f"Welcome, {user_data['full_name']}!\n\n"
                            f"{role_icon} Role: {role_name}\n"
                            f"🚀 Starting SPK Application...")
            
            self.root.destroy()
        else:
            self.show_error("❌ Login Failed",
                          "Invalid username or password!\n\n"
                          "💡 Try these credentials:\n\n"
                          "👑 Admin: admin / admin123\n"
                          "⚙️ Operator: operator / operator123")
    
    def show_success(self, title, message):
        """Show success message"""
        messagebox.showinfo(title, message)
    
    def show_error(self, title, message):
        """Show error message"""
        messagebox.showerror(title, message)
    
    def run(self):
        """Run the login window"""
        self.root.mainloop()
        return self.user_data if self.login_successful else None

def show_modern_login():
    """Show modern login window and return user data if successful"""
    login_window = ModernLoginWindow()
    return login_window.run()

if __name__ == "__main__":
    user_data = show_modern_login()
    if user_data:
        print(f"Login successful: {user_data}")
    else:
        print("Login cancelled or failed")
