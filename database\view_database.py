#!/usr/bin/env python3
"""
SPK TOPSIS Enhanced v2.0 - Database Viewer
Author: <PERSON>owo
NIM: 211011450583
Kelas: 06TPLP003

Script untuk melihat isi database SQLite dengan format yang rapi
"""

import sqlite3
import os
from datetime import datetime

class DatabaseViewer:
    def __init__(self, db_path='spk_karyawan_enhanced.db'):
        self.db_path = db_path
        self.conn = None
    
    def connect(self):
        """Koneksi ke database"""
        try:
            if not os.path.exists(self.db_path):
                print("❌ Database tidak ditemukan!")
                print(f"💡 Pastikan file {self.db_path} ada di folder ini")
                return False
            
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"❌ Error koneksi database: {e}")
            return False
    
    def close(self):
        """Tutup koneksi database"""
        if self.conn:
            self.conn.close()
    
    def get_database_info(self):
        """Informasi umum database"""
        try:
            size = os.path.getsize(self.db_path)
            modified = datetime.fromtimestamp(os.path.getmtime(self.db_path))
            
            print("="*60)
            print("🗄️ INFORMASI DATABASE")
            print("="*60)
            print(f"📁 File: {self.db_path}")
            print(f"📏 Ukuran: {size:,} bytes ({size/1024:.1f} KB)")
            print(f"📅 Modified: {modified.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
            
        except Exception as e:
            print(f"❌ Error mendapatkan info database: {e}")
    
    def view_tables(self):
        """Lihat semua tabel"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
            tables = cursor.fetchall()
            
            print("📊 TABEL DALAM DATABASE:")
            for i, table in enumerate(tables, 1):
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   {i}. {table_name} ({count} records)")
            print()
            
        except Exception as e:
            print(f"❌ Error melihat tabel: {e}")
    
    def view_users(self):
        """Lihat data users"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT id, username, role, full_name, created_at FROM users ORDER BY id")
            users = cursor.fetchall()
            
            print("👤 USERS:")
            print("   ID | Username | Role      | Full Name        | Created")
            print("   " + "-"*65)
            for user in users:
                created = user['created_at'][:19] if user['created_at'] else 'N/A'
                full_name = user['full_name'] or 'N/A'
                print(f"   {user['id']:2} | {user['username']:<8} | {user['role']:<9} | {full_name:<16} | {created}")
            print()
            
        except Exception as e:
            print(f"❌ Error melihat users: {e}")
    
    def view_criteria(self):
        """Lihat data kriteria"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT code, name, weight*100 as bobot_persen, type, description 
                FROM dynamic_criteria 
                WHERE is_active = 1
                ORDER BY code
            """)
            criteria = cursor.fetchall()
            
            print("📊 KRITERIA:")
            print("   Kode | Nama              | Bobot | Jenis   | Deskripsi")
            print("   " + "-"*80)
            for crit in criteria:
                desc = (crit['description'][:30] + '...') if crit['description'] and len(crit['description']) > 30 else (crit['description'] or 'N/A')
                print(f"   {crit['code']:<4} | {crit['name']:<17} | {crit['bobot_persen']:5.0f}% | {crit['type']:<7} | {desc}")
            
            # Total bobot
            cursor.execute("SELECT SUM(weight)*100 FROM dynamic_criteria WHERE is_active = 1")
            total_weight = cursor.fetchone()[0] or 0
            print("   " + "-"*80)
            print(f"   TOTAL BOBOT: {total_weight:.0f}%")
            print()
            
        except Exception as e:
            print(f"❌ Error melihat kriteria: {e}")
    
    def view_alternatives(self):
        """Lihat data alternatif/karyawan"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT id, name, position, employee_id, department, description 
                FROM dynamic_alternatives 
                WHERE is_active = 1
                ORDER BY name
            """)
            alternatives = cursor.fetchall()
            
            print("👥 KARYAWAN/ALTERNATIF:")
            print("   ID | Nama   | Posisi   | Employee ID | Department | Deskripsi")
            print("   " + "-"*75)
            for alt in alternatives:
                desc = (alt['description'][:20] + '...') if alt['description'] and len(alt['description']) > 20 else (alt['description'] or 'N/A')
                emp_id = alt['employee_id'] or 'N/A'
                dept = alt['department'] or 'N/A'
                print(f"   {alt['id']:2} | {alt['name']:<6} | {alt['position']:<8} | {emp_id:<11} | {dept:<10} | {desc}")
            print()
            
        except Exception as e:
            print(f"❌ Error melihat alternatif: {e}")
    
    def view_evaluation_matrix(self):
        """Lihat matriks evaluasi"""
        try:
            cursor = self.conn.cursor()
            
            # Get criteria codes
            cursor.execute("SELECT code FROM dynamic_criteria WHERE is_active = 1 ORDER BY code")
            criteria_codes = [row[0] for row in cursor.fetchall()]
            
            # Get evaluation matrix
            cursor.execute("""
                SELECT 
                    a.name,
                    MAX(CASE WHEN c.code = 'C1' THEN e.score END) as C1,
                    MAX(CASE WHEN c.code = 'C2' THEN e.score END) as C2,
                    MAX(CASE WHEN c.code = 'C3' THEN e.score END) as C3,
                    MAX(CASE WHEN c.code = 'C4' THEN e.score END) as C4,
                    MAX(CASE WHEN c.code = 'C5' THEN e.score END) as C5
                FROM dynamic_alternatives a
                LEFT JOIN dynamic_evaluations e ON a.id = e.alternative_id
                LEFT JOIN dynamic_criteria c ON e.criteria_id = c.id
                WHERE a.is_active = 1
                GROUP BY a.id, a.name
                ORDER BY a.name
            """)
            evaluations = cursor.fetchall()
            
            print("📝 MATRIKS EVALUASI:")
            print("   Karyawan | C1 | C2 | C3 | C4 | C5")
            print("   " + "-"*35)
            for eval_row in evaluations:
                name = eval_row['name']
                scores = [eval_row['C1'], eval_row['C2'], eval_row['C3'], eval_row['C4'], eval_row['C5']]
                score_str = " | ".join([f"{score:2.0f}" if score is not None else " -" for score in scores])
                print(f"   {name:<8} | {score_str}")
            
            # Count evaluations
            cursor.execute("SELECT COUNT(*) FROM dynamic_evaluations")
            total_evals = cursor.fetchone()[0]
            print("   " + "-"*35)
            print(f"   Total evaluasi: {total_evals}")
            print()
            
        except Exception as e:
            print(f"❌ Error melihat matriks evaluasi: {e}")
    
    def view_statistics(self):
        """Lihat statistik database"""
        try:
            cursor = self.conn.cursor()
            
            print("📈 STATISTIK DATABASE:")
            
            # Count records in each table
            tables = ['users', 'dynamic_criteria', 'dynamic_alternatives', 'dynamic_evaluations', 'topsis_results']
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    table_display = table.replace('dynamic_', '').replace('_', ' ').title()
                    print(f"   {table_display:<15}: {count:3} records")
                except:
                    table_display = table.replace('dynamic_', '').replace('_', ' ').title()
                    print(f"   {table_display:<15}: N/A")
            
            # Check data completeness
            try:
                cursor.execute("""
                    SELECT 
                        COUNT(DISTINCT a.id) as total_alternatives,
                        COUNT(DISTINCT c.id) as total_criteria,
                        COUNT(e.id) as total_evaluations
                    FROM dynamic_alternatives a
                    CROSS JOIN dynamic_criteria c
                    LEFT JOIN dynamic_evaluations e ON a.id = e.alternative_id AND c.id = e.criteria_id
                    WHERE a.is_active = 1 AND c.is_active = 1
                """)
                stats = cursor.fetchone()
                
                if stats:
                    expected = stats['total_alternatives'] * stats['total_criteria']
                    actual = stats['total_evaluations']
                    completeness = (actual / expected * 100) if expected > 0 else 0
                    print(f"   Data Completeness: {completeness:.1f}% ({actual}/{expected})")
            except:
                pass
            
            print()
            
        except Exception as e:
            print(f"❌ Error melihat statistik: {e}")
    
    def view_all(self):
        """Lihat semua data"""
        if not self.connect():
            return
        
        try:
            self.get_database_info()
            self.view_tables()
            self.view_users()
            self.view_criteria()
            self.view_alternatives()
            self.view_evaluation_matrix()
            self.view_statistics()
            
            print("✅ Selesai melihat semua data database")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            self.close()

def main():
    """Main function"""
    print("="*60)
    print("🔍 SPK TOPSIS Enhanced v2.0 - Database Viewer")
    print("="*60)
    print("Author: Muhammad Bayu Prasetyo Wibowo")
    print("NIM: 211011450583")
    print("Kelas: 06TPLP003")
    print("="*60)
    print()
    
    viewer = DatabaseViewer()
    
    while True:
        print("🎯 PILIH OPSI:")
        print("   1. Lihat semua data")
        print("   2. Lihat users")
        print("   3. Lihat kriteria")
        print("   4. Lihat karyawan")
        print("   5. Lihat matriks evaluasi")
        print("   6. Lihat statistik")
        print("   7. Info database")
        print("   8. Keluar")
        print()
        
        choice = input("Pilih opsi (1-8): ").strip()
        print()
        
        if choice == '1':
            viewer.view_all()
        elif choice == '2':
            if viewer.connect():
                viewer.view_users()
                viewer.close()
        elif choice == '3':
            if viewer.connect():
                viewer.view_criteria()
                viewer.close()
        elif choice == '4':
            if viewer.connect():
                viewer.view_alternatives()
                viewer.close()
        elif choice == '5':
            if viewer.connect():
                viewer.view_evaluation_matrix()
                viewer.close()
        elif choice == '6':
            if viewer.connect():
                viewer.view_statistics()
                viewer.close()
        elif choice == '7':
            viewer.get_database_info()
        elif choice == '8':
            print("👋 Terima kasih!")
            break
        else:
            print("❌ Pilihan tidak valid!")
        
        print()

if __name__ == "__main__":
    main()
