import tkinter as tk
from tkinter import ttk, messagebox
from typing import List, Dict, Any
import sys
import os

# Tambahkan path parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import TOPSISResult

class ResultWindow:
    def __init__(self, parent, results: List[Dict[str, Any]]):
        self.parent = parent
        self.results = [TOPSISResult.from_dict(result) for result in results]
        
        # Create window
        self.window = tk.Toplevel(parent)
        self.window.title("🏆 Hasil Perhitungan TOPSIS")
        self.window.geometry("1400x900")
        self.window.resizable(True, True)
        self.window.transient(parent)
        self.window.configure(bg='#f8f9fa')
        
        self.create_widgets()
        self.center_window()
    
    def create_widgets(self):
        """Create result window widgets"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Modern title header
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        title_frame.columnconfigure(1, weight=1)

        # Title with icon
        title_label = ttk.Label(title_frame, text="🏆 Hasil Perhitungan TOPSIS",
                               font=('Segoe UI', 18, 'bold'), foreground='#2c3e50')
        title_label.grid(row=0, column=0, sticky=tk.W)

        # Subtitle
        subtitle_label = ttk.Label(title_frame, text="📊 Ranking Karyawan untuk Pengangkatan Tetap",
                                 font=('Segoe UI', 11), foreground='#7f8c8d')
        subtitle_label.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))

        # Action buttons frame
        action_frame = ttk.Frame(title_frame)
        action_frame.grid(row=0, column=2, rowspan=2, sticky=tk.E)

        # Export Ranking button
        ttk.Button(action_frame, text="📋 Export Ranking",
                  command=self.export_ranking,
                  style='Warning.TButton').pack(side=tk.RIGHT, padx=(5, 0))

        # Export Excel button
        ttk.Button(action_frame, text="📈 Export ke Excel",
                  command=self.export_to_excel,
                  style='Success.TButton').pack(side=tk.RIGHT, padx=(10, 0))
        
        # Results table with modern design
        table_frame = ttk.LabelFrame(main_frame, text="🏆 Ranking Karyawan Berdasarkan TOPSIS", padding="15")
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(1, weight=1)

        # Table info header with enhanced information
        info_frame = ttk.Frame(table_frame)
        info_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)

        # Main info
        ttk.Label(info_frame, text="📈 Semakin tinggi score TOPSIS, semakin direkomendasikan untuk diangkat menjadi karyawan tetap",
                 font=('Segoe UI', 9, 'bold'), foreground='#17a2b8').grid(row=0, column=0, sticky=tk.W)

        # Additional info with criteria explanation
        ttk.Label(info_frame, text=f"🎯 Total: {len(self.results)} karyawan | 📊 Nilai kriteria: Kinerja, Disiplin, Teknis, Komunikasi (1-5), Absensi (%)",
                 font=('Segoe UI', 8), foreground='#6c757d').grid(row=1, column=0, sticky=tk.W, pady=(2, 0))

        # Legend
        legend_frame = ttk.Frame(info_frame)
        legend_frame.grid(row=0, column=1, rowspan=2, sticky=tk.E)

        ttk.Label(legend_frame, text="🥇 Rank 1  🥈 Rank 2  🥉 Rank 3  ⭐ Top 5  📊 Lainnya",
                 font=('Segoe UI', 8), foreground='#495057').pack()

        # Treeview with enhanced columns including criteria details
        columns = ('Rank', 'Nama', 'NIP', 'Posisi', 'Masa_Kerja', 'Score', 'Score %', 'Kinerja', 'Kedisiplinan', 'Teknis', 'Komunikasi', 'Absensi', 'Rekomendasi', 'Status')
        self.result_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=16)

        # Configure columns with icons and better formatting
        column_config = {
            'Rank': {'width': 50, 'text': '🏆 Rank', 'anchor': 'center'},
            'Nama': {'width': 140, 'text': '👤 Nama Karyawan', 'anchor': 'w'},
            'NIP': {'width': 80, 'text': '🆔 NIP', 'anchor': 'center'},
            'Posisi': {'width': 110, 'text': '💼 Posisi', 'anchor': 'w'},
            'Masa_Kerja': {'width': 70, 'text': '📅 Masa Kerja', 'anchor': 'center'},
            'Score': {'width': 80, 'text': '📊 Score TOPSIS', 'anchor': 'center'},
            'Score %': {'width': 70, 'text': '📈 %', 'anchor': 'center'},
            'Kinerja': {'width': 60, 'text': '⭐ Kinerja', 'anchor': 'center'},
            'Kedisiplinan': {'width': 70, 'text': '🎯 Disiplin', 'anchor': 'center'},
            'Teknis': {'width': 60, 'text': '🔧 Teknis', 'anchor': 'center'},
            'Komunikasi': {'width': 70, 'text': '💬 Komunikasi', 'anchor': 'center'},
            'Absensi': {'width': 60, 'text': '📊 Absensi', 'anchor': 'center'},
            'Rekomendasi': {'width': 150, 'text': '⭐ Rekomendasi', 'anchor': 'w'},
            'Status': {'width': 100, 'text': '🎯 Status', 'anchor': 'center'}
        }

        for col in columns:
            config = column_config[col]
            self.result_tree.heading(col, text=config['text'], command=lambda c=col: self.sort_by_column(c))
            self.result_tree.column(col, width=config['width'], minwidth=60, anchor=config['anchor'])
        
        # Modern scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.result_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid treeview and scrollbars
        self.result_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        # Summary frame with modern design
        summary_frame = ttk.LabelFrame(main_frame, text="📊 Ringkasan Hasil Analisis", padding="15")
        summary_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(20, 0))

        # Summary statistics
        self.create_summary(summary_frame)

        # Detail frame with modern design
        detail_frame = ttk.LabelFrame(main_frame, text="🔍 Detail Karyawan Terpilih", padding="15")
        detail_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(15, 0))

        # Instructions
        ttk.Label(detail_frame, text="💡 Klik pada baris karyawan di tabel untuk melihat detail perhitungan",
                 font=('Segoe UI', 9), foreground='#6c757d').grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.detail_text = tk.Text(detail_frame, height=8, wrap=tk.WORD, state=tk.DISABLED,
                                  font=('Segoe UI', 9), bg='#f8f9fa', relief='solid', borderwidth=1)
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scrollbar.set)

        self.detail_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        detail_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        detail_frame.columnconfigure(0, weight=1)
        detail_frame.rowconfigure(1, weight=1)

        # Load results first
        self.load_results()

        # Load initial content
        self.load_initial_detail_content()

        # Bind selection event
        self.result_tree.bind('<<TreeviewSelect>>', self.on_select)

        # Auto-select top performer
        if self.results and self.result_tree.get_children():
            self.result_tree.selection_set(self.result_tree.get_children()[0])
            self.show_employee_detail(self.results[0])

        # Bottom action frame
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(20, 0))

        # Close button
        ttk.Button(bottom_frame, text="❌ Tutup", command=self.window.destroy).pack(side=tk.RIGHT)

        # Additional info
        ttk.Label(bottom_frame, text="📋 Hasil ini dapat diekspor ke Excel untuk analisis lebih lanjut",
                 font=('Segoe UI', 8), foreground='#6c757d').pack(side=tk.LEFT)
    
    def create_summary(self, parent):
        """Create summary statistics with modern design"""
        if not self.results:
            return

        # Calculate statistics
        scores = [result.score for result in self.results]
        avg_score = sum(scores) / len(scores)
        max_score = max(scores)
        min_score = min(scores)

        # Find top performer
        top_performer = self.results[0]  # Already sorted by score

        # Recommendation counts
        recommendations = {}
        for result in self.results:
            rec = result.get_recommendation()
            recommendations[rec] = recommendations.get(rec, 0) + 1

        # Main statistics grid
        stats_grid = ttk.Frame(parent)
        stats_grid.pack(fill=tk.X, pady=(0, 15))

        # Statistics cards
        self.create_stat_card(stats_grid, "👥 Total Karyawan", str(len(self.results)),
                             "karyawan dianalisis", 0, 0, '#3498db')

        self.create_stat_card(stats_grid, "🏆 Score Tertinggi", f"{max_score:.4f}",
                             f"({max_score*100:.2f}%)", 0, 1, '#27ae60')

        self.create_stat_card(stats_grid, "📊 Score Rata-rata", f"{avg_score:.4f}",
                             f"({avg_score*100:.2f}%)", 0, 2, '#f39c12')

        self.create_stat_card(stats_grid, "📉 Score Terendah", f"{min_score:.4f}",
                             f"({min_score*100:.2f}%)", 0, 3, '#e74c3c')

        # Top performer highlight
        top_frame = ttk.LabelFrame(parent, text="🥇 Karyawan Terbaik", padding="10")
        top_frame.pack(fill=tk.X, pady=(0, 15))

        top_info = ttk.Frame(top_frame)
        top_info.pack(fill=tk.X)

        ttk.Label(top_info, text=f"👤 {top_performer.nama}",
                 font=('Segoe UI', 12, 'bold'), foreground='#27ae60').pack(side=tk.LEFT)

        ttk.Label(top_info, text=f"🆔 {top_performer.nip} | 💼 {top_performer.posisi}",
                 font=('Segoe UI', 10)).pack(side=tk.LEFT, padx=(20, 0))

        ttk.Label(top_info, text=f"⭐ Score: {top_performer.score:.4f} ({top_performer.get_score_percentage():.2f}%)",
                 font=('Segoe UI', 10, 'bold'), foreground='#27ae60').pack(side=tk.RIGHT)

        # Recommendation distribution
        rec_frame = ttk.LabelFrame(parent, text="📈 Distribusi Rekomendasi", padding="10")
        rec_frame.pack(fill=tk.X)

        rec_grid = ttk.Frame(rec_frame)
        rec_grid.pack(fill=tk.X)

        col = 0
        for rec, count in recommendations.items():
            percentage = (count / len(self.results)) * 100

            # Color coding for recommendations
            if "Sangat" in rec:
                color = '#27ae60'
                icon = '🌟'
            elif "Direkomendasikan" in rec and "Kurang" not in rec:
                color = '#f39c12'
                icon = '⭐'
            elif "Cukup" in rec:
                color = '#3498db'
                icon = '💫'
            else:
                color = '#e74c3c'
                icon = '⚠️'

            self.create_rec_card(rec_grid, f"{icon} {rec}", count, percentage, col, color)
            col += 1

    def create_stat_card(self, parent, title, value, subtitle, row, col, color):
        """Create a statistics card"""
        card_frame = ttk.Frame(parent, relief='solid', borderwidth=1)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid
        parent.columnconfigure(col, weight=1)

        # Title
        ttk.Label(card_frame, text=title, font=('Segoe UI', 9, 'bold'),
                 foreground=color).pack(pady=(8, 2))

        # Value
        ttk.Label(card_frame, text=value, font=('Segoe UI', 14, 'bold'),
                 foreground=color).pack()

        # Subtitle
        ttk.Label(card_frame, text=subtitle, font=('Segoe UI', 8),
                 foreground='#7f8c8d').pack(pady=(2, 8))

    def create_rec_card(self, parent, title, count, percentage, col, color):
        """Create a recommendation card"""
        card_frame = ttk.Frame(parent, relief='solid', borderwidth=1)
        card_frame.grid(row=0, column=col, padx=5, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid
        parent.columnconfigure(col, weight=1)

        # Title
        ttk.Label(card_frame, text=title, font=('Segoe UI', 9, 'bold'),
                 foreground=color).pack(pady=(8, 2))

        # Count
        ttk.Label(card_frame, text=str(count), font=('Segoe UI', 16, 'bold'),
                 foreground=color).pack()

        # Percentage
        ttk.Label(card_frame, text=f"({percentage:.1f}%)", font=('Segoe UI', 8),
                 foreground='#7f8c8d').pack(pady=(2, 8))

    def load_initial_detail_content(self):
        """Load initial attractive content in detail panel"""
        if not self.results:
            return

        # Get top 3 performers
        top_3 = self.results[:3]

        initial_content = f"""
🏆 HASIL ANALISIS TOPSIS - PENGANGKATAN KARYAWAN TETAP

📊 RINGKASAN EKSEKUTIF:
═══════════════════════════════════════════════════════════════

✨ Total karyawan yang dianalisis: {len(self.results)} orang
🎯 Metode yang digunakan: TOPSIS (Technique for Order Preference by Similarity to Ideal Solution)
📅 Tanggal analisis: {self.get_current_date()}

🏆 TOP 3 KARYAWAN TERBAIK:
═══════════════════════════════════════════════════════════════

🥇 JUARA 1: {top_3[0].nama}
   🆔 NIP: {top_3[0].nip}
   💼 Posisi: {top_3[0].posisi}
   ⭐ Score TOPSIS: {top_3[0].score:.4f} ({top_3[0].get_score_percentage():.1f}%)
   🎯 Status: {top_3[0].get_recommendation()}

🥈 JUARA 2: {top_3[1].nama if len(top_3) > 1 else 'N/A'}
   🆔 NIP: {top_3[1].nip if len(top_3) > 1 else 'N/A'}
   💼 Posisi: {top_3[1].posisi if len(top_3) > 1 else 'N/A'}
   ⭐ Score TOPSIS: {top_3[1].score:.4f} ({top_3[1].get_score_percentage():.1f}%) if len(top_3) > 1 else 'N/A'

🥉 JUARA 3: {top_3[2].nama if len(top_3) > 2 else 'N/A'}
   🆔 NIP: {top_3[2].nip if len(top_3) > 2 else 'N/A'}
   💼 Posisi: {top_3[2].posisi if len(top_3) > 2 else 'N/A'}
   ⭐ Score TOPSIS: {top_3[2].score:.4f} ({top_3[2].get_score_percentage():.1f}%) if len(top_3) > 2 else 'N/A'

📈 ANALISIS PERFORMA:
═══════════════════════════════════════════════════════════════

• Score tertinggi: {max(r.score for r in self.results):.4f} ({max(r.get_score_percentage() for r in self.results):.1f}%)
• Score terendah: {min(r.score for r in self.results):.4f} ({min(r.get_score_percentage() for r in self.results):.1f}%)
• Score rata-rata: {sum(r.score for r in self.results)/len(self.results):.4f} ({sum(r.get_score_percentage() for r in self.results)/len(self.results):.1f}%)
• Rentang score: {max(r.score for r in self.results) - min(r.score for r in self.results):.4f}

💡 REKOMENDASI TINDAKAN:
═══════════════════════════════════════════════════════════════

🌟 Sangat Direkomendasikan: {len([r for r in self.results if 'Sangat' in r.get_recommendation()])} karyawan
⭐ Direkomendasikan: {len([r for r in self.results if 'Direkomendasikan' in r.get_recommendation() and 'Sangat' not in r.get_recommendation() and 'Cukup' not in r.get_recommendation() and 'Kurang' not in r.get_recommendation()])} karyawan
💫 Cukup Direkomendasikan: {len([r for r in self.results if 'Cukup' in r.get_recommendation()])} karyawan
⚠️ Kurang Direkomendasikan: {len([r for r in self.results if 'Kurang' in r.get_recommendation()])} karyawan

📋 PETUNJUK PENGGUNAAN:
═══════════════════════════════════════════════════════════════

1. 🖱️ Klik pada baris karyawan di tabel untuk melihat detail perhitungan
2. 📊 Gunakan kolom sorting untuk mengurutkan data
3. 📈 Export hasil ke Excel untuk dokumentasi dan presentasi

═══════════════════════════════════════════════════════════════
        """.strip()

        # Insert content
        self.detail_text.configure(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, initial_content)
        self.detail_text.configure(state=tk.DISABLED)

    def get_current_date(self):
        """Get current date in Indonesian format"""
        from datetime import datetime
        import locale
        try:
            locale.setlocale(locale.LC_TIME, 'id_ID.UTF-8')
        except:
            pass
        return datetime.now().strftime("%d %B %Y")

    def load_results(self):
        """Load results into treeview with enhanced formatting"""
        # Clear existing items
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # Get employee data for detailed information
        from database import DatabaseManager
        db_manager = DatabaseManager()

        # Insert results with enhanced formatting
        for result in self.results:
            # Get employee details from database
            employee_data = db_manager.get_employee_by_id(result.employee_id)

            # Enhanced color coding and status
            if result.ranking == 1:
                tags = ('champion',)
                status = '🥇 Juara 1'
            elif result.ranking == 2:
                tags = ('runner_up',)
                status = '🥈 Juara 2'
            elif result.ranking == 3:
                tags = ('third',)
                status = '🥉 Juara 3'
            elif result.ranking <= 5:
                tags = ('good',)
                status = '⭐ Top 5'
            elif result.ranking <= 10:
                tags = ('average',)
                status = '📊 Top 10'
            else:
                tags = ('normal',)
                status = '📋 Lainnya'

            # Format values with icons and better presentation including criteria details
            if employee_data:
                values = (
                    f"#{result.ranking}",
                    result.nama,
                    result.nip,
                    result.posisi,
                    f"{employee_data['masa_kerja']} thn",
                    f"{result.score:.4f}",
                    f"{result.get_score_percentage():.1f}%",
                    f"{employee_data['kinerja_kerja']:.1f}",
                    f"{employee_data['kedisiplinan']:.1f}",
                    f"{employee_data['kemampuan_teknis']:.1f}",
                    f"{employee_data['komunikasi']:.1f}",
                    f"{employee_data['tingkat_absensi']:.1f}%",
                    result.get_recommendation(),
                    status
                )
            else:
                # Fallback if employee data not found
                values = (
                    f"#{result.ranking}",
                    result.nama,
                    result.nip,
                    result.posisi,
                    "N/A",
                    f"{result.score:.4f}",
                    f"{result.get_score_percentage():.1f}%",
                    "N/A",
                    "N/A",
                    "N/A",
                    "N/A",
                    "N/A",
                    result.get_recommendation(),
                    status
                )

            self.result_tree.insert('', tk.END, values=values, tags=tags)

        # Configure enhanced tags with better colors
        self.result_tree.tag_configure('champion', background='#d4edda', foreground='#155724')  # Green
        self.result_tree.tag_configure('runner_up', background='#d1ecf1', foreground='#0c5460')  # Blue
        self.result_tree.tag_configure('third', background='#fff3cd', foreground='#856404')     # Yellow
        self.result_tree.tag_configure('good', background='#f8f9fa', foreground='#495057')      # Light gray
        self.result_tree.tag_configure('average', background='#ffffff', foreground='#6c757d')   # White
        self.result_tree.tag_configure('normal', background='#f8f9fa', foreground='#6c757d')    # Light gray
    
    def sort_by_column(self, column):
        """Sort treeview by column"""
        items = [(self.result_tree.set(item, column), item) for item in self.result_tree.get_children()]

        # Determine if numeric sort is needed
        numeric_columns = ['Rank', 'Score', 'Score %', 'Kinerja', 'Kedisiplinan', 'Teknis', 'Komunikasi', 'Absensi']
        if column in numeric_columns:
            try:
                # Handle different formats
                def extract_number(value):
                    if isinstance(value, str):
                        # Remove symbols and extract number
                        import re
                        numbers = re.findall(r'[\d.]+', value)
                        return float(numbers[0]) if numbers else 0
                    return float(value)

                items.sort(key=lambda x: extract_number(x[0]))
            except:
                items.sort()  # Fallback to string sort
        else:
            items.sort()

        # Rearrange items
        for index, (_, item) in enumerate(items):
            self.result_tree.move(item, '', index)
    
    def on_select(self, event=None):
        """Handle treeview selection"""
        selected = self.result_tree.selection()
        if not selected:
            return
        
        # Get selected item data
        item = self.result_tree.item(selected[0])
        values = item['values']
        
        # Find corresponding result
        selected_result = None
        for result in self.results:
            if result.ranking == values[0]:
                selected_result = result
                break
        
        if selected_result:
            self.show_employee_detail(selected_result)
    
    def show_employee_detail(self, result: TOPSISResult):
        """Show detailed information for selected employee with enhanced formatting"""

        # Get employee details from database
        from database import DatabaseManager
        db_manager = DatabaseManager()
        employee_data = db_manager.get_employee_by_id(result.employee_id)

        # Determine performance level
        score_pct = result.get_score_percentage()
        if score_pct >= 80:
            performance_level = "🌟 EXCELLENT"
            performance_color = "Hijau"
            performance_desc = "Performa luar biasa, sangat siap untuk promosi"
        elif score_pct >= 70:
            performance_level = "⭐ VERY GOOD"
            performance_color = "Biru"
            performance_desc = "Performa sangat baik, direkomendasikan untuk promosi"
        elif score_pct >= 60:
            performance_level = "💫 GOOD"
            performance_color = "Kuning"
            performance_desc = "Performa baik, dapat dipertimbangkan untuk promosi"
        elif score_pct >= 50:
            performance_level = "📊 AVERAGE"
            performance_color = "Orange"
            performance_desc = "Performa rata-rata, perlu peningkatan sebelum promosi"
        else:
            performance_level = "⚠️ BELOW AVERAGE"
            performance_color = "Merah"
            performance_desc = "Performa di bawah rata-rata, perlu perbaikan signifikan"

        # Get ranking context
        total_employees = len(self.results)
        ranking_percentile = ((total_employees - result.ranking + 1) / total_employees) * 100

        if result.ranking == 1:
            ranking_badge = "🥇 JUARA 1"
        elif result.ranking == 2:
            ranking_badge = "🥈 JUARA 2"
        elif result.ranking == 3:
            ranking_badge = "🥉 JUARA 3"
        elif result.ranking <= 5:
            ranking_badge = f"⭐ TOP {result.ranking}"
        else:
            ranking_badge = f"📊 RANKING #{result.ranking}"

        # Create detailed text with criteria information
        if employee_data:
            criteria_section = f"""
📊 NILAI KRITERIA PENILAIAN:
───────────────────────────────────────────────────────────────
⭐ Kinerja Kerja      : {employee_data['kinerja_kerja']:.1f}/5.0 ({self.get_criteria_rating(employee_data['kinerja_kerja'])})
🎯 Kedisiplinan       : {employee_data['kedisiplinan']:.1f}/5.0 ({self.get_criteria_rating(employee_data['kedisiplinan'])})
🔧 Kemampuan Teknis   : {employee_data['kemampuan_teknis']:.1f}/5.0 ({self.get_criteria_rating(employee_data['kemampuan_teknis'])})
💬 Komunikasi         : {employee_data['komunikasi']:.1f}/5.0 ({self.get_criteria_rating(employee_data['komunikasi'])})
📅 Masa Kerja         : {employee_data['masa_kerja']} tahun
📊 Tingkat Absensi    : {employee_data['tingkat_absensi']:.1f}% ({self.get_absensi_rating(employee_data['tingkat_absensi'])})
"""
        else:
            criteria_section = """
📊 NILAI KRITERIA PENILAIAN:
───────────────────────────────────────────────────────────────
⚠️ Data kriteria tidak tersedia
"""

        detail_text = f"""
🔍 DETAIL ANALISIS KARYAWAN
═══════════════════════════════════════════════════════════════

👤 INFORMASI PERSONAL:
───────────────────────────────────────────────────────────────
📝 Nama Lengkap    : {result.nama}
🆔 NIP             : {result.nip}
💼 Posisi          : {result.posisi}
🏆 Ranking         : {ranking_badge}
📊 Persentil       : Top {ranking_percentile:.1f}% dari {total_employees} karyawan
{criteria_section}
⭐ HASIL PERHITUNGAN TOPSIS:
───────────────────────────────────────────────────────────────
🎯 Score TOPSIS    : {result.score:.6f}
📈 Score Persentase: {result.get_score_percentage():.2f}%
📏 Jarak D+        : {result.distance_positive:.6f} (ke solusi ideal positif)
📏 Jarak D-        : {result.distance_negative:.6f} (ke solusi ideal negatif)
🎪 Rasio D-/(D++D-): {result.score:.6f}

🎯 EVALUASI PERFORMA:
───────────────────────────────────────────────────────────────
📊 Level Performa  : {performance_level}
🎨 Kategori        : {performance_color}
💡 Deskripsi       : {performance_desc}
⭐ Rekomendasi     : {result.get_recommendation()}

📈 ANALISIS MENDALAM:
───────────────────────────────────────────────────────────────
• 🎯 Score Interpretation:
  - Score {result.score:.4f} menunjukkan karyawan ini berada pada posisi yang {'sangat baik' if score_pct >= 70 else 'baik' if score_pct >= 50 else 'perlu perbaikan'}
  - Dalam skala 0-1, score ini termasuk {'tinggi' if score_pct >= 70 else 'sedang' if score_pct >= 50 else 'rendah'}

• 📏 Distance Analysis:
  - D+ = {result.distance_positive:.4f} ({'rendah (baik)' if result.distance_positive < 0.1 else 'sedang' if result.distance_positive < 0.2 else 'tinggi'})
  - D- = {result.distance_negative:.4f} ({'tinggi (baik)' if result.distance_negative > 0.1 else 'sedang' if result.distance_negative > 0.05 else 'rendah'})
  - Ideal: D+ kecil (dekat dengan solusi terbaik) dan D- besar (jauh dari solusi terburuk)

• 🏆 Competitive Position:
  - Mengalahkan {total_employees - result.ranking} dari {total_employees - 1} karyawan lainnya
  - Berada di {ranking_percentile:.1f}% teratas
  - {'Sangat kompetitif' if ranking_percentile >= 80 else 'Kompetitif' if ranking_percentile >= 60 else 'Cukup kompetitif' if ranking_percentile >= 40 else 'Kurang kompetitif'}

💼 REKOMENDASI TINDAKAN:
───────────────────────────────────────────────────────────────
"""

        # Add specific recommendations based on performance
        if score_pct >= 80:
            detail_text += """✅ SEGERA ANGKAT: Karyawan ini siap untuk diangkat menjadi karyawan tetap
✅ FAST TRACK: Pertimbangkan untuk jalur cepat promosi
✅ MENTOR ROLE: Dapat dijadikan mentor untuk karyawan lain
✅ LEADERSHIP: Berpotensi untuk posisi kepemimpinan"""
        elif score_pct >= 70:
            detail_text += """✅ DIREKOMENDASIKAN: Sangat layak untuk diangkat menjadi karyawan tetap
✅ DEVELOPMENT: Berikan program pengembangan lanjutan
✅ RESPONSIBILITY: Tingkatkan tanggung jawab dan wewenang
✅ MONITORING: Pantau perkembangan untuk 3-6 bulan ke depan"""
        elif score_pct >= 60:
            detail_text += """⚠️ PERTIMBANGKAN: Dapat dipertimbangkan dengan syarat tertentu
⚠️ IMPROVEMENT: Fokus pada area yang perlu diperbaiki
⚠️ TRAINING: Berikan pelatihan tambahan sesuai kebutuhan
⚠️ EVALUATION: Evaluasi ulang dalam 6 bulan"""
        else:
            detail_text += """❌ BELUM SIAP: Belum siap untuk diangkat menjadi karyawan tetap
❌ DEVELOPMENT: Perlu program pengembangan intensif
❌ COACHING: Berikan coaching dan mentoring
❌ RE-EVALUATION: Evaluasi ulang setelah perbaikan signifikan"""

        detail_text += f"""

📅 TIMELINE REKOMENDASI:
───────────────────────────────────────────────────────────────
• Keputusan segera: {'Ya' if score_pct >= 70 else 'Tidak, perlu evaluasi lanjutan'}
• Review berikutnya: {('1-3 bulan' if score_pct >= 80 else '3-6 bulan' if score_pct >= 60 else '6-12 bulan')}
• Target improvement: {('Maintain excellence' if score_pct >= 80 else f'Tingkatkan ke {score_pct + 10:.0f}%+')}

═══════════════════════════════════════════════════════════════
📊 Analisis ini dihasilkan menggunakan metode TOPSIS dengan 6 kriteria penilaian
🕒 Dianalisis pada: {self.get_current_date()}
        """.strip()
        
        # Update detail text
        self.detail_text.configure(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        self.detail_text.insert(1.0, detail_text)
        self.detail_text.configure(state=tk.DISABLED)
    
    def export_to_excel(self):
        """Export TOPSIS results to Excel"""
        try:
            if not self.results:
                messagebox.showwarning("⚠️ Peringatan", "Tidak ada data untuk diekspor")
                return

            # Import pandas and openpyxl for Excel export
            try:
                import pandas as pd
                from datetime import datetime
            except ImportError:
                messagebox.showerror("❌ Error",
                                   "Library yang diperlukan tidak tersedia.\n\n"
                                   "Untuk menggunakan fitur export Excel, install:\n"
                                   "pip install pandas openpyxl")
                return

            # Prepare data for Excel with detailed criteria
            from database import DatabaseManager
            db_manager = DatabaseManager()

            excel_data = []
            for result in self.results:
                # Get employee details
                employee_data = db_manager.get_employee_by_id(result.employee_id)

                if employee_data:
                    excel_data.append({
                        'Ranking': result.ranking,
                        'Nama_Karyawan': result.nama,
                        'NIP': result.nip,
                        'Posisi': result.posisi,
                        'Masa_Kerja_Tahun': employee_data['masa_kerja'],
                        'Score_TOPSIS': round(result.score, 6),
                        'Score_Persentase': round(result.get_score_percentage(), 2),
                        'Kinerja_Kerja': employee_data['kinerja_kerja'],
                        'Kedisiplinan': employee_data['kedisiplinan'],
                        'Kemampuan_Teknis': employee_data['kemampuan_teknis'],
                        'Komunikasi': employee_data['komunikasi'],
                        'Tingkat_Absensi_Persen': employee_data['tingkat_absensi'],
                        'Jarak_D_Plus': round(result.distance_positive, 6),
                        'Jarak_D_Minus': round(result.distance_negative, 6),
                        'Rekomendasi': result.get_recommendation(),
                        'Status_Ranking': self.get_ranking_status(result.ranking),
                        'Level_Performa': self.get_performance_level(result.get_score_percentage()),
                        'Tanggal_Analisis': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    })
                else:
                    # Fallback data
                    excel_data.append({
                        'Ranking': result.ranking,
                        'Nama_Karyawan': result.nama,
                        'NIP': result.nip,
                        'Posisi': result.posisi,
                        'Masa_Kerja_Tahun': 'N/A',
                        'Score_TOPSIS': round(result.score, 6),
                        'Score_Persentase': round(result.get_score_percentage(), 2),
                        'Kinerja_Kerja': 'N/A',
                        'Kedisiplinan': 'N/A',
                        'Kemampuan_Teknis': 'N/A',
                        'Komunikasi': 'N/A',
                        'Tingkat_Absensi_Persen': 'N/A',
                        'Jarak_D_Plus': round(result.distance_positive, 6),
                        'Jarak_D_Minus': round(result.distance_negative, 6),
                        'Rekomendasi': result.get_recommendation(),
                        'Status_Ranking': self.get_ranking_status(result.ranking),
                        'Level_Performa': self.get_performance_level(result.get_score_percentage()),
                        'Tanggal_Analisis': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    })

            # Create DataFrame
            df = pd.DataFrame(excel_data)

            # Ask user for save location
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="💾 Simpan Hasil TOPSIS ke Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile=f"TOPSIS_Results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if filename:
                # Create Excel writer with multiple sheets
                with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                    # Main results sheet
                    df.to_excel(writer, sheet_name='TOPSIS_Results', index=False)

                    # Summary statistics sheet
                    self.create_summary_sheet(writer, self.results)

                    # Recommendation distribution sheet
                    self.create_recommendation_sheet(writer, self.results)

                messagebox.showinfo("🎉 Export Berhasil!",
                                  f"✅ Data berhasil diekspor ke Excel!\n\n"
                                  f"📁 File: {filename}\n"
                                  f"📊 {len(self.results)} karyawan\n"
                                  f"📋 3 sheet: Results, Summary, Recommendations\n\n"
                                  f"💡 File dapat dibuka dengan Microsoft Excel atau aplikasi spreadsheet lainnya")

        except Exception as e:
            messagebox.showerror("❌ Error Export", f"Gagal mengekspor ke Excel:\n{str(e)}")

    def get_ranking_status(self, ranking):
        """Get ranking status text"""
        if ranking == 1:
            return "🥇 Juara 1"
        elif ranking == 2:
            return "🥈 Juara 2"
        elif ranking == 3:
            return "🥉 Juara 3"
        elif ranking <= 5:
            return f"⭐ Top {ranking}"
        elif ranking <= 10:
            return f"📊 Top {ranking}"
        else:
            return f"📋 Ranking {ranking}"

    def get_performance_level(self, score_pct):
        """Get performance level text"""
        if score_pct >= 80:
            return "🌟 Excellent"
        elif score_pct >= 70:
            return "⭐ Very Good"
        elif score_pct >= 60:
            return "💫 Good"
        elif score_pct >= 50:
            return "📊 Average"
        else:
            return "⚠️ Below Average"

    def create_summary_sheet(self, writer, results):
        """Create summary statistics sheet"""
        import pandas as pd
        from datetime import datetime

        # Calculate statistics
        scores = [result.score for result in results]

        summary_data = {
            'Metrik': [
                'Total Karyawan',
                'Score Tertinggi',
                'Score Terendah',
                'Score Rata-rata',
                'Standar Deviasi',
                'Rentang Score',
                'Tanggal Analisis'
            ],
            'Nilai': [
                len(results),
                f"{max(scores):.6f}",
                f"{min(scores):.6f}",
                f"{sum(scores)/len(scores):.6f}",
                f"{(sum([(x - sum(scores)/len(scores))**2 for x in scores])/len(scores))**0.5:.6f}",
                f"{max(scores) - min(scores):.6f}",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ],
            'Persentase': [
                f"{len(results)} orang",
                f"{max(scores)*100:.2f}%",
                f"{min(scores)*100:.2f}%",
                f"{(sum(scores)/len(scores))*100:.2f}%",
                f"{((sum([(x - sum(scores)/len(scores))**2 for x in scores])/len(scores))**0.5)*100:.2f}%",
                f"{(max(scores) - min(scores))*100:.2f}%",
                "Waktu Analisis"
            ]
        }

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary_Statistics', index=False)

    def create_recommendation_sheet(self, writer, results):
        """Create recommendation distribution sheet"""
        import pandas as pd

        # Recommendation distribution
        recommendations = {}
        for result in results:
            rec = result.get_recommendation()
            recommendations[rec] = recommendations.get(rec, 0) + 1

        rec_data = {
            'Rekomendasi': list(recommendations.keys()),
            'Jumlah': list(recommendations.values()),
            'Persentase': [f"{(count/len(results))*100:.1f}%" for count in recommendations.values()]
        }

        rec_df = pd.DataFrame(rec_data)
        rec_df.to_excel(writer, sheet_name='Recommendation_Distribution', index=False)

    def export_ranking(self):
        """Export ranking summary for permanent employee promotion"""
        try:
            # Import required libraries
            try:
                import pandas as pd
                from datetime import datetime
                from tkinter import filedialog
            except ImportError:
                messagebox.showerror("❌ Error",
                                   "Library yang diperlukan tidak tersedia.\n\n"
                                   "Untuk menggunakan fitur export, install:\n"
                                   "pip install pandas openpyxl")
                return

            # Filter top performers (recommended for permanent employment)
            top_performers = []
            recommended_performers = []

            for result in self.results:
                recommendation = result.get_recommendation()
                if "Sangat" in recommendation or result.ranking <= 3:
                    top_performers.append(result)
                elif "Direkomendasikan" in recommendation and "Kurang" not in recommendation:
                    recommended_performers.append(result)

            # File dialog untuk save
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"Ranking_Karyawan_Tetap_{timestamp}.xlsx"

            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                initialfile=default_filename,
                title="Simpan Ranking Karyawan Tetap ke Excel"
            )

            if not file_path:
                return

            # Prepare summary data
            summary_data = []

            # Add top performers
            for i, result in enumerate(top_performers, 1):
                summary_data.append({
                    'Prioritas': f"Prioritas {i}",
                    'Ranking_TOPSIS': result.ranking,
                    'Nama_Karyawan': result.nama,
                    'NIP': result.nip,
                    'Posisi': result.posisi,
                    'Score_TOPSIS': round(result.score, 6),
                    'Score_Persentase': f"{result.get_score_percentage():.1f}%",
                    'Rekomendasi': result.get_recommendation(),
                    'Status_Pengangkatan': "🌟 SANGAT DIREKOMENDASIKAN",
                    'Catatan': "Siap diangkat menjadi karyawan tetap segera"
                })

            # Add recommended performers
            for i, result in enumerate(recommended_performers, len(top_performers) + 1):
                summary_data.append({
                    'Prioritas': f"Cadangan {i - len(top_performers)}",
                    'Ranking_TOPSIS': result.ranking,
                    'Nama_Karyawan': result.nama,
                    'NIP': result.nip,
                    'Posisi': result.posisi,
                    'Score_TOPSIS': round(result.score, 6),
                    'Score_Persentase': f"{result.get_score_percentage():.1f}%",
                    'Rekomendasi': result.get_recommendation(),
                    'Status_Pengangkatan': "⭐ DIREKOMENDASIKAN",
                    'Catatan': "Dapat dipertimbangkan untuk pengangkatan"
                })

            # Create DataFrame
            df = pd.DataFrame(summary_data)

            # Save to Excel with styling
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Ranking_Karyawan_Tetap', index=False)

                # Get workbook and worksheet
                workbook = writer.book
                worksheet = writer.sheets['Ranking_Karyawan_Tetap']

                # Style the header
                from openpyxl.styles import Font, PatternFill, Alignment
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                header_font = Font(color="FFFFFF", bold=True)

                for cell in worksheet[1]:
                    cell.fill = header_fill
                    cell.font = header_font
                    cell.alignment = Alignment(horizontal="center")

                # Auto-adjust column widths
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            # Success message
            messagebox.showinfo("🎉 Export Berhasil!",
                              f"✅ Ranking karyawan tetap berhasil diekspor!\n\n"
                              f"📁 File: {file_path.split('/')[-1]}\n"
                              f"🌟 Prioritas utama: {len(top_performers)} karyawan\n"
                              f"⭐ Cadangan: {len(recommended_performers)} karyawan\n"
                              f"📈 Format: Excel (.xlsx)\n\n"
                              f"💡 File siap untuk review manajemen")

        except Exception as e:
            print(f"Export ranking error: {e}")  # Debug
            messagebox.showerror("❌ Error Export", f"Gagal mengekspor ranking:\n{str(e)}")

    def center_window(self):
        """Center the window on parent"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width // 2) - (width // 2)
        y = parent_y + (parent_height // 2) - (height // 2)
        
        self.window.geometry(f'{width}x{height}+{x}+{y}')

    def get_criteria_rating(self, value):
        """Get rating description for criteria value (1-5)"""
        if value >= 4.5:
            return "Sangat Baik"
        elif value >= 3.5:
            return "Baik"
        elif value >= 2.5:
            return "Cukup"
        elif value >= 1.5:
            return "Kurang"
        else:
            return "Sangat Kurang"

    def get_absensi_rating(self, value):
        """Get rating description for absensi percentage"""
        if value <= 2:
            return "Sangat Baik"
        elif value <= 5:
            return "Baik"
        elif value <= 10:
            return "Cukup"
        elif value <= 15:
            return "Kurang"
        else:
            return "Sangat Kurang"
