#!/usr/bin/env python3
"""
SPK TOPSIS Enhanced v2.0 - Complete Database Manager
Author: <PERSON>ibowo
NIM: ************
Kelas: 06TPLP003

File lengkap untuk manajemen database SQLite sistem TOPSIS
Berisi semua fungsi penting untuk CRUD operations dan <PERSON><PERSON><PERSON> calculation
"""

import sqlite3
import json
import hashlib
import os
from datetime import datetime, date
from typing import List, Dict, Tuple, Optional
import uuid

class SPKTopsisDatabase:
    """Complete Database Manager untuk SPK TOPSIS"""
    
    def __init__(self, db_path: str = 'spk_karyawan_enhanced.db'):
        self.db_path = db_path
        self.conn = None
    
    def connect(self) -> bool:
        """Koneksi ke database"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute("PRAGMA foreign_keys = ON")
            self.conn.row_factory = sqlite3.Row  # Enable column access by name
            return True
        except Exception as e:
            print(f"❌ Error koneksi database: {e}")
            return False
    
    def close(self):
        """Tutup koneksi database"""
        if self.conn:
            self.conn.close()
    
    def execute_sql_file(self, sql_file_path: str) -> bool:
        """Execute SQL file untuk setup database"""
        try:
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_script = file.read()
            
            self.conn.executescript(sql_script)
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error executing SQL file: {e}")
            return False
    
    # ============================================================
    # USER MANAGEMENT
    # ============================================================
    
    def hash_password(self, password: str) -> str:
        """Hash password menggunakan SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """Autentikasi user"""
        try:
            cursor = self.conn.cursor()
            hashed_password = self.hash_password(password)
            
            cursor.execute("""
                SELECT id, username, role, full_name, email 
                FROM users 
                WHERE username = ? AND password = ? AND is_active = 1
            """, (username, hashed_password))
            
            user = cursor.fetchone()
            if user:
                # Update last login
                cursor.execute("""
                    UPDATE users SET last_login = CURRENT_TIMESTAMP 
                    WHERE id = ?
                """, (user['id'],))
                self.conn.commit()
                
                return dict(user)
            return None
        except Exception as e:
            print(f"❌ Error authentication: {e}")
            return None
    
    def create_user(self, username: str, password: str, role: str, 
                   full_name: str = None, email: str = None) -> bool:
        """Buat user baru"""
        try:
            cursor = self.conn.cursor()
            hashed_password = self.hash_password(password)
            
            cursor.execute("""
                INSERT INTO users (username, password, role, full_name, email)
                VALUES (?, ?, ?, ?, ?)
            """, (username, hashed_password, role, full_name, email))
            
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error creating user: {e}")
            return False
    
    # ============================================================
    # CRITERIA MANAGEMENT
    # ============================================================
    
    def get_all_criteria(self) -> List[Dict]:
        """Ambil semua kriteria"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT * FROM dynamic_criteria 
                WHERE is_active = 1 
                ORDER BY code
            """)
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"❌ Error getting criteria: {e}")
            return []
    
    def add_criteria(self, code: str, name: str, weight: float, 
                    criteria_type: str, description: str = None) -> bool:
        """Tambah kriteria baru"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO dynamic_criteria (code, name, weight, type, description)
                VALUES (?, ?, ?, ?, ?)
            """, (code, name, weight, criteria_type, description))
            
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error adding criteria: {e}")
            return False
    
    def update_criteria(self, criteria_id: int, **kwargs) -> bool:
        """Update kriteria"""
        try:
            cursor = self.conn.cursor()
            
            # Build dynamic update query
            set_clauses = []
            values = []
            
            for key, value in kwargs.items():
                if key in ['code', 'name', 'weight', 'type', 'description', 'is_active']:
                    set_clauses.append(f"{key} = ?")
                    values.append(value)
            
            if not set_clauses:
                return False
            
            set_clauses.append("updated_at = CURRENT_TIMESTAMP")
            values.append(criteria_id)
            
            query = f"""
                UPDATE dynamic_criteria 
                SET {', '.join(set_clauses)}
                WHERE id = ?
            """
            
            cursor.execute(query, values)
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error updating criteria: {e}")
            return False
    
    def delete_criteria(self, criteria_id: int) -> bool:
        """Hapus kriteria (soft delete)"""
        return self.update_criteria(criteria_id, is_active=0)
    
    def validate_criteria_weights(self) -> Tuple[bool, float]:
        """Validasi total bobot kriteria"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT SUM(weight) as total_weight 
                FROM dynamic_criteria 
                WHERE is_active = 1
            """)
            
            result = cursor.fetchone()
            total_weight = result['total_weight'] if result else 0
            
            is_valid = abs(total_weight - 1.0) < 0.001
            return is_valid, total_weight
        except Exception as e:
            print(f"❌ Error validating weights: {e}")
            return False, 0.0
    
    # ============================================================
    # ALTERNATIVES MANAGEMENT
    # ============================================================
    
    def get_all_alternatives(self) -> List[Dict]:
        """Ambil semua alternatif/karyawan"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                SELECT * FROM dynamic_alternatives 
                WHERE is_active = 1 
                ORDER BY name
            """)
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"❌ Error getting alternatives: {e}")
            return []
    
    def add_alternative(self, name: str, position: str = 'Karyawan', 
                       department: str = None, employee_id: str = None, 
                       description: str = None, hire_date: str = None) -> bool:
        """Tambah alternatif/karyawan baru"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("""
                INSERT INTO dynamic_alternatives 
                (name, position, department, employee_id, description, hire_date)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (name, position, department, employee_id, description, hire_date))
            
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error adding alternative: {e}")
            return False
    
    def update_alternative(self, alternative_id: int, **kwargs) -> bool:
        """Update alternatif/karyawan"""
        try:
            cursor = self.conn.cursor()
            
            # Build dynamic update query
            set_clauses = []
            values = []
            
            for key, value in kwargs.items():
                if key in ['name', 'position', 'department', 'employee_id', 'description', 'hire_date', 'is_active']:
                    set_clauses.append(f"{key} = ?")
                    values.append(value)
            
            if not set_clauses:
                return False
            
            set_clauses.append("updated_at = CURRENT_TIMESTAMP")
            values.append(alternative_id)
            
            query = f"""
                UPDATE dynamic_alternatives 
                SET {', '.join(set_clauses)}
                WHERE id = ?
            """
            
            cursor.execute(query, values)
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error updating alternative: {e}")
            return False
    
    def delete_alternative(self, alternative_id: int) -> bool:
        """Hapus alternatif (soft delete)"""
        return self.update_alternative(alternative_id, is_active=0)
    
    # ============================================================
    # EVALUATIONS MANAGEMENT
    # ============================================================
    
    def get_evaluations(self, evaluation_period: str = None) -> List[Dict]:
        """Ambil data evaluasi"""
        try:
            cursor = self.conn.cursor()
            
            if evaluation_period:
                cursor.execute("""
                    SELECT e.*, a.name as alternative_name, c.code as criteria_code, c.name as criteria_name
                    FROM dynamic_evaluations e
                    JOIN dynamic_alternatives a ON e.alternative_id = a.id
                    JOIN dynamic_criteria c ON e.criteria_id = c.id
                    WHERE e.evaluation_period = ?
                    ORDER BY a.name, c.code
                """, (evaluation_period,))
            else:
                cursor.execute("""
                    SELECT e.*, a.name as alternative_name, c.code as criteria_code, c.name as criteria_name
                    FROM dynamic_evaluations e
                    JOIN dynamic_alternatives a ON e.alternative_id = a.id
                    JOIN dynamic_criteria c ON e.criteria_id = c.id
                    ORDER BY a.name, c.code
                """)
            
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"❌ Error getting evaluations: {e}")
            return []
    
    def add_evaluation(self, alternative_id: int, criteria_id: int, score: float,
                      evaluator: str = None, evaluation_period: str = None, 
                      notes: str = None) -> bool:
        """Tambah/update evaluasi"""
        try:
            cursor = self.conn.cursor()
            
            if not evaluation_period:
                evaluation_period = f"{datetime.now().year}-Q{(datetime.now().month-1)//3 + 1}"
            
            cursor.execute("""
                INSERT OR REPLACE INTO dynamic_evaluations 
                (alternative_id, criteria_id, score, evaluator, evaluation_period, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (alternative_id, criteria_id, score, evaluator, evaluation_period, notes))
            
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error adding evaluation: {e}")
            return False
    
    def get_decision_matrix(self, evaluation_period: str = None) -> Dict:
        """Ambil matriks keputusan untuk TOPSIS"""
        try:
            cursor = self.conn.cursor()
            
            # Get alternatives
            alternatives = self.get_all_alternatives()
            
            # Get criteria
            criteria = self.get_all_criteria()
            
            # Get evaluations
            if evaluation_period:
                cursor.execute("""
                    SELECT alternative_id, criteria_id, score
                    FROM dynamic_evaluations
                    WHERE evaluation_period = ?
                """, (evaluation_period,))
            else:
                cursor.execute("""
                    SELECT alternative_id, criteria_id, score
                    FROM dynamic_evaluations
                """)
            
            evaluations = cursor.fetchall()
            
            # Build matrix
            matrix = {}
            for alt in alternatives:
                matrix[alt['id']] = {}
                for crit in criteria:
                    matrix[alt['id']][crit['id']] = None
            
            # Fill matrix with evaluation scores
            for eval_row in evaluations:
                alt_id = eval_row[0]
                crit_id = eval_row[1]
                score = eval_row[2]
                
                if alt_id in matrix and crit_id in matrix[alt_id]:
                    matrix[alt_id][crit_id] = score
            
            return {
                'alternatives': alternatives,
                'criteria': criteria,
                'matrix': matrix,
                'evaluation_period': evaluation_period
            }
        except Exception as e:
            print(f"❌ Error getting decision matrix: {e}")
            return {}
    
    # ============================================================
    # TOPSIS CALCULATION RESULTS
    # ============================================================
    
    def save_topsis_results(self, calculation_id: str, results: List[Dict], 
                           calculated_by: str = None) -> bool:
        """Simpan hasil perhitungan TOPSIS"""
        try:
            cursor = self.conn.cursor()
            
            # Clear previous results for this calculation
            cursor.execute("""
                DELETE FROM topsis_results WHERE calculation_id = ?
            """, (calculation_id,))
            
            # Insert new results
            for result in results:
                cursor.execute("""
                    INSERT INTO topsis_results 
                    (calculation_id, alternative_id, normalized_scores, weighted_scores,
                     positive_distance, negative_distance, preference_value, ranking, calculated_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    calculation_id,
                    result['alternative_id'],
                    json.dumps(result.get('normalized_scores', {})),
                    json.dumps(result.get('weighted_scores', {})),
                    result['positive_distance'],
                    result['negative_distance'],
                    result['preference_value'],
                    result['ranking'],
                    calculated_by
                ))
            
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error saving TOPSIS results: {e}")
            return False
    
    def get_latest_ranking(self) -> List[Dict]:
        """Ambil ranking terbaru"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT * FROM v_latest_ranking")
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"❌ Error getting latest ranking: {e}")
            return []
    
    def save_calculation_history(self, calculation_id: str, title: str, 
                               description: str = None, evaluation_period: str = None,
                               created_by: str = None) -> bool:
        """Simpan riwayat perhitungan"""
        try:
            cursor = self.conn.cursor()
            
            # Get current stats
            alternatives = self.get_all_alternatives()
            criteria = self.get_all_criteria()
            
            # Get criteria weights
            criteria_weights = {crit['code']: crit['weight'] for crit in criteria}
            
            cursor.execute("""
                INSERT OR REPLACE INTO calculation_history 
                (calculation_id, title, description, evaluation_period, 
                 total_alternatives, total_criteria, criteria_weights, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                calculation_id, title, description, evaluation_period,
                len(alternatives), len(criteria), 
                json.dumps(criteria_weights), created_by
            ))
            
            self.conn.commit()
            return True
        except Exception as e:
            print(f"❌ Error saving calculation history: {e}")
            return False
    
    # ============================================================
    # UTILITY FUNCTIONS
    # ============================================================
    
    def get_system_stats(self) -> Dict:
        """Ambil statistik sistem"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT * FROM v_system_stats")
            result = cursor.fetchone()
            return dict(result) if result else {}
        except Exception as e:
            print(f"❌ Error getting system stats: {e}")
            return {}
    
    def get_evaluation_completeness(self) -> List[Dict]:
        """Ambil kelengkapan evaluasi"""
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT * FROM v_evaluation_completeness")
            return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"❌ Error getting evaluation completeness: {e}")
            return []
    
    def backup_database(self, backup_path: str = None) -> bool:
        """Backup database"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backup_spk_topsis_{timestamp}.db"
            
            import shutil
            shutil.copy2(self.db_path, backup_path)
            print(f"✅ Database backup created: {backup_path}")
            return True
        except Exception as e:
            print(f"❌ Error creating backup: {e}")
            return False
    
    def vacuum_database(self) -> bool:
        """Optimize database"""
        try:
            self.conn.execute("VACUUM")
            self.conn.execute("ANALYZE")
            self.conn.commit()
            print("✅ Database optimized")
            return True
        except Exception as e:
            print(f"❌ Error optimizing database: {e}")
            return False

# ============================================================
# USAGE EXAMPLE
# ============================================================

def example_usage():
    """Contoh penggunaan database manager"""
    
    # Initialize database
    db = SPKTopsisDatabase()
    
    if not db.connect():
        print("❌ Gagal koneksi database")
        return
    
    try:
        # Setup database from SQL file
        if os.path.exists('SPK_TOPSIS_DATABASE_COMPLETE.sql'):
            print("📊 Setting up database from SQL file...")
            db.execute_sql_file('SPK_TOPSIS_DATABASE_COMPLETE.sql')
        
        # Test authentication
        user = db.authenticate_user('admin', 'admin123')
        if user:
            print(f"✅ Login berhasil: {user['username']} ({user['role']})")
        
        # Get system stats
        stats = db.get_system_stats()
        print(f"📈 Statistik: {stats}")
        
        # Get decision matrix
        matrix = db.get_decision_matrix()
        print(f"📊 Matrix: {len(matrix.get('alternatives', []))} alternatives, {len(matrix.get('criteria', []))} criteria")
        
        # Get latest ranking
        ranking = db.get_latest_ranking()
        if ranking:
            print("🏆 Latest ranking:")
            for rank in ranking:
                print(f"   {rank['ranking']}. {rank['karyawan']} - {rank['percentage']}%")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        db.close()

if __name__ == "__main__":
    example_usage()
