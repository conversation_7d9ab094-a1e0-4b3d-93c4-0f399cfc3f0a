"""
Enhanced Dashboard untuk SP<PERSON> Karyawan TOPSIS
Dashboard dengan statistik dan quick actions
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class EnhancedDashboard:
    def __init__(self, parent, db_manager):
        """Initialize enhanced dashboard"""
        self.parent = parent
        self.db_manager = db_manager
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        self.create_widgets()
        self.load_dashboard_data()
    
    def create_widgets(self):
        """Create dashboard widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="📊 Dashboard SP<PERSON> Karyawan TOPSIS", 
                 style='Title.TLabel').pack(side='left')
        
        ttk.Button(title_frame, text="🔄 Refresh", 
                  command=self.load_dashboard_data, style='Nav.TButton').pack(side='right')
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_rowconfigure(1, weight=1)
        main_container.grid_columnconfigure(0, weight=1)
        main_container.grid_columnconfigure(1, weight=1)
        
        # Statistics cards
        self.create_stats_cards(main_container)
        
        # Charts
        self.create_charts(main_container)
        
        # Quick actions
        self.create_quick_actions(main_container)
        
        # Recent activity
        self.create_recent_activity(main_container)
    
    def create_stats_cards(self, parent):
        """Create statistics cards"""
        stats_frame = ttk.LabelFrame(parent, text="📈 Statistik Sistem", padding=15)
        stats_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10), pady=(0, 10))
        
        # Configure grid
        stats_frame.grid_rowconfigure(0, weight=1)
        stats_frame.grid_rowconfigure(1, weight=1)
        stats_frame.grid_columnconfigure(0, weight=1)
        stats_frame.grid_columnconfigure(1, weight=1)
        
        # Cards data will be populated in load_dashboard_data
        self.stats_cards = {}
        
        # Create card frames
        card_configs = [
            ('criteria', '⚖️ Kriteria', 0, 0),
            ('alternatives', '👥 Alternatif', 0, 1),
            ('calculations', '🧮 Perhitungan', 1, 0),
            ('users', '👤 Users', 1, 1)
        ]
        
        for card_id, title, row, col in card_configs:
            card_frame = ttk.Frame(stats_frame, relief='raised', borderwidth=1)
            card_frame.grid(row=row, column=col, sticky='nsew', padx=5, pady=5)
            card_frame.grid_rowconfigure(0, weight=1)
            card_frame.grid_columnconfigure(0, weight=1)
            
            # Title
            ttk.Label(card_frame, text=title, font=('Segoe UI', 10, 'bold')).pack(pady=(10, 5))
            
            # Value
            value_var = tk.StringVar(value="0")
            value_label = ttk.Label(card_frame, textvariable=value_var, 
                                   font=('Segoe UI', 24, 'bold'), foreground='#2c3e50')
            value_label.pack()
            
            # Description
            desc_var = tk.StringVar(value="Loading...")
            desc_label = ttk.Label(card_frame, textvariable=desc_var, 
                                  font=('Segoe UI', 8), foreground='#7f8c8d')
            desc_label.pack(pady=(0, 10))
            
            self.stats_cards[card_id] = {
                'value_var': value_var,
                'desc_var': desc_var
            }
    
    def create_charts(self, parent):
        """Create charts section"""
        charts_frame = ttk.LabelFrame(parent, text="📊 Visualisasi Data", padding=10)
        charts_frame.grid(row=0, column=1, sticky='nsew', padx=(10, 0), pady=(0, 10))
        
        # Create matplotlib figure
        self.fig, (self.ax1, self.ax2) = plt.subplots(1, 2, figsize=(8, 4))
        self.fig.patch.set_facecolor('white')
        
        # Create canvas
        self.canvas = FigureCanvasTkAgg(self.fig, charts_frame)
        self.canvas.get_tk_widget().pack(fill='both', expand=True)
    
    def create_quick_actions(self, parent):
        """Create useful quick actions section"""
        actions_frame = ttk.LabelFrame(parent, text="⚡ Quick Actions", padding=15)
        actions_frame.grid(row=1, column=0, sticky='nsew', padx=(0, 10), pady=(10, 0))

        # Configure grid
        actions_frame.grid_rowconfigure(0, weight=1)
        actions_frame.grid_columnconfigure(0, weight=1)
        actions_frame.grid_columnconfigure(1, weight=1)

        # Only useful action buttons
        actions = [
            ("🧮 Hitung TOPSIS", self.calculate_topsis_direct, 0, 0),
            ("🔄 Refresh Data", self.load_dashboard_data, 0, 1)
        ]

        for text, command, row, col in actions:
            btn = ttk.Button(actions_frame, text=text, command=command,
                           style='Action.TButton')
            btn.grid(row=row, column=col, sticky='nsew', padx=5, pady=5)
    
    def create_recent_activity(self, parent):
        """Create recent activity section"""
        activity_frame = ttk.LabelFrame(parent, text="📝 Aktivitas Terbaru", padding=10)
        activity_frame.grid(row=1, column=1, sticky='nsew', padx=(10, 0), pady=(10, 0))
        
        # Activity list
        self.activity_tree = ttk.Treeview(activity_frame, columns=('Time', 'Action'), 
                                         show='headings', height=8)
        
        self.activity_tree.heading('Time', text='Waktu')
        self.activity_tree.heading('Action', text='Aktivitas')
        
        self.activity_tree.column('Time', width=120)
        self.activity_tree.column('Action', width=200)
        
        # Scrollbar
        activity_scrollbar = ttk.Scrollbar(activity_frame, orient='vertical', 
                                          command=self.activity_tree.yview)
        self.activity_tree.configure(yscrollcommand=activity_scrollbar.set)
        
        # Pack
        self.activity_tree.pack(side='left', fill='both', expand=True)
        activity_scrollbar.pack(side='right', fill='y')
    
    def load_dashboard_data(self):
        """Load dashboard data"""
        try:
            # Load statistics
            criteria = self.db_manager.get_all_criteria()
            alternatives = self.db_manager.get_all_alternatives()
            users = self.db_manager.get_all_users()
            history = self.db_manager.get_calculation_history()
            
            # Update stats cards
            self.stats_cards['criteria']['value_var'].set(str(len(criteria)))
            self.stats_cards['criteria']['desc_var'].set("Kriteria aktif")
            
            self.stats_cards['alternatives']['value_var'].set(str(len(alternatives)))
            self.stats_cards['alternatives']['desc_var'].set("Alternatif tersedia")
            
            self.stats_cards['calculations']['value_var'].set(str(len(history)))
            self.stats_cards['calculations']['desc_var'].set("Perhitungan dilakukan")
            
            self.stats_cards['users']['value_var'].set(str(len(users)))
            self.stats_cards['users']['desc_var'].set("User terdaftar")
            
            # Update charts
            self.update_charts(criteria, alternatives)
            
            # Update recent activity
            self.update_recent_activity(history)
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat data dashboard: {str(e)}")
    
    def update_charts(self, criteria, alternatives):
        """Update charts with current data"""
        try:
            # Clear previous plots
            self.ax1.clear()
            self.ax2.clear()
            
            # Chart 1: Criteria weights pie chart
            if criteria:
                labels = [c['nama'] for c in criteria]
                weights = [c['bobot'] for c in criteria]
                colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6']
                
                self.ax1.pie(weights, labels=labels, autopct='%1.1f%%', 
                           colors=colors[:len(criteria)], startangle=90)
                self.ax1.set_title('Distribusi Bobot Kriteria')
            else:
                self.ax1.text(0.5, 0.5, 'Belum ada kriteria', 
                            ha='center', va='center', transform=self.ax1.transAxes)
                self.ax1.set_title('Distribusi Bobot Kriteria')
            
            # Chart 2: Alternatives by position bar chart
            if alternatives:
                positions = {}
                for alt in alternatives:
                    pos = alt['posisi']
                    positions[pos] = positions.get(pos, 0) + 1
                
                pos_names = list(positions.keys())
                pos_counts = list(positions.values())
                
                bars = self.ax2.bar(range(len(pos_names)), pos_counts, 
                                  color='#3498db', alpha=0.7)
                self.ax2.set_xlabel('Posisi')
                self.ax2.set_ylabel('Jumlah')
                self.ax2.set_title('Distribusi Alternatif per Posisi')
                self.ax2.set_xticks(range(len(pos_names)))
                self.ax2.set_xticklabels(pos_names, rotation=45, ha='right')
                
                # Add value labels on bars
                for bar in bars:
                    height = bar.get_height()
                    self.ax2.text(bar.get_x() + bar.get_width()/2., height,
                                f'{int(height)}', ha='center', va='bottom')
            else:
                self.ax2.text(0.5, 0.5, 'Belum ada alternatif', 
                            ha='center', va='center', transform=self.ax2.transAxes)
                self.ax2.set_title('Distribusi Alternatif per Posisi')
            
            # Adjust layout and refresh
            self.fig.tight_layout()
            self.canvas.draw()
            
        except Exception as e:
            print(f"Error updating charts: {e}")
    
    def update_recent_activity(self, history):
        """Update recent activity list"""
        try:
            # Clear existing items
            for item in self.activity_tree.get_children():
                self.activity_tree.delete(item)
            
            # Add recent activities (limit to 10)
            recent_history = history[:10] if history else []
            
            for record in recent_history:
                time_str = record.get('created_at', '')[:16]  # YYYY-MM-DD HH:MM
                action = f"Perhitungan '{record.get('session_name', '')}' - {record.get('total_alternatives', 0)} alternatif"
                
                self.activity_tree.insert('', 'end', values=(time_str, action))
            
            if not recent_history:
                self.activity_tree.insert('', 'end', values=('', 'Belum ada aktivitas'))
                
        except Exception as e:
            print(f"Error updating recent activity: {e}")
    
    # Quick action methods
    def calculate_topsis_direct(self):
        """Quick action: Calculate TOPSIS directly"""
        try:
            # Check prerequisites
            criteria = self.db_manager.get_all_criteria()
            alternatives = self.db_manager.get_all_alternatives()

            if not criteria:
                messagebox.showwarning("⚠️ Peringatan",
                                     "Belum ada kriteria yang didefinisikan!\n\n"
                                     "Silakan tambah kriteria terlebih dahulu melalui menu 'Kelola Kriteria'.")
                return

            if not alternatives:
                messagebox.showwarning("⚠️ Peringatan",
                                     "Belum ada alternatif yang didefinisikan!\n\n"
                                     "Silakan tambah alternatif terlebih dahulu melalui menu 'Kelola Alternatif'.")
                return

            if len(alternatives) < 2:
                messagebox.showwarning("⚠️ Peringatan",
                                     "Minimal 2 alternatif diperlukan untuk perhitungan TOPSIS!")
                return

            # Check total weight
            total_weight = sum(c['bobot'] for c in criteria)
            if abs(total_weight - 1.0) > 0.001:
                messagebox.showwarning("⚠️ Peringatan",
                                     f"Total bobot kriteria harus 100%!\n\n"
                                     f"Saat ini: {total_weight*100:.1f}%\n"
                                     f"Silakan sesuaikan bobot melalui menu 'Kelola Kriteria'.")
                return

            # All checks passed - redirect to calculation page
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'show_topsis_calculation'):
                main_window.show_topsis_calculation()
            else:
                messagebox.showinfo("✅ Siap Hitung",
                                  f"Data sudah lengkap!\n\n"
                                  f"📊 Kriteria: {len(criteria)}\n"
                                  f"👥 Alternatif: {len(alternatives)}\n\n"
                                  f"Gunakan menu 'Hitung TOPSIS' untuk melakukan perhitungan.")

        except Exception as e:
            messagebox.showerror("❌ Error", f"Error checking prerequisites: {str(e)}")

    def get_main_window(self):
        """Get main window instance"""
        widget = self.parent
        while widget:
            if hasattr(widget, 'show_topsis_calculation') or hasattr(widget, 'show_results'):
                return widget
            widget = widget.master if hasattr(widget, 'master') else None
        return None
