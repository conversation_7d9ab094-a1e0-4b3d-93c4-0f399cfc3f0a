"""
Qt5 Login Window - Modern and Beautiful Login Interface
SPK Ka<PERSON>wan TOPSIS Enhanced v2.0
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                            QFrame, QCheckBox, QMessageBox, QGraphicsDropShadowEffect)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor, QIcon

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.enhanced_database_manager import EnhancedDatabaseManager

class ModernLoginWindow(QMainWindow):
    """Modern Qt5 Login Window with beautiful design"""
    
    # Signal emitted when login is successful
    login_successful = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.db_manager = EnhancedDatabaseManager()
        self.user_data = None
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """Setup the user interface"""
        self.setWindowTitle("🏢 SPK Karyawan TOPSIS - Login")
        self.setFixedSize(600, 750)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Center window on screen
        self.center_window()
        
        # Main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # Main layout
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # Create login card
        self.create_login_card(main_layout)
        
    def center_window(self):
        """Center window on screen"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def create_login_card(self, parent_layout):
        """Create the main login card"""
        # Login card frame
        self.login_card = QFrame()
        self.login_card.setObjectName("loginCard")
        self.login_card.setFixedSize(540, 690)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 10)
        self.login_card.setGraphicsEffect(shadow)
        
        parent_layout.addWidget(self.login_card, alignment=Qt.AlignCenter)
        
        # Card layout
        card_layout = QVBoxLayout(self.login_card)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(0)
        
        # Create header
        self.create_header(card_layout)
        
        # Create form
        self.create_form(card_layout)
        
        # Create footer
        self.create_footer(card_layout)
    
    def create_header(self, parent_layout):
        """Create the header section"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setFixedHeight(180)
        parent_layout.addWidget(header_frame)
        
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(40, 30, 40, 30)
        
        # Title
        title_label = QLabel("🏆 SPK TOPSIS")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        # Subtitle
        subtitle_label = QLabel("Sistem Pendukung Keputusan Enhanced v2.0")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)
        
        # Description
        desc_label = QLabel("Pengangkatan Karyawan Tetap - Metode TOPSIS")
        desc_label.setObjectName("descLabel")
        desc_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(desc_label)
    
    def create_form(self, parent_layout):
        """Create the login form"""
        form_frame = QFrame()
        form_frame.setObjectName("formFrame")
        parent_layout.addWidget(form_frame)
        
        form_layout = QVBoxLayout(form_frame)
        form_layout.setContentsMargins(40, 30, 40, 30)
        form_layout.setSpacing(20)
        
        # Login title
        login_title = QLabel("🔐 Login to Continue")
        login_title.setObjectName("loginTitle")
        login_title.setAlignment(Qt.AlignCenter)
        form_layout.addWidget(login_title)
        
        # Username section
        username_label = QLabel("👤 Username")
        username_label.setObjectName("fieldLabel")
        form_layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setObjectName("modernInput")
        self.username_input.setPlaceholderText("Enter your username")
        self.username_input.setFixedHeight(50)
        form_layout.addWidget(self.username_input)
        
        # Password section
        password_label = QLabel("🔒 Password")
        password_label.setObjectName("fieldLabel")
        form_layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setObjectName("modernInput")
        self.password_input.setPlaceholderText("Enter your password")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFixedHeight(50)
        form_layout.addWidget(self.password_input)
        
        # Show password checkbox
        self.show_password_cb = QCheckBox("👁️ Show Password")
        self.show_password_cb.setObjectName("modernCheckbox")
        self.show_password_cb.toggled.connect(self.toggle_password_visibility)
        form_layout.addWidget(self.show_password_cb)
        
        # Login button
        self.login_btn = QPushButton("🚀 Login")
        self.login_btn.setObjectName("primaryButton")
        self.login_btn.setFixedHeight(55)
        self.login_btn.clicked.connect(self.handle_login)
        form_layout.addWidget(self.login_btn)
        
        # Connect Enter key
        self.username_input.returnPressed.connect(self.handle_login)
        self.password_input.returnPressed.connect(self.handle_login)
        
        # Set focus
        self.username_input.setFocus()
    
    def create_footer(self, parent_layout):
        """Create the footer section"""
        footer_frame = QFrame()
        footer_frame.setObjectName("footerFrame")
        footer_frame.setFixedHeight(200)
        parent_layout.addWidget(footer_frame)
        
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setContentsMargins(40, 20, 40, 20)
        footer_layout.setSpacing(15)
        
        # Credentials info
        creds_label = QLabel("💡 Demo Credentials:")
        creds_label.setObjectName("credsTitle")
        footer_layout.addWidget(creds_label)
        
        # Admin credentials
        admin_frame = QFrame()
        admin_frame.setObjectName("credFrame")
        admin_layout = QVBoxLayout(admin_frame)
        admin_layout.setContentsMargins(15, 10, 15, 10)
        
        admin_title = QLabel("👑 Administrator (Full Access)")
        admin_title.setObjectName("credTitle")
        admin_layout.addWidget(admin_title)
        
        admin_creds = QLabel("Username: admin | Password: admin123")
        admin_creds.setObjectName("credText")
        admin_layout.addWidget(admin_creds)
        
        footer_layout.addWidget(admin_frame)
        
        # Operator credentials
        operator_frame = QFrame()
        operator_frame.setObjectName("credFrame")
        operator_layout = QVBoxLayout(operator_frame)
        operator_layout.setContentsMargins(15, 10, 15, 10)
        
        operator_title = QLabel("⚙️ Data Operator (Input Only)")
        operator_title.setObjectName("credTitle")
        operator_layout.addWidget(operator_title)
        
        operator_creds = QLabel("Username: operator | Password: operator123")
        operator_creds.setObjectName("credText")
        operator_layout.addWidget(operator_creds)
        
        footer_layout.addWidget(operator_frame)
        
        # Author info
        author_label = QLabel("© 2024 Muhammad Bayu Prasetyo Wibowo - 211011450583 - 06TPLP003")
        author_label.setObjectName("authorLabel")
        author_label.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(author_label)
    
    def toggle_password_visibility(self, checked):
        """Toggle password visibility"""
        if checked:
            self.password_input.setEchoMode(QLineEdit.Normal)
        else:
            self.password_input.setEchoMode(QLineEdit.Password)
    
    def handle_login(self):
        """Handle login attempt"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            self.show_error("❌ Login Failed", "Please enter both username and password!")
            return
        
        # Authenticate user
        user_data = self.db_manager.authenticate_user(username, password)
        
        if user_data:
            self.user_data = user_data
            
            # Show success message
            role_icon = "👑" if user_data['role'] == 'admin' else "⚙️"
            role_name = "Administrator" if user_data['role'] == 'admin' else "Data Operator"
            
            self.show_success("✅ Login Successful!", 
                            f"Welcome, {user_data['full_name']}!\n\n"
                            f"{role_icon} Role: {role_name}\n"
                            f"🚀 Starting SPK Application...")
            
            # Emit signal and close
            self.login_successful.emit(user_data)
            self.close()
        else:
            self.show_error("❌ Login Failed",
                          "Invalid username or password!\n\n"
                          "💡 Try these credentials:\n\n"
                          "👑 Admin Access:\n"
                          "   Username: admin\n"
                          "   Password: admin123\n\n"
                          "⚙️ Operator Access:\n"
                          "   Username: operator\n"
                          "   Password: operator123")
    
    def show_success(self, title, message):
        """Show success message"""
        msg = QMessageBox(self)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Information)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #f0f9ff;
                color: #1e40af;
            }
            QMessageBox QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
        """)
        msg.exec_()
    
    def show_error(self, title, message):
        """Show error message"""
        msg = QMessageBox(self)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Critical)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #fef2f2;
                color: #dc2626;
            }
            QMessageBox QPushButton {
                background-color: #ef4444;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
        """)
        msg.exec_()
    
    def apply_styles(self):
        """Apply modern styles to the window"""
        self.setStyleSheet("""
            /* Login Card */
            #loginCard {
                background-color: white;
                border-radius: 20px;
                border: 1px solid #e5e7eb;
            }
            
            /* Header */
            #headerFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #1d4ed8);
                border-top-left-radius: 20px;
                border-top-right-radius: 20px;
            }
            
            #titleLabel {
                color: white;
                font-size: 28px;
                font-weight: bold;
                margin-bottom: 5px;
            }
            
            #subtitleLabel {
                color: #dbeafe;
                font-size: 14px;
                font-weight: 500;
            }
            
            #descLabel {
                color: #bfdbfe;
                font-size: 12px;
                margin-top: 5px;
            }
            
            /* Form */
            #formFrame {
                background-color: white;
            }
            
            #loginTitle {
                color: #1f2937;
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 10px;
            }
            
            #fieldLabel {
                color: #374151;
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 5px;
            }
            
            #modernInput {
                border: 2px solid #e5e7eb;
                border-radius: 10px;
                padding: 12px 16px;
                font-size: 14px;
                background-color: #f9fafb;
                color: #1f2937;
            }
            
            #modernInput:focus {
                border-color: #3b82f6;
                background-color: white;
                outline: none;
            }
            
            #modernCheckbox {
                color: #6b7280;
                font-size: 12px;
                spacing: 8px;
            }
            
            #modernCheckbox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid #d1d5db;
            }
            
            #modernCheckbox::indicator:checked {
                background-color: #3b82f6;
                border-color: #3b82f6;
            }
            
            #primaryButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3b82f6, stop:1 #1d4ed8);
                color: white;
                border: none;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
            }
            
            #primaryButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2563eb, stop:1 #1e40af);
            }
            
            #primaryButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1d4ed8, stop:1 #1e3a8a);
            }
            
            /* Footer */
            #footerFrame {
                background-color: #f8fafc;
                border-bottom-left-radius: 20px;
                border-bottom-right-radius: 20px;
            }
            
            #credsTitle {
                color: #374151;
                font-size: 13px;
                font-weight: bold;
            }
            
            #credFrame {
                background-color: #f1f5f9;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                margin: 2px 0px;
            }
            
            #credTitle {
                color: #475569;
                font-size: 12px;
                font-weight: bold;
            }
            
            #credText {
                color: #64748b;
                font-size: 11px;
                font-family: 'Consolas', monospace;
            }
            
            #authorLabel {
                color: #9ca3af;
                font-size: 10px;
                margin-top: 10px;
            }
        """)

def show_qt_login():
    """Show Qt5 login window and return user data if successful"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    login_window = ModernLoginWindow()
    login_window.show()
    
    # Wait for login result
    user_data = None
    
    def on_login_success(data):
        nonlocal user_data
        user_data = data
        app.quit()
    
    login_window.login_successful.connect(on_login_success)
    
    app.exec_()
    return user_data

if __name__ == "__main__":
    user_data = show_qt_login()
    if user_data:
        print(f"Login successful: {user_data}")
    else:
        print("Login cancelled or failed")
