# SPK TOPSIS Enhanced v2.0

## 📋 Deskripsi Proyek

Sistem Pendukung Keputusan (SPK) untuk Pengangkatan Karyawan Tetap menggunakan metode TOPSIS (Technique for Order Preference by Similarity to Ideal Solution). Aplikasi ini dikembangkan sebagai tugas UAS mata kuliah Sistem Penunjang Keputusan.

## 👨‍💻 Informasi Pengembang

- **Nama:** <PERSON>ibowo
- **NIM:** 211011450583
- **Kelas:** 06TPLP003
- **Mata Kuliah:** Sistem Penunjang Keputusan
- **Tahun:** 2024

## 🎯 Fitur Utama

### 🔐 Sistem Autentikasi
- Login dengan username dan password
- Role-based access control (Admin & Operator)
- Session management

### 📊 Manajemen Kriteria
- Tambah, edit, hapus kriteria penilaian
- Pengaturan bobot kriteria dengan validasi 100%
- Support kriteria benefit dan cost
- Validasi real-time

### 👥 Manajemen Alternatif
- <PERSON><PERSON>, edit, hapus data karyawan
- Input nilai untuk setiap kriteria
- Validasi data komprehensif
- Import/export data

### 🧮 Perhitungan TOPSIS
- Implementasi algoritma TOPSIS lengkap
- Normalisasi matriks keputusan
- Pembobotan kriteria
- Perhitungan jarak ideal positif dan negatif
- Ranking otomatis

### 📈 Dashboard & Visualisasi
- Dashboard overview dengan statistik
- Grafik distribusi bobot kriteria
- Chart hasil ranking
- Real-time data updates

### 📤 Export & Reporting
- Export hasil ke Excel (.xlsx)
- Export ke CSV
- Laporan lengkap dengan detail perhitungan
- Template yang dapat dikustomisasi

## 🛠️ Teknologi yang Digunakan

### Backend
- **Python 3.8+**
- **SQLite** - Database
- **NumPy** - Perhitungan numerik
- **Pandas** - Manipulasi data

### Frontend
- **Tkinter** - GUI framework utama
- **CustomTkinter** - Modern UI components
- **TTKBootstrap** - Enhanced themes
- **Matplotlib** - Visualisasi data

### Libraries Tambahan
- **OpenPyXL** - Excel file handling
- **Pillow** - Image processing
- **Hashlib** - Password hashing

## 📦 Instalasi

### Metode 1: Executable (Recommended)
1. Download file `SPK_TOPSIS_Enhanced_v2.0.exe`
2. Jalankan file executable
3. Ikuti panduan setup database jika diperlukan

### Metode 2: Source Code
1. Clone atau download repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Setup database:
   ```bash
   python setup_database_with_sample_data.py
   ```
4. Jalankan aplikasi:
   ```bash
   python main_modern.py
   ```

## 🔑 Login Credentials

### Administrator (Full Access)
- **Username:** `admin`
- **Password:** `admin123`
- **Permissions:** Semua fitur tersedia

### Data Operator (Limited Access)
- **Username:** `operator`
- **Password:** `operator123`
- **Permissions:** Input data saja

## 📖 Cara Penggunaan

### 1. Login
- Jalankan aplikasi
- Masukkan username dan password
- Pilih role yang sesuai

### 2. Setup Kriteria
- Masuk ke menu "Kelola Kriteria"
- Tambah kriteria penilaian
- Atur bobot (total harus 100%)
- Tentukan jenis (benefit/cost)

### 3. Input Data Karyawan
- Masuk ke menu "Kelola Alternatif"
- Tambah data karyawan
- Input nilai untuk setiap kriteria
- Validasi data otomatis

### 4. Hitung Ranking
- Masuk ke menu "Hitung TOPSIS"
- Sistem akan memvalidasi data
- Proses perhitungan otomatis
- Hasil ranking tersimpan

### 5. Lihat Hasil
- Masuk ke menu "Hasil TOPSIS"
- Lihat ranking karyawan
- Analisis detail perhitungan
- Export laporan

## 🎨 Interface Versions

Aplikasi tersedia dalam 3 versi interface:

1. **Modern UI** (`main_modern.py`) - CustomTkinter dengan desain modern
2. **Enhanced UI** (`main_enhanced.py`) - Tkinter dengan styling enhanced
3. **Qt5 UI** (`main_qt5.py`) - PyQt5 dengan native look

## 📊 Metode TOPSIS

### Langkah-langkah Perhitungan:
1. **Normalisasi Matriks** - Menggunakan metode euclidean
2. **Pembobotan** - Mengalikan dengan bobot kriteria
3. **Ideal Solution** - Menentukan solusi ideal positif dan negatif
4. **Jarak Euclidean** - Menghitung jarak ke ideal solution
5. **Preference Score** - Menghitung skor preferensi
6. **Ranking** - Mengurutkan berdasarkan skor tertinggi

## 🔧 Troubleshooting

### Aplikasi tidak bisa jalan
- Pastikan Python 3.8+ terinstall
- Install semua dependencies
- Jalankan sebagai Administrator

### Error database
- Jalankan setup database ulang
- Pastikan file database tidak corrupt
- Check permissions folder

### UI tidak muncul
- Install CustomTkinter: `pip install customtkinter`
- Update Tkinter ke versi terbaru
- Check display settings

## 📁 Struktur Project

```
SPK_TOPSIS_Enhanced_v2.0/
├── main_modern.py              # Entry point modern UI
├── main_enhanced.py            # Entry point enhanced UI
├── main_qt5.py                # Entry point Qt5 UI
├── database/                   # Database management
│   ├── enhanced_database_manager.py
│   └── spk_karyawan_enhanced.db
├── core/                      # Core algorithms
│   └── enhanced_topsis_calculator.py
├── ui/                        # UI components
│   ├── enhanced_dashboard.py
│   ├── dynamic_criteria_manager.py
│   └── dynamic_alternatives_manager.py
├── modern_ui/                 # Modern UI components
├── qt_ui/                     # Qt5 UI components
├── utils/                     # Utilities
│   ├── error_handler.py
│   └── ui_helpers.py
└── requirements.txt           # Dependencies
```

## 📈 Sample Data

Aplikasi dilengkapi dengan sample data:
- **5 Kriteria:** Kemampuan Teknik, Kualitas, Presisi, Pelanggaran, Kehadiran
- **8 Karyawan:** Data lengkap untuk testing
- **Bobot Seimbang:** Distribusi bobot yang realistis

## 🚀 Build Executable

Untuk membuat file executable:

```bash
python build_executable.py
```

Output:
- `dist/SPK_TOPSIS_Enhanced_v2.0.exe`
- `SPK_TOPSIS_Enhanced_v2.0_Portable/` (portable package)

## 📞 Support & Contact

Jika ada pertanyaan atau masalah:
- **Email:** [Contact Developer]
- **GitHub:** [Repository Link]
- **Documentation:** Lihat file USER_GUIDE.md

## 📄 License

Proyek ini dikembangkan untuk keperluan akademik.
© 2024 Muhammad Bayu Prasetyo Wibowo

---

**Terima kasih telah menggunakan SPK TOPSIS Enhanced v2.0!** 🎉
