"""
Login Window untuk SP<PERSON> Karyawan TOPSIS
Sistem autentikasi dengan username dan password
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.enhanced_database_manager import EnhancedDatabaseManager
from ui.modern_theme import ModernTheme

class LoginWindow:
    def __init__(self):
        """Initialize login window"""
        self.db_manager = EnhancedDatabaseManager()
        self.user_data = None
        self.login_successful = False
        
        self.create_login_window()
    
    def create_login_window(self):
        """Create modern login window interface"""
        self.root = tk.Tk()
        self.root.title("🏢 SPK Karyawan TOPSIS - Login")
        self.root.geometry("550x700")
        self.root.resizable(False, False)

        # Set modern background
        self.root.configure(bg='#f8fafc')

        # Center window
        self.center_window()

        # Initialize modern theme
        self.theme = ModernTheme()
        self.style = self.theme.apply_modern_style(self.root)
        
        # Create modern main container
        self.create_modern_container()
        
        # Bind Enter key
        self.root.bind('<Return>', lambda e: self.login())
        
        # Focus on username entry
        self.username_entry.focus()
    
    def center_window(self):
        """Center window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_modern_container(self):
        """Create modern login container with beautiful design"""
        # Main container with gradient-like background
        main_container = tk.Frame(self.root, bg='#f8fafc')
        main_container.pack(fill='both', expand=True, padx=40, pady=40)

        # Login card with shadow effect
        login_card = tk.Frame(main_container,
                             bg='white',
                             relief='solid',
                             borderwidth=1,
                             highlightbackground='#e2e8f0',
                             highlightthickness=1)
        login_card.pack(fill='both', expand=True, padx=20, pady=20)

        # Header section with gradient-like effect
        self.create_modern_header(login_card)

        # Login form section
        self.create_modern_form(login_card)

        # Footer section
        self.create_modern_footer(login_card)

    def create_modern_header(self, parent):
        """Create modern header with beautiful styling"""
        # Header background with color
        header_frame = tk.Frame(parent, bg='#3b82f6', height=120)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)

        # Icon and title container
        content_frame = tk.Frame(header_frame, bg='#3b82f6')
        content_frame.pack(expand=True, fill='both')

        # Large icon
        icon_label = tk.Label(content_frame,
                             text="🏢",
                             font=('Segoe UI', 56),
                             bg='#3b82f6',
                             fg='white')
        icon_label.pack(pady=(20, 5))

        # Title
        title_label = tk.Label(content_frame,
                              text="SPK Karyawan TOPSIS",
                              font=('Segoe UI', 18, 'bold'),
                              bg='#3b82f6',
                              fg='white')
        title_label.pack()

        # Subtitle
        subtitle_label = tk.Label(content_frame,
                                 text="Sistem Pendukung Keputusan Enhanced v2.0",
                                 font=('Segoe UI', 11),
                                 bg='#3b82f6',
                                 fg='#dbeafe')
        subtitle_label.pack(pady=(2, 15))

    def create_modern_form(self, parent):
        """Create modern login form with beautiful styling"""
        # Form container
        form_frame = tk.Frame(parent, bg='white')
        form_frame.pack(fill='both', expand=True, padx=40, pady=30)

        # Login title
        login_title = tk.Label(form_frame,
                              text="🔐 Login to Continue",
                              font=('Segoe UI', 16, 'bold'),
                              bg='white',
                              fg='#1f2937')
        login_title.pack(pady=(0, 25))

        # Username section
        username_label = tk.Label(form_frame,
                                 text="👤 Username",
                                 font=('Segoe UI', 12, 'bold'),
                                 bg='white',
                                 fg='#374151')
        username_label.pack(anchor='w', pady=(0, 8))

        self.username_entry = tk.Entry(form_frame,
                                      font=('Segoe UI', 12),
                                      bg='#f9fafb',
                                      fg='#1f2937',
                                      relief='solid',
                                      borderwidth=2,
                                      highlightbackground='#d1d5db',
                                      highlightcolor='#3b82f6',
                                      highlightthickness=2)
        self.username_entry.pack(fill='x', pady=(0, 20), ipady=12)

        # Password section
        password_label = tk.Label(form_frame,
                                 text="🔒 Password",
                                 font=('Segoe UI', 12, 'bold'),
                                 bg='white',
                                 fg='#374151')
        password_label.pack(anchor='w', pady=(0, 8))

        self.password_entry = tk.Entry(form_frame,
                                      font=('Segoe UI', 12),
                                      bg='#f9fafb',
                                      fg='#1f2937',
                                      relief='solid',
                                      borderwidth=2,
                                      highlightbackground='#d1d5db',
                                      highlightcolor='#3b82f6',
                                      highlightthickness=2,
                                      show='*')
        self.password_entry.pack(fill='x', pady=(0, 15), ipady=12)

        # Show password checkbox
        checkbox_frame = tk.Frame(form_frame, bg='white')
        checkbox_frame.pack(fill='x', pady=(0, 25))

        self.show_password_var = tk.BooleanVar()
        show_password_cb = tk.Checkbutton(checkbox_frame,
                                         text="👁️ Show Password",
                                         variable=self.show_password_var,
                                         command=self.toggle_password_visibility,
                                         font=('Segoe UI', 10),
                                         bg='white',
                                         fg='#6b7280',
                                         activebackground='white',
                                         activeforeground='#3b82f6',
                                         selectcolor='white')
        show_password_cb.pack(anchor='w')

        # Login button with modern styling
        login_btn = tk.Button(form_frame,
                             text="🚀 Login",
                             font=('Segoe UI', 14, 'bold'),
                             bg='#3b82f6',
                             fg='white',
                             relief='flat',
                             borderwidth=0,
                             cursor='hand2',
                             command=self.login)
        login_btn.pack(fill='x', pady=(10, 0), ipady=15)

        # Hover effects for login button
        def on_enter(e):
            login_btn.configure(bg='#2563eb')

        def on_leave(e):
            login_btn.configure(bg='#3b82f6')

        login_btn.bind("<Enter>", on_enter)
        login_btn.bind("<Leave>", on_leave)

    def create_modern_footer(self, parent):
        """Create modern footer with credentials info"""
        # Footer container
        footer_frame = tk.Frame(parent, bg='white')
        footer_frame.pack(fill='x', padx=40, pady=(0, 30))

        # Separator line
        separator = tk.Frame(footer_frame, bg='#e5e7eb', height=1)
        separator.pack(fill='x', pady=(0, 20))

        # Credentials info card
        info_card = tk.Frame(footer_frame,
                            bg='#f8fafc',
                            relief='solid',
                            borderwidth=1,
                            highlightbackground='#e2e8f0')
        info_card.pack(fill='x', pady=(0, 15))

        # Info title
        info_title = tk.Label(info_card,
                             text="ℹ️ Default Login Credentials",
                             font=('Segoe UI', 11, 'bold'),
                             bg='#f8fafc',
                             fg='#374151')
        info_title.pack(pady=(15, 10))

        # Admin credentials
        admin_frame = tk.Frame(info_card, bg='#f8fafc')
        admin_frame.pack(fill='x', padx=20, pady=5)

        tk.Label(admin_frame,
                text="👑 Admin:",
                font=('Segoe UI', 10, 'bold'),
                bg='#f8fafc',
                fg='#3b82f6').pack(anchor='w')

        tk.Label(admin_frame,
                text="Username: admin | Password: admin123",
                font=('Consolas', 9),
                bg='#f8fafc',
                fg='#6b7280').pack(anchor='w', padx=(20, 0))

        # Operator credentials
        operator_frame = tk.Frame(info_card, bg='#f8fafc')
        operator_frame.pack(fill='x', padx=20, pady=(5, 15))

        tk.Label(operator_frame,
                text="⚙️ Operator:",
                font=('Segoe UI', 10, 'bold'),
                bg='#f8fafc',
                fg='#7c3aed').pack(anchor='w')

        tk.Label(operator_frame,
                text="Username: operator | Password: operator123",
                font=('Consolas', 9),
                bg='#f8fafc',
                fg='#6b7280').pack(anchor='w', padx=(20, 0))

        # Author info
        author_label = tk.Label(footer_frame,
                               text="© 2024 Muhammad Bayu Prasetyo Wibowo - 211011450583 - 06TPLP003",
                               font=('Segoe UI', 8),
                               bg='white',
                               fg='#9ca3af')
        author_label.pack(pady=(10, 0))
    

    
    def toggle_password_visibility(self):
        """Toggle password visibility"""
        if self.show_password_var.get():
            self.password_entry.configure(show='')
        else:
            self.password_entry.configure(show='*')
    
    def login(self):
        """Handle modern login process with enhanced feedback"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username or not password:
            self.show_modern_error("❌ Input Required",
                                  "Please enter both username and password!")
            return

        # Show loading effect (change button text temporarily)
        original_text = "🚀 Login"

        # Authenticate user
        user_data = self.db_manager.authenticate_user(username, password)

        if user_data:
            self.user_data = user_data
            self.login_successful = True

            # Show success message with role info
            role_icon = "👑" if user_data['role'] == 'admin' else "⚙️"
            role_name = "Administrator" if user_data['role'] == 'admin' else "Data Operator"

            self.show_modern_success("✅ Login Successful!",
                                   f"Welcome, {user_data['full_name']}!\n\n"
                                   f"{role_icon} Role: {role_name}\n"
                                   f"🚀 Starting SPK Application...")

            self.root.destroy()
        else:
            # Show error with helpful information
            self.show_modern_error("❌ Login Failed",
                                 "Invalid username or password!\n\n"
                                 "💡 Try these credentials:\n\n"
                                 "👑 Admin Access:\n"
                                 "   Username: admin\n"
                                 "   Password: admin123\n\n"
                                 "⚙️ Operator Access:\n"
                                 "   Username: operator\n"
                                 "   Password: operator123")

            # Clear password field and focus username
            self.password_entry.delete(0, tk.END)
            self.username_entry.focus()

    def show_modern_error(self, title, message):
        """Show modern error dialog"""
        messagebox.showerror(title, message)

    def show_modern_success(self, title, message):
        """Show modern success dialog"""
        messagebox.showinfo(title, message)
    
    def run(self):
        """Run login window"""
        self.root.mainloop()
        return self.login_successful, self.user_data, self.db_manager

def show_login():
    """Show login window and return result"""
    login_window = LoginWindow()
    return login_window.run()

if __name__ == "__main__":
    success, user, db = show_login()
    if success:
        print(f"Login successful: {user}")
    else:
        print("Login cancelled or failed")
