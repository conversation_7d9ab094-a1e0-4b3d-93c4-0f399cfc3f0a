"""
Dynamic Criteria Manager
Mengelola kriteria secara dinamis dengan CRUD operations
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class DynamicCriteriaManager:
    def __init__(self, parent, db_manager):
        """Initialize dynamic criteria manager"""
        self.parent = parent
        self.db_manager = db_manager
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        self.create_widgets()
        self.load_criteria()
    
    def create_widgets(self):
        """Create criteria manager widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="⚖️ Kelola Kriteria TOPSIS", 
                 style='Title.TLabel').pack(side='left')
        
        # Only show add button if user has permission
        if self.db_manager.has_permission('add_criteria'):
            ttk.But<PERSON>(title_frame, text="➕ Tambah Kriteria",
                      command=self.add_criteria, style='Nav.TButton').pack(side='right')
        else:
            ttk.Label(title_frame, text="👁️ View Only (Operator)",
                     style='Subtitle.TLabel').pack(side='right')
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=2)
        main_container.grid_columnconfigure(1, weight=1)
        
        # Left side - Criteria table
        self.create_criteria_table(main_container)
        
        # Right side - Form
        self.create_criteria_form(main_container)
    
    def create_criteria_table(self, parent):
        """Create criteria table"""
        table_frame = ttk.LabelFrame(parent, text="📋 Daftar Kriteria", padding=10)
        table_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))
        
        # Configure grid
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview
        columns = ('ID', 'Kode', 'Nama', 'Bobot', '%', 'Jenis', 'Min', 'Max')
        self.criteria_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {
            'ID': 50, 'Kode': 60, 'Nama': 200, 'Bobot': 80, 
            '%': 60, 'Jenis': 80, 'Min': 50, 'Max': 50
        }
        
        for col in columns:
            self.criteria_tree.heading(col, text=col)
            self.criteria_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.criteria_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.criteria_tree.xview)
        self.criteria_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.criteria_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Bind events
        self.criteria_tree.bind('<<TreeviewSelect>>', self.on_criteria_select)
        self.criteria_tree.bind('<Double-1>', self.edit_criteria)
        
        # Context menu
        self.create_context_menu()
        
        # Status
        self.status_var = tk.StringVar()
        status_label = ttk.Label(table_frame, textvariable=self.status_var, 
                               style='Info.TLabel')
        status_label.grid(row=2, column=0, columnspan=2, pady=5)
    
    def create_context_menu(self):
        """Create context menu for criteria table"""
        self.context_menu = tk.Menu(self.criteria_tree, tearoff=0)
        self.context_menu.add_command(label="✏️ Edit", command=self.edit_criteria)
        self.context_menu.add_command(label="🗑️ Hapus", command=self.delete_criteria)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🔄 Refresh", command=self.load_criteria)
        
        def show_context_menu(event):
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()
        
        self.criteria_tree.bind("<Button-3>", show_context_menu)
    
    def create_criteria_form(self, parent):
        """Create criteria form"""
        form_frame = ttk.LabelFrame(parent, text="📝 Form Kriteria", padding=15)
        form_frame.grid(row=0, column=1, sticky='nsew')
        
        # Form fields
        self.create_form_fields(form_frame)
        
        # Buttons
        self.create_form_buttons(form_frame)
        
        # Weight validation info
        self.create_weight_info(form_frame)
    
    def create_form_fields(self, parent):
        """Create form input fields"""
        # Kode
        ttk.Label(parent, text="🔤 Kode Kriteria:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.kode_var = tk.StringVar()
        self.kode_entry = ttk.Entry(parent, textvariable=self.kode_var, width=30)
        self.kode_entry.pack(fill='x', pady=(0, 10))
        
        # Nama
        ttk.Label(parent, text="📝 Nama Kriteria:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.nama_var = tk.StringVar()
        self.nama_entry = ttk.Entry(parent, textvariable=self.nama_var, width=30)
        self.nama_entry.pack(fill='x', pady=(0, 10))
        
        # Bobot
        ttk.Label(parent, text="⚖️ Bobot (0.0 - 1.0):", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.bobot_var = tk.DoubleVar()
        self.bobot_entry = ttk.Entry(parent, textvariable=self.bobot_var, width=30)
        self.bobot_entry.pack(fill='x', pady=(0, 10))
        
        # Jenis
        ttk.Label(parent, text="🎯 Jenis Kriteria:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.jenis_var = tk.StringVar(value='benefit')
        jenis_frame = ttk.Frame(parent)
        jenis_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Radiobutton(jenis_frame, text="📈 Benefit (semakin tinggi semakin baik)", 
                       variable=self.jenis_var, value='benefit').pack(anchor='w')
        ttk.Radiobutton(jenis_frame, text="📉 Cost (semakin rendah semakin baik)", 
                       variable=self.jenis_var, value='cost').pack(anchor='w')
        
        # Range nilai
        range_frame = ttk.LabelFrame(parent, text="📊 Range Nilai", padding=10)
        range_frame.pack(fill='x', pady=(0, 10))
        
        # Min value
        ttk.Label(range_frame, text="Min:").pack(side='left')
        self.min_value_var = tk.DoubleVar(value=1.0)
        min_entry = ttk.Entry(range_frame, textvariable=self.min_value_var, width=8)
        min_entry.pack(side='left', padx=(5, 10))
        
        # Max value
        ttk.Label(range_frame, text="Max:").pack(side='left')
        self.max_value_var = tk.DoubleVar(value=15.0)
        max_entry = ttk.Entry(range_frame, textvariable=self.max_value_var, width=8)
        max_entry.pack(side='left', padx=5)
        
        # Deskripsi
        ttk.Label(parent, text="📄 Deskripsi:", font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        self.deskripsi_text = tk.Text(parent, height=4, width=30, wrap='word')
        self.deskripsi_text.pack(fill='x', pady=(0, 10))
    
    def create_form_buttons(self, parent):
        """Create form buttons"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill='x', pady=10)
        
        self.save_btn = ttk.Button(button_frame, text="💾 Simpan", 
                                  command=self.save_criteria, style='Action.TButton')
        self.save_btn.pack(side='left', padx=(0, 5))
        
        self.clear_btn = ttk.Button(button_frame, text="🗑️ Clear", 
                                   command=self.clear_form, style='Nav.TButton')
        self.clear_btn.pack(side='left', padx=5)
        
        self.auto_balance_btn = ttk.Button(button_frame, text="⚖️ Auto Balance", 
                                          command=self.auto_balance_weights, style='Nav.TButton')
        self.auto_balance_btn.pack(side='left', padx=5)
    
    def create_weight_info(self, parent):
        """Create weight validation info"""
        info_frame = ttk.LabelFrame(parent, text="ℹ️ Validasi Bobot", padding=10)
        info_frame.pack(fill='x', pady=(10, 0))
        
        self.total_weight_var = tk.StringVar()
        self.weight_status_var = tk.StringVar()
        
        ttk.Label(info_frame, text="Total Bobot:").pack(anchor='w')
        ttk.Label(info_frame, textvariable=self.total_weight_var, 
                 font=('Consolas', 10, 'bold')).pack(anchor='w')
        
        ttk.Label(info_frame, textvariable=self.weight_status_var, 
                 font=('Segoe UI', 9)).pack(anchor='w', pady=(5, 0))
        
        # Update weight info periodically
        self.update_weight_info()
    
    def update_weight_info(self):
        """Update weight validation info"""
        try:
            criteria = self.db_manager.get_all_criteria()
            total_weight = sum(c['bobot'] for c in criteria)
            percentage = total_weight * 100
            
            self.total_weight_var.set(f"{total_weight:.3f} ({percentage:.1f}%)")
            
            if abs(total_weight - 1.0) < 0.001:
                self.weight_status_var.set("✅ Bobot valid (100%)")
            else:
                self.weight_status_var.set("⚠️ Bobot harus total 100%")
            
        except Exception:
            self.total_weight_var.set("Error")
            self.weight_status_var.set("❌ Error validasi")
        
        # Schedule next update
        self.frame.after(2000, self.update_weight_info)
    
    def load_criteria(self):
        """Load criteria data"""
        try:
            # Clear existing items
            for item in self.criteria_tree.get_children():
                self.criteria_tree.delete(item)
            
            # Load criteria from database
            criteria = self.db_manager.get_all_criteria()
            
            if not criteria:
                self.status_var.set("Belum ada kriteria")
                return
            
            # Populate table
            for criterion in criteria:
                percentage = criterion['bobot'] * 100
                self.criteria_tree.insert('', 'end', values=(
                    criterion['id'],
                    criterion['kode'],
                    criterion['nama'],
                    f"{criterion['bobot']:.3f}",
                    f"{percentage:.1f}%",
                    criterion['jenis'].title(),
                    criterion.get('min_value', 1.0),
                    criterion.get('max_value', 15.0)
                ))
            
            self.status_var.set(f"Menampilkan {len(criteria)} kriteria")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat kriteria: {str(e)}")
            self.status_var.set("Error memuat data")
    
    def on_criteria_select(self, event):
        """Handle criteria selection"""
        selection = self.criteria_tree.selection()
        if selection:
            self.load_criteria_to_form(selection[0])
    
    def load_criteria_to_form(self, item):
        """Load selected criteria to form"""
        try:
            values = self.criteria_tree.item(item, 'values')
            criteria_id = int(values[0])
            
            # Get full criteria data
            criteria = self.db_manager.get_all_criteria()
            criterion = next((c for c in criteria if c['id'] == criteria_id), None)
            
            if criterion:
                self.current_criteria_id = criteria_id
                self.kode_var.set(criterion['kode'])
                self.nama_var.set(criterion['nama'])
                self.bobot_var.set(criterion['bobot'])
                self.jenis_var.set(criterion['jenis'])
                self.min_value_var.set(criterion.get('min_value', 1.0))
                self.max_value_var.set(criterion.get('max_value', 15.0))
                
                self.deskripsi_text.delete(1.0, tk.END)
                self.deskripsi_text.insert(1.0, criterion.get('deskripsi', ''))
                
                self.save_btn.configure(text="💾 Update")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat data kriteria: {str(e)}")
    
    def clear_form(self):
        """Clear form fields"""
        self.current_criteria_id = None
        self.kode_var.set('')
        self.nama_var.set('')
        self.bobot_var.set(0.0)
        self.jenis_var.set('benefit')
        self.min_value_var.set(1.0)
        self.max_value_var.set(15.0)
        self.deskripsi_text.delete(1.0, tk.END)
        self.save_btn.configure(text="💾 Simpan")
    
    def validate_form(self) -> bool:
        """Validate form input with comprehensive checks"""
        try:
            # Import validation utility
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils'))
            from error_handler import validate_input

            # Validate kode kriteria
            kode = self.kode_var.get().strip()
            validate_input(kode, 'Kode Kriteria', str)
            if len(kode) < 2:
                raise ValueError("Kode kriteria minimal 2 karakter!")
            if not kode.replace('_', '').replace('-', '').isalnum():
                raise ValueError("Kode kriteria hanya boleh mengandung huruf, angka, underscore, dan dash!")

            # Validate nama kriteria
            nama = self.nama_var.get().strip()
            validate_input(nama, 'Nama Kriteria', str)
            if len(nama) < 3:
                raise ValueError("Nama kriteria minimal 3 karakter!")

            # Validate bobot
            bobot = self.bobot_var.get()
            validate_input(bobot, 'Bobot', float, 0.0, 1.0)

            # Validate jenis kriteria
            jenis = self.jenis_var.get()
            if jenis not in ['benefit', 'cost']:
                raise ValueError("Jenis kriteria harus 'benefit' atau 'cost'!")

            # Validate min/max values
            min_val = self.min_value_var.get()
            max_val = self.max_value_var.get()
            validate_input(min_val, 'Nilai Minimum', float, 0.0)
            validate_input(max_val, 'Nilai Maksimum', float, min_val + 0.1)

            if min_val >= max_val:
                raise ValueError("Nilai minimum harus lebih kecil dari nilai maksimum!")

            return True

        except ValueError as e:
            messagebox.showerror("❌ Data Tidak Valid", str(e))
            return False
        except Exception as e:
            messagebox.showerror("❌ Error Validasi", f"Terjadi kesalahan validasi: {str(e)}")
            return False

    def save_criteria(self):
        """Save criteria"""
        # Check permissions
        if hasattr(self, 'current_criteria_id') and self.current_criteria_id:
            if not self.db_manager.has_permission('edit_criteria'):
                messagebox.showerror("Access Denied", "Anda tidak memiliki permission untuk edit kriteria!")
                return
        else:
            if not self.db_manager.has_permission('add_criteria'):
                messagebox.showerror("Access Denied", "Anda tidak memiliki permission untuk tambah kriteria!")
                return

        if not self.validate_form():
            return

        try:
            criteria_data = {
                'kode': self.kode_var.get().strip(),
                'nama': self.nama_var.get().strip(),
                'bobot': self.bobot_var.get(),
                'jenis': self.jenis_var.get(),
                'deskripsi': self.deskripsi_text.get(1.0, tk.END).strip(),
                'min_value': self.min_value_var.get(),
                'max_value': self.max_value_var.get()
            }

            if hasattr(self, 'current_criteria_id') and self.current_criteria_id:
                # Update existing criteria
                success = self.db_manager.update_criteria(self.current_criteria_id, criteria_data)
                action = "diupdate"
            else:
                # Add new criteria
                success = self.db_manager.add_criteria(criteria_data)
                action = "ditambahkan"

            if success:
                messagebox.showinfo("✅ Berhasil", f"Kriteria berhasil {action}!")
                self.clear_form()
                self.load_criteria()
            else:
                messagebox.showerror("❌ Gagal", f"Gagal {action.replace('di', 'men')} kriteria!")

        except Exception as e:
            messagebox.showerror("Error", f"Error menyimpan kriteria: {str(e)}")

    def add_criteria(self):
        """Add new criteria"""
        self.clear_form()
        self.kode_entry.focus()

    def edit_criteria(self):
        """Edit selected criteria"""
        selection = self.criteria_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih kriteria yang akan diedit!")
            return

        self.load_criteria_to_form(selection[0])

    def delete_criteria(self):
        """Delete selected criteria"""
        if not self.db_manager.has_permission('delete_criteria'):
            messagebox.showerror("Access Denied", "Anda tidak memiliki permission untuk hapus kriteria!")
            return

        selection = self.criteria_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih kriteria yang akan dihapus!")
            return

        values = self.criteria_tree.item(selection[0], 'values')
        criteria_name = values[2]

        if messagebox.askyesno("Konfirmasi",
                              f"Yakin ingin menghapus kriteria '{criteria_name}'?\n\n"
                              f"Tindakan ini tidak dapat dibatalkan!"):
            try:
                criteria_id = int(values[0])
                success = self.db_manager.delete_criteria(criteria_id)

                if success:
                    messagebox.showinfo("✅ Berhasil", "Kriteria berhasil dihapus!")
                    self.clear_form()
                    self.load_criteria()
                else:
                    messagebox.showerror("❌ Gagal", "Gagal menghapus kriteria!")

            except Exception as e:
                messagebox.showerror("Error", f"Error menghapus kriteria: {str(e)}")

    def auto_balance_weights(self):
        """Auto balance all criteria weights"""
        if messagebox.askyesno("Konfirmasi",
                              "Auto balance akan membagi rata bobot semua kriteria.\n"
                              "Yakin ingin melanjutkan?"):
            try:
                criteria = self.db_manager.get_all_criteria()
                if not criteria:
                    messagebox.showwarning("Peringatan", "Belum ada kriteria!")
                    return

                # Calculate equal weight for each criteria
                equal_weight = 1.0 / len(criteria)

                # Update all criteria weights
                for criterion in criteria:
                    criterion['bobot'] = equal_weight
                    self.db_manager.update_criteria(criterion['id'], criterion)

                messagebox.showinfo("✅ Berhasil",
                                  f"Bobot berhasil dibagi rata!\n"
                                  f"Setiap kriteria: {equal_weight:.3f} ({equal_weight*100:.1f}%)")

                self.clear_form()
                self.load_criteria()

            except Exception as e:
                messagebox.showerror("Error", f"Error auto balance: {str(e)}")
