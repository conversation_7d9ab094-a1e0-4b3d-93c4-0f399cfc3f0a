"""
History Frame untuk SPK Karyawan TOPSIS
Menampilkan riwayat perhitungan TOPSIS
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import json

class HistoryFrame:
    def __init__(self, parent, db_manager):
        """Initialize history frame"""
        self.parent = parent
        self.db_manager = db_manager
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        self.create_widgets()
        self.load_history()
    
    def create_widgets(self):
        """Create history widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="Riwayat Perhitungan TOPSIS", 
                 style='Title.TLabel').pack(side='left')
        
        ttk.Button(title_frame, text="🔄 Refresh", 
                  command=self.load_history, style='Nav.TButton').pack(side='right', padx=(10, 0))
        
        ttk.Button(title_frame, text="🗑️ Bersihkan Riwayat", 
                  command=self.clear_history, style='Nav.TButton').pack(side='right')
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=2)
        main_container.grid_columnconfigure(1, weight=1)
        
        # Left side - History table
        self.create_history_table(main_container)
        
        # Right side - Details
        self.create_details_panel(main_container)
    
    def create_history_table(self, parent):
        """Create history table"""
        table_frame = ttk.LabelFrame(parent, text="Daftar Riwayat", padding=10)
        table_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))
        
        # Configure grid
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # Treeview
        columns = ('ID', 'Nama Sesi', 'Total Karyawan', 'Tanggal')
        self.history_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        column_widths = {'ID': 50, 'Nama Sesi': 200, 'Total Karyawan': 120, 'Tanggal': 150}
        
        for col in columns:
            self.history_tree.heading(col, text=col)
            self.history_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.history_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.history_tree.xview)
        self.history_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.history_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Bind events
        self.history_tree.bind('<<TreeviewSelect>>', self.on_selection_change)
        self.history_tree.bind('<Double-1>', self.on_item_double_click)
        
        # Status
        self.status_var = tk.StringVar()
        status_label = ttk.Label(table_frame, textvariable=self.status_var, 
                               style='Info.TLabel')
        status_label.grid(row=2, column=0, columnspan=2, pady=5)
    
    def create_details_panel(self, parent):
        """Create details panel"""
        details_frame = ttk.LabelFrame(parent, text="Detail Perhitungan", padding=15)
        details_frame.grid(row=0, column=1, sticky='nsew', padx=(10, 0))
        
        # Configure grid
        details_frame.grid_rowconfigure(0, weight=1)
        details_frame.grid_columnconfigure(0, weight=1)
        
        # Details text
        self.details_text = tk.Text(details_frame, wrap='word', font=('Consolas', 10), 
                                   state='disabled', height=20)
        
        details_scrollbar = ttk.Scrollbar(details_frame, orient='vertical', command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scrollbar.set)
        
        # Grid layout
        self.details_text.grid(row=0, column=0, sticky='nsew')
        details_scrollbar.grid(row=0, column=1, sticky='ns')
        
        # Action buttons
        action_frame = ttk.Frame(details_frame)
        action_frame.grid(row=1, column=0, columnspan=2, pady=10)
        
        ttk.Button(action_frame, text="📊 Lihat Bobot", 
                  command=self.view_weights, style='Action.TButton').pack(side='left', padx=5)
        
        ttk.Button(action_frame, text="📋 Copy Detail", 
                  command=self.copy_details, style='Nav.TButton').pack(side='left', padx=5)
        
        # Summary info
        summary_frame = ttk.LabelFrame(details_frame, text="Ringkasan", padding=10)
        summary_frame.grid(row=2, column=0, columnspan=2, sticky='ew', pady=(10, 0))
        
        self.summary_var = tk.StringVar()
        summary_label = ttk.Label(summary_frame, textvariable=self.summary_var, 
                                style='Info.TLabel', justify='left')
        summary_label.pack(anchor='w')
    
    def load_history(self):
        """Load calculation history"""
        try:
            # Clear existing items
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            
            # Load history from database
            history = self.db_manager.get_calculation_history()
            
            if not history:
                self.status_var.set("Belum ada riwayat perhitungan")
                self.clear_details()
                return
            
            # Populate table
            for record in history:
                # Format tanggal
                tanggal = record.get('created_at', '')
                if tanggal:
                    try:
                        dt = datetime.fromisoformat(tanggal.replace('Z', '+00:00'))
                        tanggal = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass
                
                self.history_tree.insert('', 'end', values=(
                    record.get('id', ''),
                    record.get('session_name', ''),
                    record.get('total_employees', ''),
                    tanggal
                ))
            
            self.status_var.set(f"Menampilkan {len(history)} riwayat perhitungan")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat riwayat: {str(e)}")
            self.status_var.set("Error memuat data")
    
    def on_selection_change(self, event):
        """Handle selection change"""
        selection = self.history_tree.selection()
        if selection:
            self.show_details(selection[0])
        else:
            self.clear_details()
    
    def on_item_double_click(self, event):
        """Handle double click"""
        selection = self.history_tree.selection()
        if selection:
            self.view_weights()
    
    def show_details(self, item):
        """Show details for selected history item"""
        try:
            values = self.history_tree.item(item, 'values')
            history_id = int(values[0])
            
            # Get full history record
            history = self.db_manager.get_calculation_history()
            record = next((h for h in history if h['id'] == history_id), None)
            
            if not record:
                self.clear_details()
                return
            
            # Parse criteria weights
            try:
                weights = json.loads(record.get('criteria_weights', '{}'))
            except:
                weights = {}
            
            # Build details text
            details_text = f"""DETAIL PERHITUNGAN TOPSIS
{'='*40}

ID Sesi: {record.get('id', '')}
Nama Sesi: {record.get('session_name', '')}
Tanggal: {values[3]}
Total Karyawan: {record.get('total_employees', '')}

BOBOT KRITERIA YANG DIGUNAKAN:
{'='*40}
"""
            
            # Add criteria weights
            criteria_names = {
                'C1': 'Kemampuan Teknik',
                'C2': 'Kualitas',
                'C3': 'Presisi',
                'C4': 'Pelanggaran',
                'C5': 'Absensi'
            }
            
            total_weight = 0
            for kode in ['C1', 'C2', 'C3', 'C4', 'C5']:
                weight = weights.get(kode, 0)
                total_weight += weight
                nama = criteria_names.get(kode, kode)
                details_text += f"{kode} - {nama:<20}: {weight:.3f} ({weight*100:.1f}%)\n"
            
            details_text += f"\nTotal Bobot: {total_weight:.3f} ({total_weight*100:.1f}%)\n"
            
            if abs(total_weight - 1.0) < 0.001:
                details_text += "✓ Bobot valid (100%)\n"
            else:
                details_text += "⚠ Bobot tidak valid!\n"
            
            # Add calculation info
            details_text += f"""
INFORMASI PERHITUNGAN:
{'='*40}
• Metode: TOPSIS (Technique for Order Preference by Similarity to Ideal Solution)
• Normalisasi: Euclidean Distance
• Kriteria Benefit: C1, C2, C3, C5
• Kriteria Cost: C4
• Status Threshold:
  - Sangat Direkomendasikan: ≥ 0.7
  - Direkomendasikan: 0.5 - 0.7
  - Perlu Pertimbangan: 0.3 - 0.5
  - Tidak Direkomendasikan: < 0.3

CATATAN:
Hasil perhitungan ini menggunakan bobot kriteria yang tercantum di atas.
Perubahan bobot akan menghasilkan ranking yang berbeda.
            """
            
            # Update details display
            self.details_text.configure(state='normal')
            self.details_text.delete(1.0, tk.END)
            self.details_text.insert(tk.END, details_text)
            self.details_text.configure(state='disabled')
            
            # Update summary
            summary_text = f"Sesi: {record.get('session_name', '')}\nKaryawan: {record.get('total_employees', '')} | Bobot Valid: {'Ya' if abs(total_weight - 1.0) < 0.001 else 'Tidak'}"
            self.summary_var.set(summary_text)
            
        except Exception as e:
            print(f"Error showing details: {e}")
            self.clear_details()
    
    def clear_details(self):
        """Clear details display"""
        self.details_text.configure(state='normal')
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(tk.END, "Pilih riwayat perhitungan untuk melihat detail")
        self.details_text.configure(state='disabled')
        
        self.summary_var.set("Tidak ada data dipilih")
    
    def view_weights(self):
        """View criteria weights in detail"""
        selection = self.history_tree.selection()
        if not selection:
            messagebox.showwarning("Peringatan", "Pilih riwayat perhitungan terlebih dahulu!")
            return
        
        try:
            values = self.history_tree.item(selection[0], 'values')
            history_id = int(values[0])
            
            # Get history record
            history = self.db_manager.get_calculation_history()
            record = next((h for h in history if h['id'] == history_id), None)
            
            if not record:
                messagebox.showerror("Error", "Data riwayat tidak ditemukan!")
                return
            
            # Parse weights
            try:
                weights = json.loads(record.get('criteria_weights', '{}'))
            except:
                weights = {}
            
            # Create weights display window
            self.show_weights_window(record, weights)
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal menampilkan bobot: {str(e)}")
    
    def show_weights_window(self, record, weights):
        """Show weights in separate window"""
        # Create new window
        weights_window = tk.Toplevel(self.frame)
        weights_window.title(f"Bobot Kriteria - {record.get('session_name', '')}")
        weights_window.geometry("500x400")
        weights_window.resizable(False, False)
        
        # Center window
        weights_window.transient(self.frame.winfo_toplevel())
        weights_window.grab_set()
        
        # Title
        title_label = ttk.Label(weights_window, text="Bobot Kriteria TOPSIS", 
                               style='Title.TLabel')
        title_label.pack(pady=20)
        
        # Weights table
        weights_frame = ttk.LabelFrame(weights_window, text="Detail Bobot", padding=20)
        weights_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # Table headers
        headers = ['Kode', 'Nama Kriteria', 'Bobot', 'Persentase', 'Jenis']
        for i, header in enumerate(headers):
            ttk.Label(weights_frame, text=header, style='Header.TLabel').grid(
                row=0, column=i, padx=10, pady=5, sticky='w')
        
        # Separator
        for i in range(len(headers)):
            ttk.Separator(weights_frame, orient='horizontal').grid(
                row=1, column=i, sticky='ew', padx=5)
        
        # Criteria data
        criteria_data = [
            ('C1', 'Kemampuan Teknik', 'benefit'),
            ('C2', 'Kualitas', 'benefit'),
            ('C3', 'Presisi', 'benefit'),
            ('C4', 'Pelanggaran', 'cost'),
            ('C5', 'Absensi', 'benefit')
        ]
        
        total_weight = 0
        for i, (kode, nama, jenis) in enumerate(criteria_data, 2):
            weight = weights.get(kode, 0)
            total_weight += weight
            percentage = weight * 100
            
            ttk.Label(weights_frame, text=kode).grid(row=i, column=0, padx=10, pady=2, sticky='w')
            ttk.Label(weights_frame, text=nama).grid(row=i, column=1, padx=10, pady=2, sticky='w')
            ttk.Label(weights_frame, text=f"{weight:.3f}").grid(row=i, column=2, padx=10, pady=2, sticky='w')
            ttk.Label(weights_frame, text=f"{percentage:.1f}%").grid(row=i, column=3, padx=10, pady=2, sticky='w')
            ttk.Label(weights_frame, text=jenis.title()).grid(row=i, column=4, padx=10, pady=2, sticky='w')
        
        # Total row
        ttk.Separator(weights_frame, orient='horizontal').grid(
            row=len(criteria_data)+2, column=0, columnspan=len(headers), sticky='ew', padx=5, pady=5)
        
        ttk.Label(weights_frame, text="TOTAL", style='Header.TLabel').grid(
            row=len(criteria_data)+3, column=1, padx=10, pady=5, sticky='w')
        ttk.Label(weights_frame, text=f"{total_weight:.3f}", style='Header.TLabel').grid(
            row=len(criteria_data)+3, column=2, padx=10, pady=5, sticky='w')
        ttk.Label(weights_frame, text=f"{total_weight*100:.1f}%", style='Header.TLabel').grid(
            row=len(criteria_data)+3, column=3, padx=10, pady=5, sticky='w')
        
        # Validation status
        if abs(total_weight - 1.0) < 0.001:
            status_text = "✓ Bobot Valid (100%)"
            status_color = 'green'
        else:
            status_text = "⚠ Bobot Tidak Valid"
            status_color = 'red'
        
        status_label = ttk.Label(weights_frame, text=status_text, style='Header.TLabel')
        status_label.grid(row=len(criteria_data)+3, column=4, padx=10, pady=5, sticky='w')
        
        # Close button
        ttk.Button(weights_window, text="Tutup", 
                  command=weights_window.destroy, style='Action.TButton').pack(pady=10)
    
    def copy_details(self):
        """Copy details to clipboard"""
        try:
            details = self.details_text.get(1.0, tk.END)
            self.frame.clipboard_clear()
            self.frame.clipboard_append(details)
            messagebox.showinfo("Sukses", "Detail berhasil disalin ke clipboard!")
        except Exception as e:
            messagebox.showerror("Error", f"Gagal menyalin detail: {str(e)}")
    
    def clear_history(self):
        """Clear calculation history"""
        if not messagebox.askyesno("Konfirmasi", 
                                 "Yakin ingin menghapus semua riwayat perhitungan?\n\nTindakan ini tidak dapat dibatalkan!"):
            return
        
        try:
            # This would require a new method in database manager
            # For now, show info message
            messagebox.showinfo("Info", "Fitur hapus riwayat akan tersedia dalam versi mendatang")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal menghapus riwayat: {str(e)}")
