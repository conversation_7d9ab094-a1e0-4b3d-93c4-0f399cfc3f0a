"""
Enhanced TOPSIS Calculator
Implementasi algoritma TOPSIS untuk sistem dinamis
"""

import numpy as np
import pandas as pd
from datetime import datetime
import json
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class EnhancedTOPSISCalculator:
    def __init__(self, db_manager):
        """Initialize enhanced TOPSIS calculator"""
        self.db_manager = db_manager
        self.criteria = []
        self.alternatives = []
        self.decision_matrix = None
        self.normalized_matrix = None
        self.weighted_matrix = None
        self.ideal_positive = None
        self.ideal_negative = None
        self.distances_positive = None
        self.distances_negative = None
        self.scores = None
        self.rankings = None
    
    def load_data(self):
        """Load criteria and alternatives data"""
        try:
            # Load criteria
            self.criteria = self.db_manager.get_all_criteria()
            if not self.criteria:
                raise ValueError("Tidak ada kriteria yang didefinisikan")
            
            # Load alternatives with criteria values
            alternatives_data = self.db_manager.get_all_alternatives_with_values()
            if not alternatives_data:
                raise ValueError("Tidak ada alternatif yang didefinisikan")
            
            self.alternatives = alternatives_data
            
            # Validate data completeness
            self.validate_data()
            
            return True
            
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def validate_data(self):
        """Validate data completeness and consistency"""
        # Check if all alternatives have values for all criteria
        criteria_codes = [c['kode'] for c in self.criteria]

        for alternative in self.alternatives:
            alt_criteria_codes = list(alternative['criteria_values'].keys())
            missing_criteria = set(criteria_codes) - set(alt_criteria_codes)

            if missing_criteria:
                missing_names = [c['nama'] for c in self.criteria if c['kode'] in missing_criteria]
                raise ValueError(f"Alternatif '{alternative['nama']}' belum memiliki nilai untuk kriteria: {', '.join(missing_names)}")

        # Check total weight
        total_weight = sum(c['bobot'] for c in self.criteria)
        if abs(total_weight - 1.0) > 0.001:
            raise ValueError(f"Total bobot kriteria harus 1.0 (100%), saat ini: {total_weight:.3f}")
    
    def build_decision_matrix(self):
        """Build decision matrix from alternatives and criteria"""
        try:
            # Create matrix: rows = alternatives, columns = criteria
            n_alternatives = len(self.alternatives)
            n_criteria = len(self.criteria)
            
            self.decision_matrix = np.zeros((n_alternatives, n_criteria))
            
            # Fill matrix
            for i, alternative in enumerate(self.alternatives):
                for j, criterion in enumerate(self.criteria):
                    criteria_code = criterion['kode']
                    value = alternative['criteria_values'].get(criteria_code, 0.0)
                    self.decision_matrix[i, j] = value
            
            print(f"Decision matrix built: {n_alternatives} x {n_criteria}")
            return True
            
        except Exception as e:
            print(f"Error building decision matrix: {e}")
            return False
    
    def normalize_matrix(self):
        """Normalize decision matrix using vector normalization"""
        try:
            # Calculate column sums of squares
            col_sums_sq = np.sum(self.decision_matrix ** 2, axis=0)
            
            # Calculate square roots
            col_sqrt = np.sqrt(col_sums_sq)
            
            # Avoid division by zero
            col_sqrt[col_sqrt == 0] = 1
            
            # Normalize
            self.normalized_matrix = self.decision_matrix / col_sqrt
            
            print("Matrix normalized successfully")
            return True
            
        except Exception as e:
            print(f"Error normalizing matrix: {e}")
            return False
    
    def apply_weights(self):
        """Apply criteria weights to normalized matrix"""
        try:
            # Get weights vector
            weights = np.array([c['bobot'] for c in self.criteria])
            
            # Apply weights
            self.weighted_matrix = self.normalized_matrix * weights
            
            print("Weights applied successfully")
            return True
            
        except Exception as e:
            print(f"Error applying weights: {e}")
            return False
    
    def determine_ideal_solutions(self):
        """Determine positive and negative ideal solutions"""
        try:
            n_criteria = len(self.criteria)
            self.ideal_positive = np.zeros(n_criteria)
            self.ideal_negative = np.zeros(n_criteria)
            
            for j, criterion in enumerate(self.criteria):
                column = self.weighted_matrix[:, j]
                
                if criterion['jenis'] == 'benefit':
                    # For benefit criteria: max is positive ideal, min is negative ideal
                    self.ideal_positive[j] = np.max(column)
                    self.ideal_negative[j] = np.min(column)
                else:  # cost
                    # For cost criteria: min is positive ideal, max is negative ideal
                    self.ideal_positive[j] = np.min(column)
                    self.ideal_negative[j] = np.max(column)
            
            print("Ideal solutions determined")
            return True
            
        except Exception as e:
            print(f"Error determining ideal solutions: {e}")
            return False
    
    def calculate_distances(self):
        """Calculate distances to ideal solutions"""
        try:
            n_alternatives = len(self.alternatives)
            self.distances_positive = np.zeros(n_alternatives)
            self.distances_negative = np.zeros(n_alternatives)
            
            for i in range(n_alternatives):
                # Distance to positive ideal
                diff_pos = self.weighted_matrix[i, :] - self.ideal_positive
                self.distances_positive[i] = np.sqrt(np.sum(diff_pos ** 2))
                
                # Distance to negative ideal
                diff_neg = self.weighted_matrix[i, :] - self.ideal_negative
                self.distances_negative[i] = np.sqrt(np.sum(diff_neg ** 2))
            
            print("Distances calculated")
            return True
            
        except Exception as e:
            print(f"Error calculating distances: {e}")
            return False
    
    def calculate_scores(self):
        """Calculate TOPSIS scores and rankings"""
        try:
            # Calculate preference scores
            total_distances = self.distances_positive + self.distances_negative
            
            # Avoid division by zero
            total_distances[total_distances == 0] = 1e-10
            
            self.scores = self.distances_negative / total_distances
            
            # Calculate rankings (1 = best)
            self.rankings = np.argsort(-self.scores) + 1  # Descending order, 1-based
            
            print("Scores and rankings calculated")
            return True
            
        except Exception as e:
            print(f"Error calculating scores: {e}")
            return False
    
    def save_results(self):
        """Save results to database"""
        try:
            # Generate session name
            session_name = f"TOPSIS_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Prepare results data
            results = []
            for i, alternative in enumerate(self.alternatives):
                # Find ranking for this alternative
                ranking = np.where(self.rankings == i + 1)[0][0] + 1
                
                result = {
                    'alternative_id': alternative['id'],
                    'score': float(self.scores[i]),
                    'ranking': int(ranking),
                    'distance_positive': float(self.distances_positive[i]),
                    'distance_negative': float(self.distances_negative[i])
                }
                results.append(result)
            
            # Save to database
            success = self.db_manager.save_topsis_results(session_name, results)
            
            if success:
                # Save calculation history
                criteria_weights = {c['kode']: c['bobot'] for c in self.criteria}
                
                history_data = {
                    'session_name': session_name,
                    'total_alternatives': len(self.alternatives),
                    'total_criteria': len(self.criteria),
                    'criteria_weights': json.dumps(criteria_weights)
                }
                
                # This would require adding save_calculation_history method to db_manager
                print(f"Results saved with session: {session_name}")
                return results
            else:
                raise Exception("Failed to save results to database")
                
        except Exception as e:
            print(f"Error saving results: {e}")
            return None
    
    def calculate(self):
        """Main calculation method"""
        try:
            print("Starting TOPSIS calculation...")
            
            # Step 1: Load data
            if not self.load_data():
                return None
            
            # Step 2: Build decision matrix
            if not self.build_decision_matrix():
                return None
            
            # Step 3: Normalize matrix
            if not self.normalize_matrix():
                return None
            
            # Step 4: Apply weights
            if not self.apply_weights():
                return None
            
            # Step 5: Determine ideal solutions
            if not self.determine_ideal_solutions():
                return None
            
            # Step 6: Calculate distances
            if not self.calculate_distances():
                return None
            
            # Step 7: Calculate scores and rankings
            if not self.calculate_scores():
                return None
            
            # Step 8: Save results
            results = self.save_results()
            
            if results:
                print(f"TOPSIS calculation completed successfully!")
                print(f"Total alternatives processed: {len(results)}")
                return results
            else:
                return None
                
        except Exception as e:
            print(f"Error in TOPSIS calculation: {e}")
            return None
    
    def get_calculation_summary(self):
        """Get summary of calculation results"""
        if self.scores is None:
            return None
        
        summary = {
            'total_alternatives': len(self.alternatives),
            'total_criteria': len(self.criteria),
            'max_score': float(np.max(self.scores)),
            'min_score': float(np.min(self.scores)),
            'avg_score': float(np.mean(self.scores)),
            'std_score': float(np.std(self.scores))
        }
        
        return summary
