"""
Build Executable Script
Membuat file executable (.exe) untuk aplikasi SPK TOPSIS
Author: <PERSON> Wibowo
NIM: ************
Kelas: 06TPLP003
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        print("✅ PyInstaller is available")
        return True
    except ImportError:
        print("❌ PyInstaller not found")
        print("📦 Installing PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return False

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main_modern.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ui', 'ui'),
        ('database', 'database'),
        ('core', 'core'),
        ('utils', 'utils'),
        ('modern_ui', 'modern_ui'),
        ('requirements.txt', '.'),
        ('README.md', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'customtkinter',
        'numpy',
        'pandas',
        'matplotlib',
        'matplotlib.backends.backend_tkagg',
        'openpyxl',
        'sqlite3',
        'hashlib',
        'datetime',
        'json',
        'os',
        'sys',
        'threading',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SPK_TOPSIS_Enhanced_v2.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open('spk_topsis.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Spec file created: spk_topsis.spec")

def build_executable():
    """Build executable using PyInstaller"""
    print("🔨 Building executable...")
    
    try:
        # Build using spec file
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "spk_topsis.spec"]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Executable built successfully!")
            
            # Check if executable exists
            exe_path = Path("dist/SPK_TOPSIS_Enhanced_v2.0.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 Executable location: {exe_path.absolute()}")
                print(f"📏 File size: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Executable not found in expected location")
                return False
        else:
            print("❌ Build failed!")
            print("Error output:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error during build: {e}")
        return False

def create_portable_package():
    """Create portable package with all necessary files"""
    print("📦 Creating portable package...")
    
    package_dir = Path("SPK_TOPSIS_Enhanced_v2.0_Portable")
    
    # Create package directory
    if package_dir.exists():
        shutil.rmtree(package_dir)
    package_dir.mkdir()
    
    # Copy executable
    exe_source = Path("dist/SPK_TOPSIS_Enhanced_v2.0.exe")
    if exe_source.exists():
        shutil.copy2(exe_source, package_dir / "SPK_TOPSIS_Enhanced_v2.0.exe")
        print("✅ Copied executable")
    
    # Copy database setup script
    if Path("setup_database_with_sample_data.py").exists():
        shutil.copy2("setup_database_with_sample_data.py", package_dir)
        print("✅ Copied database setup script")
    
    # Copy requirements
    if Path("requirements.txt").exists():
        shutil.copy2("requirements.txt", package_dir)
        print("✅ Copied requirements.txt")
    
    # Copy README
    if Path("README.md").exists():
        shutil.copy2("README.md", package_dir)
        print("✅ Copied README.md")
    
    # Create batch files for easy execution
    create_batch_files(package_dir)
    
    # Create user guide
    create_user_guide(package_dir)
    
    print(f"✅ Portable package created: {package_dir.absolute()}")
    return True

def create_batch_files(package_dir):
    """Create batch files for easy execution"""
    
    # Main application launcher
    main_bat = '''@echo off
title SPK TOPSIS Enhanced v2.0
echo.
echo ============================================================
echo 🏆 SPK TOPSIS Enhanced v2.0 - Desktop Application
echo ============================================================
echo Author: Muhammad Bayu Prasetyo Wibowo
echo NIM: ************
echo Kelas: 06TPLP003
echo ============================================================
echo.
echo 🚀 Starting SPK TOPSIS Application...
echo.

SPK_TOPSIS_Enhanced_v2.0.exe

if %errorlevel% neq 0 (
    echo.
    echo ❌ Application failed to start!
    echo 💡 Make sure all dependencies are installed.
    echo.
    pause
)
'''
    
    with open(package_dir / "Run_SPK_TOPSIS.bat", 'w', encoding='utf-8') as f:
        f.write(main_bat)
    
    # Database setup launcher
    setup_bat = '''@echo off
title SPK TOPSIS - Database Setup
echo.
echo ============================================================
echo 🔧 SPK TOPSIS Enhanced v2.0 - Database Setup
echo ============================================================
echo.
echo This will setup the database with sample data for testing.
echo.
pause

python setup_database_with_sample_data.py

echo.
echo Setup completed!
pause
'''
    
    with open(package_dir / "Setup_Database.bat", 'w', encoding='utf-8') as f:
        f.write(setup_bat)
    
    print("✅ Created batch files")

def create_user_guide(package_dir):
    """Create user guide"""
    guide_content = '''# SPK TOPSIS Enhanced v2.0 - User Guide

## 📋 Panduan Penggunaan

### 🚀 Cara Menjalankan Aplikasi

1. **Jalankan Aplikasi Utama:**
   - Double-click `Run_SPK_TOPSIS.bat`
   - Atau langsung jalankan `SPK_TOPSIS_Enhanced_v2.0.exe`

2. **Setup Database (Opsional):**
   - Double-click `Setup_Database.bat` untuk mengisi database dengan data sample
   - Atau jalankan `python setup_database_with_sample_data.py`

### 🔐 Login Credentials

**Administrator (Full Access):**
- Username: `admin`
- Password: `admin123`

**Data Operator (Input Only):**
- Username: `operator`  
- Password: `operator123`

### 📊 Fitur Utama

1. **Dashboard** - Overview sistem dan statistik
2. **Kelola Kriteria** - Tambah, edit, hapus kriteria penilaian
3. **Kelola Alternatif** - Tambah, edit, hapus data karyawan
4. **Hitung TOPSIS** - Jalankan perhitungan ranking
5. **Lihat Hasil** - Tampilkan hasil ranking dan analisis
6. **Export Data** - Export hasil ke Excel/CSV

### 🛠️ Troubleshooting

**Jika aplikasi tidak bisa jalan:**
1. Pastikan Windows Defender tidak memblokir file .exe
2. Install Microsoft Visual C++ Redistributable
3. Jalankan sebagai Administrator

**Jika ada error database:**
1. Jalankan `Setup_Database.bat`
2. Pastikan file database tidak sedang dibuka aplikasi lain

### 📞 Support

Jika ada masalah, hubungi:
- **Author:** Muhammad Bayu Prasetyo Wibowo
- **NIM:** ************
- **Kelas:** 06TPLP003

---
© 2024 SPK TOPSIS Enhanced v2.0
'''
    
    with open(package_dir / "USER_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ Created user guide")

def main():
    """Main build process"""
    print("=" * 60)
    print("🏗️ SPK TOPSIS Enhanced v2.0 - Build Executable")
    print("=" * 60)
    print("Author: Muhammad Bayu Prasetyo Wibowo")
    print("NIM: ************")
    print("Kelas: 06TPLP003")
    print("=" * 60)
    print()
    
    # Check PyInstaller
    if not check_pyinstaller():
        print("❌ Cannot proceed without PyInstaller")
        return False
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if not build_executable():
        print("❌ Build failed")
        return False
    
    # Create portable package
    if not create_portable_package():
        print("❌ Package creation failed")
        return False
    
    print("\n🎉 Build completed successfully!")
    print("\n📁 Files created:")
    print("   - dist/SPK_TOPSIS_Enhanced_v2.0.exe")
    print("   - SPK_TOPSIS_Enhanced_v2.0_Portable/ (portable package)")
    print("\n🚀 Ready for distribution!")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ Build process failed!")
    except KeyboardInterrupt:
        print("\n⚠️ Build cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nPress Enter to exit...")
