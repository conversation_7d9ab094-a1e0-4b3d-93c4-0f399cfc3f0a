@echo off
title SPK TOPSIS - Cleanup Alternatives
color 0E

echo.
echo ============================================================
echo 🧹 SPK TOPSIS Enhanced v2.0 - Cleanup Alternatives
echo ============================================================
echo Author: <PERSON> Wibowo
echo NIM: 211011450583
echo Kelas: 06TPLP003
echo ============================================================
echo.
echo 🎯 TUJUAN:
echo    Hapus semua alternatif/karyawan KECUALI:
echo    ✅ Rahmat
echo    ✅ Jaya  
echo    ✅ Bunga
echo.
echo 🗑️ YANG AKAN DIHAPUS:
echo    ❌ Semua karyawan lain (<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, dll.)
echo    ❌ Evaluasi untuk karyawan yang dihapus
echo    ❌ Hasil TOPSIS untuk karyawan yang dihapus
echo.
echo 💾 YANG TETAP DISIMPAN:
echo    ✅ Struktur database utuh
echo    ✅ Semua kriteria (C1-C5)
echo    ✅ 3 karyawan: Rahmat, Jaya, Bunga
echo    ✅ Evaluasi untuk 3 karyawan tersebut
echo.
echo ⚠️  PENTING: Tutup aplikasi desktop terlebih dahulu!
echo.

set /p confirm="Lanjutkan cleanup alternatives? (Y/N): "
if /i "%confirm%" NEQ "Y" (
    echo Cleanup dibatalkan.
    pause
    exit /b
)

echo.
echo 🧹 Memulai cleanup alternatives...

REM Backup database terlebih dahulu
if exist "spk_karyawan_enhanced.db" (
    copy "spk_karyawan_enhanced.db" "spk_karyawan_enhanced_backup_before_cleanup.db" >nul 2>&1
    echo 📁 Backup database dibuat: spk_karyawan_enhanced_backup_before_cleanup.db
) else (
    echo ❌ Database tidak ditemukan!
    echo 💡 Pastikan file spk_karyawan_enhanced.db ada di folder ini
    pause
    exit /b
)

REM Cek apakah Python tersedia
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Python ditemukan, menggunakan Python script...
    
    REM Buat Python script untuk cleanup
    echo import sqlite3 > cleanup_temp.py
    echo import os >> cleanup_temp.py
    echo. >> cleanup_temp.py
    echo if os.path.exists('spk_karyawan_enhanced.db'): >> cleanup_temp.py
    echo     conn = sqlite3.connect('spk_karyawan_enhanced.db') >> cleanup_temp.py
    echo     with open('cleanup_alternatives.sql', 'r', encoding='utf-8') as f: >> cleanup_temp.py
    echo         sql_script = f.read() >> cleanup_temp.py
    echo     conn.executescript(sql_script) >> cleanup_temp.py
    echo     conn.close() >> cleanup_temp.py
    echo     print('✅ Cleanup berhasil!') >> cleanup_temp.py
    echo else: >> cleanup_temp.py
    echo     print('❌ Database tidak ditemukan!') >> cleanup_temp.py
    
    python cleanup_temp.py
    del cleanup_temp.py >nul 2>&1
    
    if %errorlevel% == 0 (
        echo ✅ Cleanup berhasil dengan Python!
        goto :success
    ) else (
        echo ❌ Error saat cleanup dengan Python
        goto :manual_sql
    )
) else (
    echo ⚠️ Python tidak ditemukan, mencoba SQLite...
    goto :manual_sql
)

:manual_sql
REM Cek apakah SQLite tersedia
sqlite3 -version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ SQLite ditemukan, menjalankan SQL script...
    sqlite3 spk_karyawan_enhanced.db < cleanup_alternatives.sql
    
    if %errorlevel% == 0 (
        echo ✅ Cleanup berhasil dengan SQLite!
        goto :success
    ) else (
        echo ❌ Error saat cleanup dengan SQLite
        goto :manual_instruction
    )
) else (
    echo ⚠️ SQLite tidak ditemukan
    goto :manual_instruction
)

:manual_instruction
echo.
echo 📝 INSTRUKSI MANUAL CLEANUP:
echo.
echo 1. 🚀 Buka aplikasi: SPK_TOPSIS_Enhanced_v2.0.exe
echo 2. 🔑 Login sebagai admin: admin / admin123
echo 3. 👥 Masuk ke menu "Kelola Alternatif"
echo 4. 🗑️ Hapus satu per satu karyawan KECUALI:
echo    - Rahmat
echo    - Jaya
echo    - Bunga
echo 5. ✅ Pastikan hanya 3 karyawan yang tersisa
echo.
goto :end

:success
echo.
echo ============================================================
echo ✅ CLEANUP ALTERNATIVES BERHASIL!
echo ============================================================
echo.
echo 🧹 YANG TELAH DIHAPUS:
echo    ❌ Semua karyawan selain Rahmat, Jaya, Bunga
echo    ❌ Evaluasi untuk karyawan yang dihapus
echo    ❌ Hasil TOPSIS lama
echo.
echo 👥 KARYAWAN YANG TERSISA (3 orang):
echo    1. Rahmat - Karyawan
echo    2. Jaya - Karyawan  
echo    3. Bunga - Karyawan
echo.
echo 📝 EVALUASI YANG TERSISA:
echo    ✅ Rahmat: [10, 9,  10, 2, 15]
echo    ✅ Jaya:   [14, 15, 12, 2, 13]
echo    ✅ Bunga:  [13, 12, 15, 1, 12]
echo.
echo 🎯 LANGKAH SELANJUTNYA:
echo.
echo 1. 🚀 Buka aplikasi: SPK_TOPSIS_Enhanced_v2.0.exe
echo 2. 🔑 Login sebagai admin: admin / admin123
echo 3. 👥 Cek "Kelola Alternatif" - seharusnya hanya 3 karyawan
echo 4. 📊 Cek "Input Evaluasi" - seharusnya ada 15 data (3x5)
echo 5. 🧮 Jalankan "Hitung TOPSIS" - ranking untuk 3 karyawan
echo.
echo 💾 BACKUP: spk_karyawan_enhanced_backup_before_cleanup.db
echo    (Jika perlu restore data lama)
echo.

:end
echo ============================================================
echo.
pause
