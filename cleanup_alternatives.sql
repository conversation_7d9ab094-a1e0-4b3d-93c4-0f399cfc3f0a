-- Cleanup Alternatives - <PERSON><PERSON>, Jaya, Bunga
-- <PERSON>pus alternatif lain tapi tetap simpan struktur database

-- Backup data yang akan dihapus (opsional untuk log)
.print "=== CLEANUP ALTERNATIVES ==="
.print ""
.print "ALTERNATIF SEBELUM CLEANUP:"
SELECT id, name, position FROM dynamic_alternatives ORDER BY id;

.print ""
.print "EVALUASI SEBELUM CLEANUP:"
SELECT COUNT(*) as total_evaluasi FROM dynamic_evaluations;

-- <PERSON>pus evaluasi untuk alternatif yang akan dihapus
-- Pertama, identifikasi ID alternatif yang akan dipertahankan
-- Asumsi: Rahmat, Jaya, Bunga adalah 3 alternatif pertama atau yang namanya sesuai

-- Hapus evaluasi untuk alternatif selain <PERSON>, Jaya, Bunga
DELETE FROM dynamic_evaluations 
WHERE alternative_id NOT IN (
    SELECT id FROM dynamic_alternatives 
    WHERE name IN ('<PERSON><PERSON><PERSON>', 'Jaya', '<PERSON>')
    LIMIT 3
);

-- <PERSON><PERSON> hasil TOPSIS untuk alternatif yang akan dihapus
DELETE FROM topsis_results 
WHERE alternative_id NOT IN (
    SELECT id FROM dynamic_alternatives 
    WHERE name IN ('Rahmat', 'Jaya', 'Bunga')
    LIMIT 3
);

-- Hapus alternatif selain Rahmat, Jaya, Bunga
DELETE FROM dynamic_alternatives 
WHERE name NOT IN ('Rahmat', 'Jaya', 'Bunga');

-- Update nama jika perlu (pastikan nama sesuai)
UPDATE dynamic_alternatives SET name = 'Rahmat', position = 'Karyawan' 
WHERE name LIKE '%Rahmat%' OR name LIKE '%rahmat%';

UPDATE dynamic_alternatives SET name = 'Jaya', position = 'Karyawan' 
WHERE name LIKE '%Jaya%' OR name LIKE '%jaya%';

UPDATE dynamic_alternatives SET name = 'Bunga', position = 'Karyawan' 
WHERE name LIKE '%Bunga%' OR name LIKE '%bunga%';

-- Jika ada duplikat, hapus yang ID lebih besar
DELETE FROM dynamic_alternatives 
WHERE id NOT IN (
    SELECT MIN(id) 
    FROM dynamic_alternatives 
    GROUP BY name
);

-- Reset auto increment jika diperlukan (opsional)
-- DELETE FROM sqlite_sequence WHERE name = 'dynamic_alternatives';

-- Verifikasi hasil
.print ""
.print "=== HASIL SETELAH CLEANUP ==="
.print ""
.print "ALTERNATIF YANG TERSISA:"
SELECT id, name, position FROM dynamic_alternatives ORDER BY id;

.print ""
.print "JUMLAH EVALUASI YANG TERSISA:"
SELECT COUNT(*) as total_evaluasi FROM dynamic_evaluations;

.print ""
.print "DETAIL EVALUASI PER ALTERNATIF:"
SELECT 
    a.name as karyawan,
    COUNT(e.id) as jumlah_evaluasi
FROM dynamic_alternatives a
LEFT JOIN dynamic_evaluations e ON a.id = e.alternative_id
GROUP BY a.id, a.name
ORDER BY a.id;

-- Tambahkan data evaluasi jika belum ada (untuk 3 karyawan yang tersisa)
-- Cek apakah sudah ada evaluasi
.print ""
.print "MENAMBAHKAN EVALUASI JIKA BELUM ADA..."

-- Untuk Rahmat (asumsi ID=1 atau nama='Rahmat')
INSERT OR IGNORE INTO dynamic_evaluations (alternative_id, criteria_id, score, created_at, updated_at)
SELECT 
    (SELECT id FROM dynamic_alternatives WHERE name = 'Rahmat' LIMIT 1) as alt_id,
    c.id as criteria_id,
    CASE c.code
        WHEN 'C1' THEN 10
        WHEN 'C2' THEN 9
        WHEN 'C3' THEN 10
        WHEN 'C4' THEN 2
        WHEN 'C5' THEN 15
        ELSE 10
    END as score,
    datetime('now'),
    datetime('now')
FROM dynamic_criteria c
WHERE (SELECT id FROM dynamic_alternatives WHERE name = 'Rahmat' LIMIT 1) IS NOT NULL;

-- Untuk Jaya
INSERT OR IGNORE INTO dynamic_evaluations (alternative_id, criteria_id, score, created_at, updated_at)
SELECT 
    (SELECT id FROM dynamic_alternatives WHERE name = 'Jaya' LIMIT 1) as alt_id,
    c.id as criteria_id,
    CASE c.code
        WHEN 'C1' THEN 14
        WHEN 'C2' THEN 15
        WHEN 'C3' THEN 12
        WHEN 'C4' THEN 2
        WHEN 'C5' THEN 13
        ELSE 10
    END as score,
    datetime('now'),
    datetime('now')
FROM dynamic_criteria c
WHERE (SELECT id FROM dynamic_alternatives WHERE name = 'Jaya' LIMIT 1) IS NOT NULL;

-- Untuk Bunga
INSERT OR IGNORE INTO dynamic_evaluations (alternative_id, criteria_id, score, created_at, updated_at)
SELECT 
    (SELECT id FROM dynamic_alternatives WHERE name = 'Bunga' LIMIT 1) as alt_id,
    c.id as criteria_id,
    CASE c.code
        WHEN 'C1' THEN 13
        WHEN 'C2' THEN 12
        WHEN 'C3' THEN 15
        WHEN 'C4' THEN 1
        WHEN 'C5' THEN 12
        ELSE 10
    END as score,
    datetime('now'),
    datetime('now')
FROM dynamic_criteria c
WHERE (SELECT id FROM dynamic_alternatives WHERE name = 'Bunga' LIMIT 1) IS NOT NULL;

.print ""
.print "=== CLEANUP SELESAI ==="
.print ""
.print "RINGKASAN:"
SELECT 
    (SELECT COUNT(*) FROM dynamic_alternatives) as total_karyawan,
    (SELECT COUNT(*) FROM dynamic_evaluations) as total_evaluasi,
    (SELECT COUNT(*) FROM dynamic_criteria) as total_kriteria;

.print ""
.print "KARYAWAN FINAL:"
SELECT id, name, position FROM dynamic_alternatives ORDER BY name;
