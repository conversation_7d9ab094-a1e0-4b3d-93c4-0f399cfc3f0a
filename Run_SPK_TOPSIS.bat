@echo off
title SPK TOPSIS Enhanced v2.0
color 0A

echo.
echo ============================================================
echo 🏆 SPK TOPSIS Enhanced v2.0 - Desktop Application
echo ============================================================
echo Author: <PERSON> Wibowo
echo NIM: 211011450583
echo Kelas: 06TPLP003
echo ============================================================
echo.
echo 🚀 Starting SPK TOPSIS Application...
echo.
echo 💡 Login Credentials:
echo    👑 Admin: admin / admin123 (Full Access)
echo    ⚙️ Operator: operator / operator123 (Data Input)
echo.
echo 🎨 Starting modern interface...
echo.

python main_modern.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Modern UI failed! Trying enhanced version...
    echo.
    python main_enhanced.py
    if %errorlevel% neq 0 (
        echo.
        echo ❌ Enhanced UI failed! Trying basic version...
        echo.
        python main.py
        if %errorlevel% neq 0 (
            echo ❌ All versions failed!
            echo 💡 Please check your Python installation and dependencies.
            echo.
            echo 📦 Install dependencies:
            echo    pip install -r requirements.txt
            echo.
            pause
        )
    )
) else (
    echo.
    echo 👋 Application closed successfully!
)

echo.
echo Thank you for using SPK TOPSIS Enhanced v2.0!
pause
