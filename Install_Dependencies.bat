@echo off
title SPK TOPSIS - Install Dependencies
color 0E

echo.
echo ============================================================
echo 📦 SPK TOPSIS Enhanced v2.0 - Install Dependencies
echo ============================================================
echo Author: <PERSON> Wibowo
echo NIM: 211011450583
echo Kelas: 06TPLP003
echo ============================================================
echo.
echo This will install all required Python packages for the application.
echo.
echo 📋 Dependencies to install:
echo    - numpy (numerical computing)
echo    - pandas (data manipulation)
echo    - matplotlib (plotting)
echo    - openpyxl (Excel files)
echo    - customtkinter (modern UI)
echo    - ttkbootstrap (enhanced themes)
echo    - Pillow (image processing)
echo.
pause

echo.
echo 🔍 Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python not found!
    echo 💡 Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo.
echo 📦 Installing dependencies...
echo.

pip install --upgrade pip
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo ❌ Installation failed!
    echo 💡 Try running as Administrator or check your internet connection.
    echo.
) else (
    echo.
    echo ✅ All dependencies installed successfully!
    echo.
    echo 🚀 Next steps:
    echo    1. Run Setup_Database.bat to setup sample data
    echo    2. Run Run_SPK_TOPSIS.bat to start the application
    echo.
)

echo.
pause
