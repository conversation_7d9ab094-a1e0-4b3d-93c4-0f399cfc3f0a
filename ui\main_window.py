"""
Main Window untuk SP<PERSON>wan TOPSIS
UI utama dengan navigasi dan layout yang menarik
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database_manager import DatabaseManager
from core.topsis_calculator import TOPSISCalculator

class MainWindow:
    def __init__(self):
        """Initialize main window"""
        self.root = tk.Tk()
        self.db_manager = DatabaseManager()
        self.topsis_calculator = TOPSISCalculator()
        
        # Window configuration
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        
        # Current frame reference
        self.current_frame = None
        
        # Show dashboard by default
        self.show_dashboard()
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("SPK Pengangkatan Karyawan Tetap - TOPSIS")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        
        # Center window on screen
        self.center_window()
        
        # Configure grid weights
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(1, weight=1)
    
    def center_window(self):
        """Center window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_styles(self):
        """Setup custom styles"""
        self.style = ttk.Style()
        
        # Configure styles
        self.style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        self.style.configure('Subtitle.TLabel', font=('Arial', 12, 'bold'))
        self.style.configure('Header.TLabel', font=('Arial', 11, 'bold'))
        self.style.configure('Info.TLabel', font=('Arial', 10))
        
        # Button styles
        self.style.configure('Nav.TButton', font=('Arial', 10, 'bold'), padding=(10, 5))
        self.style.configure('Action.TButton', font=('Arial', 10, 'bold'), padding=(15, 8))
        self.style.configure('Success.TButton', font=('Arial', 10, 'bold'), padding=(15, 8))
        
        # Frame styles
        self.style.configure('Card.TFrame', relief='raised', borderwidth=1)
        self.style.configure('Sidebar.TFrame', background='#f0f0f0')
    
    def create_widgets(self):
        """Create main UI widgets"""
        # Sidebar
        self.create_sidebar()
        
        # Main content area
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.grid(row=0, column=1, sticky='nsew', padx=10, pady=10)
        self.main_frame.grid_rowconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
    
    def create_sidebar(self):
        """Create navigation sidebar"""
        sidebar = ttk.Frame(self.root, style='Sidebar.TFrame', width=200)
        sidebar.grid(row=0, column=0, sticky='ns', padx=(10, 5), pady=10)
        sidebar.grid_propagate(False)
        
        # Title
        title_label = ttk.Label(sidebar, text="SPK TOPSIS", style='Title.TLabel')
        title_label.pack(pady=(20, 10))
        
        subtitle_label = ttk.Label(sidebar, text="Karyawan Tetap", style='Subtitle.TLabel')
        subtitle_label.pack(pady=(0, 30))
        
        # Navigation buttons
        nav_buttons = [
            ("🏠 Dashboard", self.show_dashboard),
            ("👥 Data Karyawan", self.show_employees),
            ("⚖️ Kriteria & Bobot", self.show_criteria),
            ("🏆 Hasil TOPSIS", self.show_results),
            ("📊 Export Data", self.show_export),
            ("📝 Riwayat", self.show_history)
        ]
        
        self.nav_buttons = {}
        for text, command in nav_buttons:
            btn = ttk.Button(sidebar, text=text, command=command, style='Nav.TButton', width=20)
            btn.pack(pady=5, padx=10, fill='x')
            self.nav_buttons[text] = btn
        
        # Separator
        ttk.Separator(sidebar, orient='horizontal').pack(fill='x', pady=20, padx=10)
        
        # Info section
        info_frame = ttk.Frame(sidebar)
        info_frame.pack(fill='x', padx=10)
        
        ttk.Label(info_frame, text="Informasi Sistem", style='Header.TLabel').pack(anchor='w')
        
        # Get database stats
        employees_count = len(self.db_manager.get_all_employees())
        ttk.Label(info_frame, text=f"Total Karyawan: {employees_count}", style='Info.TLabel').pack(anchor='w', pady=2)
        
        results_count = len(self.db_manager.get_topsis_results())
        ttk.Label(info_frame, text=f"Hasil TOPSIS: {results_count}", style='Info.TLabel').pack(anchor='w', pady=2)
        
        # Version info
        ttk.Separator(sidebar, orient='horizontal').pack(fill='x', pady=10, padx=10)
        ttk.Label(sidebar, text="Version 1.0", style='Info.TLabel').pack(pady=5)
    
    def clear_main_frame(self):
        """Clear main content area"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """Show dashboard"""
        self.clear_main_frame()
        self.highlight_nav_button("🏠 Dashboard")
        
        from ui.dashboard import DashboardFrame
        self.current_frame = DashboardFrame(self.main_frame, self.db_manager, self.topsis_calculator)
    
    def show_employees(self):
        """Show employees management"""
        self.clear_main_frame()
        self.highlight_nav_button("👥 Data Karyawan")
        
        from ui.employees import EmployeesFrame
        self.current_frame = EmployeesFrame(self.main_frame, self.db_manager)
    
    def show_criteria(self):
        """Show criteria management"""
        self.clear_main_frame()
        self.highlight_nav_button("⚖️ Kriteria & Bobot")
        
        from ui.criteria import CriteriaFrame
        self.current_frame = CriteriaFrame(self.main_frame, self.db_manager)
    
    def show_results(self):
        """Show TOPSIS results"""
        self.clear_main_frame()
        self.highlight_nav_button("🏆 Hasil TOPSIS")
        
        from ui.results import ResultsFrame
        self.current_frame = ResultsFrame(self.main_frame, self.db_manager, self.topsis_calculator)
    
    def show_export(self):
        """Show export functionality"""
        self.clear_main_frame()
        self.highlight_nav_button("📊 Export Data")
        
        from ui.export import ExportFrame
        self.current_frame = ExportFrame(self.main_frame, self.db_manager)
    
    def show_history(self):
        """Show calculation history"""
        self.clear_main_frame()
        self.highlight_nav_button("📝 Riwayat")
        
        from ui.history import HistoryFrame
        self.current_frame = HistoryFrame(self.main_frame, self.db_manager)
    
    def highlight_nav_button(self, active_text):
        """Highlight active navigation button"""
        for text, button in self.nav_buttons.items():
            if text == active_text:
                button.configure(style='Action.TButton')
            else:
                button.configure(style='Nav.TButton')
    
    def refresh_sidebar_info(self):
        """Refresh sidebar information"""
        # This will be called after data changes
        # For now, we'll recreate the sidebar
        for widget in self.root.grid_slaves(row=0, column=0):
            widget.destroy()
        self.create_sidebar()
    
    def run(self):
        """Run the application"""
        try:
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("Error", f"Terjadi kesalahan: {str(e)}")

def main():
    """Main function"""
    try:
        app = MainWindow()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("Startup Error", f"Gagal memulai aplikasi: {str(e)}")

if __name__ == "__main__":
    main()
