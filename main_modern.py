#!/usr/bin/env python3
"""
SPK Karyawan TOPSIS Enhanced v2.0 - Modern UI Version
Beautiful Desktop Application with Modern Interface

Features:
- Modern CustomTkinter Interface (with fallback to standard Tkinter)
- User Authentication (Login/Logout)
- Dynamic Criteria Management
- Dynamic Alternatives Management
- Enhanced TOPSIS Calculator
- Role-based Access Control
- Beautiful Modern Design

Author: <PERSON> Wibowo
NIM: 211011450583
Kelas: 06TPLP003
"""

import sys
import os
import traceback

# Add current directory to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def check_modern_ui_dependencies():
    """Check if modern UI dependencies are available with caching"""
    # Cache for faster subsequent checks
    if hasattr(check_modern_ui_dependencies, '_cache'):
        return check_modern_ui_dependencies._cache

    dependencies = {
        'customtkinter': 'CustomTkinter',
        'ttkbootstrap': 'TTKBootstrap',
        'numpy': 'NumPy',
        'matplotlib': 'Matplotlib',
        'openpyxl': 'OpenPyXL'
    }

    available = {}
    missing = []

    print("🔍 Checking modern UI dependencies...")

    # Optimized dependency checking
    for module, name in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {name} - Available")
            available[module] = True
        except ImportError:
            print(f"⚠️ {name} - Not available")
            available[module] = False
            if module in ['numpy', 'matplotlib', 'openpyxl']:
                missing.append(name)

    if missing:
        print(f"\n❌ Critical dependencies missing: {', '.join(missing)}")
        print("\n📦 Install missing dependencies:")
        print("pip install -r requirements.txt")
        result = False
    elif available['customtkinter'] or available['ttkbootstrap']:
        print("✅ Modern UI libraries available")
        result = True
    else:
        print("⚠️ No modern UI libraries available, using standard Tkinter")
        result = True  # Still allow to run with standard Tkinter

    # Cache result for performance
    check_modern_ui_dependencies._cache = result
    return result

def setup_modern_application():
    """Setup modern application environment"""
    try:
        # Try to import CustomTkinter for better appearance
        try:
            import customtkinter as ctk
            ctk.set_appearance_mode("light")
            ctk.set_default_color_theme("blue")
            print("✅ CustomTkinter configured")
        except ImportError:
            print("⚠️ CustomTkinter not available, using standard styling")
        
        # Try to import TTKBootstrap for enhanced themes
        try:
            import ttkbootstrap as ttk
            print("✅ TTKBootstrap available")
        except ImportError:
            print("⚠️ TTKBootstrap not available")
        
        return True
    except Exception as e:
        print(f"❌ Failed to setup modern application: {e}")
        return False

def run_modern_application():
    """Run the modern application"""
    print("=" * 60)
    print("🏆 SPK Karyawan TOPSIS Enhanced v2.0 - Modern UI")
    print("=" * 60)
    print("Author: Muhammad Bayu Prasetyo Wibowo")
    print("NIM: 211011450583")
    print("Kelas: 06TPLP003")
    print("=" * 60)
    print()
    
    # Check dependencies
    if not check_modern_ui_dependencies():
        return False
    
    print("\n🎨 Setting up modern application...")
    
    # Setup application
    if not setup_modern_application():
        return False
    
    print("✅ Modern application ready")
    
    try:
        # Import modern UI components
        from modern_ui.modern_login_fixed import show_modern_login

        print("\n🔐 Starting modern login process...")

        # Show modern login window
        user_data = show_modern_login()

        if user_data:
            print(f"✅ Login successful: {user_data['full_name']} ({user_data['role']})")
            print("\n🚀 Starting main application...")

            # Create modern main window wrapper
            main_app = ModernMainApp(user_data)
            main_app.run()

            print("✅ Modern application started successfully!")
            print("\n💡 Enjoy the modern interface!")
            print("=" * 60)

            return True
        else:
            print("❌ Login cancelled or failed")
            return False
            
    except Exception as e:
        print(f"❌ Error starting modern application: {e}")
        print("\nFull error traceback:")
        traceback.print_exc()
        
        # Fallback to standard version
        print("\n🔄 Fallback: Starting standard Tkinter version...")
        try:
            from ui.enhanced_main_window import EnhancedMainWindow
            from ui.login_window import show_login
            
            user_data = show_login()
            if user_data:
                main_app = EnhancedMainWindow(user_data)
                main_app.run()
                return True
        except Exception as fallback_error:
            print(f"❌ Fallback also failed: {fallback_error}")
        
        return False

class ModernMainApp:
    """Modern Main Application Wrapper"""

    def __init__(self, user_data):
        from database.enhanced_database_manager import EnhancedDatabaseManager

        self.user_data = user_data
        self.db_manager = EnhancedDatabaseManager()
        self.db_manager.current_user = user_data

        # Create main window using existing enhanced version
        self.setup_main_window()

    def setup_main_window(self):
        """Setup main window"""
        import tkinter as tk
        from tkinter import ttk, messagebox
        from ui.enhanced_dashboard import EnhancedDashboard
        from ui.dynamic_criteria_manager import DynamicCriteriaManager
        from ui.dynamic_alternatives_manager import DynamicAlternativesManager
        from ui.enhanced_results import EnhancedResults
        from ui.modern_theme import ModernTheme

        # Create main window
        self.root = tk.Tk()

        # Modern title with role icon
        role_icon = "👑" if self.user_data['role'] == 'admin' else "⚙️"
        role_name = "Administrator" if self.user_data['role'] == 'admin' else "Data Operator"
        self.root.title(f"🏆 SPK Karyawan TOPSIS Enhanced v2.0 - Modern UI - {role_icon} {self.user_data['full_name']} ({role_name})")

        self.root.geometry("1400x900")
        self.root.state('zoomed')  # Maximize window

        # Set modern background
        self.root.configure(bg='#f8fafc')

        # Initialize modern theme
        self.theme = ModernTheme()
        self.style = self.theme.apply_modern_style(self.root)

        # Create UI components
        self.create_ui()

        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_ui(self):
        """Create UI components"""
        # Create menu bar
        self.create_menu_bar()

        # Create main layout
        self.create_main_layout()

        # Create status bar
        self.create_status_bar()

        # Show dashboard by default
        self.show_dashboard()

    def create_menu_bar(self):
        """Create menu bar"""
        import tkinter as tk

        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📁 File", menu=file_menu)
        file_menu.add_command(label="🏠 Dashboard", command=self.show_dashboard)
        file_menu.add_separator()
        file_menu.add_command(label="🚪 Logout", command=self.logout)

        # Data menu
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📊 Data", menu=data_menu)
        data_menu.add_command(label="⚖️ Kelola Kriteria", command=self.show_criteria_manager)
        data_menu.add_command(label="👥 Kelola Alternatif", command=self.show_alternatives_manager)

        # Analysis menu
        analysis_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="🧮 Analisis", menu=analysis_menu)
        analysis_menu.add_command(label="🏆 Hasil TOPSIS", command=self.show_results)
        analysis_menu.add_command(label="📈 Hitung TOPSIS", command=self.calculate_topsis)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="❓ Help", menu=help_menu)
        help_menu.add_command(label="📖 Panduan", command=self.show_help)
        help_menu.add_command(label="ℹ️ About", command=self.show_about)

    def create_main_layout(self):
        """Create main layout"""
        from tkinter import ttk

        # Main container
        self.main_container = ttk.Frame(self.root)
        self.main_container.pack(fill='both', expand=True, padx=10, pady=5)

        # Navigation frame
        nav_frame = ttk.Frame(self.main_container)
        nav_frame.pack(fill='x', pady=(0, 10))

        # Navigation buttons with permission check
        nav_buttons = [
            ("🏠 Dashboard", self.show_dashboard, 'view_dashboard'),
            ("⚖️ Kriteria", self.show_criteria_manager, 'view_criteria'),
            ("👥 Alternatif", self.show_alternatives_manager, 'view_alternatives'),
            ("🏆 Hasil", self.show_results, 'view_results'),
            ("🧮 Hitung", self.calculate_topsis, 'calculate_topsis')
        ]

        for text, command, permission in nav_buttons:
            if self.db_manager.has_permission(permission):
                btn = ttk.Button(nav_frame, text=text, style='Nav.TButton', command=command)
                btn.pack(side='left', padx=(0, 10))

        # User info
        user_info = ttk.Label(nav_frame,
                             text=f"👤 {self.user_data['full_name']} | 🔑 {self.user_data['role'].title()}",
                             style='Subtitle.TLabel')
        user_info.pack(side='right')

        # Content frame
        self.content_frame = ttk.Frame(self.main_container)
        self.content_frame.pack(fill='both', expand=True)

    def create_status_bar(self):
        """Create status bar"""
        import tkinter as tk
        from tkinter import ttk

        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill='x', side='bottom')

        self.status_var = tk.StringVar()
        self.status_var.set("✅ Ready - Modern UI")

        status_label = ttk.Label(self.status_bar, textvariable=self.status_var)
        status_label.pack(side='left', padx=10, pady=5)

        # Database info
        db_info = ttk.Label(self.status_bar,
                           text=f"📊 Database: {os.path.basename(self.db_manager.db_path)}")
        db_info.pack(side='right', padx=10, pady=5)

    def clear_content_frame(self):
        """Clear content frame"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        self.current_frame = None

    def show_dashboard(self):
        """Show dashboard"""
        from ui.enhanced_dashboard import EnhancedDashboard
        from tkinter import messagebox

        self.clear_content_frame()
        self.status_var.set("📊 Dashboard - Modern UI")

        try:
            self.current_frame = EnhancedDashboard(self.content_frame, self.db_manager)
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat dashboard: {str(e)}")

    def show_criteria_manager(self):
        """Show criteria manager"""
        from ui.dynamic_criteria_manager import DynamicCriteriaManager
        from tkinter import messagebox

        if not self.db_manager.has_permission('view_criteria'):
            messagebox.showerror("Access Denied", "Anda tidak memiliki permission untuk mengakses Kelola Kriteria!")
            return

        self.clear_content_frame()
        self.status_var.set("⚖️ Kelola Kriteria - Modern UI")

        try:
            self.current_frame = DynamicCriteriaManager(self.content_frame, self.db_manager)
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat kelola kriteria: {str(e)}")

    def show_alternatives_manager(self):
        """Show alternatives manager"""
        from ui.dynamic_alternatives_manager import DynamicAlternativesManager
        from tkinter import messagebox

        self.clear_content_frame()
        self.status_var.set("👥 Kelola Alternatif - Modern UI")

        try:
            self.current_frame = DynamicAlternativesManager(self.content_frame, self.db_manager)
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat kelola alternatif: {str(e)}")

    def show_results(self):
        """Show TOPSIS results"""
        from ui.enhanced_results import EnhancedResults
        from tkinter import messagebox

        self.clear_content_frame()
        self.status_var.set("🏆 Hasil TOPSIS - Modern UI")

        try:
            self.current_frame = EnhancedResults(self.content_frame, self.db_manager)
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat hasil: {str(e)}")

    def calculate_topsis(self):
        """Calculate TOPSIS"""
        from tkinter import messagebox

        if not self.db_manager.has_permission('calculate_topsis'):
            messagebox.showerror("Access Denied",
                               "Anda tidak memiliki permission untuk menghitung TOPSIS!\n\n"
                               "Hanya Admin yang dapat melakukan perhitungan TOPSIS.")
            return

        self.status_var.set("🧮 Menghitung TOPSIS...")

        try:
            # Check if we have criteria and alternatives
            criteria = self.db_manager.get_all_criteria()
            alternatives = self.db_manager.get_all_alternatives()

            if not criteria:
                messagebox.showwarning("Peringatan", "Belum ada kriteria yang didefinisikan!")
                return

            if not alternatives:
                messagebox.showwarning("Peringatan", "Belum ada alternatif yang didefinisikan!")
                return

            # Check if total weight = 1.0
            total_weight = sum(c['bobot'] for c in criteria)
            if abs(total_weight - 1.0) > 0.001:
                messagebox.showwarning("Peringatan",
                                     f"Total bobot kriteria harus 100% (1.0)!\n"
                                     f"Saat ini: {total_weight:.3f} ({total_weight*100:.1f}%)")
                return

            # Import and run TOPSIS calculation
            from core.enhanced_topsis_calculator import EnhancedTOPSISCalculator

            calculator = EnhancedTOPSISCalculator(self.db_manager)
            results = calculator.calculate()

            if results:
                messagebox.showinfo("✅ Berhasil",
                                  f"Perhitungan TOPSIS selesai!\n"
                                  f"Total alternatif: {len(results)}")
                self.show_results()
            else:
                messagebox.showerror("❌ Error", "Gagal menghitung TOPSIS!")

        except Exception as e:
            messagebox.showerror("Error", f"Error dalam perhitungan TOPSIS: {str(e)}")
        finally:
            self.status_var.set("✅ Ready - Modern UI")

    def show_help(self):
        """Show help"""
        from tkinter import messagebox

        help_text = """
🔹 PANDUAN PENGGUNAAN SPK KARYAWAN TOPSIS - MODERN UI 🔹

1. 📊 DASHBOARD
   - Melihat ringkasan sistem
   - Statistik kriteria dan alternatif
   - Quick actions

2. ⚖️ KELOLA KRITERIA
   - Tambah/edit/hapus kriteria
   - Atur bobot kriteria
   - Validasi total bobot 100%

3. 👥 KELOLA ALTERNATIF
   - Tambah/edit/hapus alternatif (karyawan)
   - Input nilai untuk setiap kriteria
   - Validasi data input

4. 🧮 HITUNG TOPSIS
   - Jalankan perhitungan TOPSIS
   - Otomatis ranking alternatif
   - Simpan hasil perhitungan

5. 🏆 HASIL
   - Lihat ranking TOPSIS
   - Export hasil ke CSV
   - Analisis detail

💡 Tips:
- Pastikan total bobot kriteria = 100%
- Input semua nilai kriteria untuk alternatif
- Gunakan nilai 1-15 untuk konsistensi

🎨 Modern UI Features:
- CustomTkinter styling
- Enhanced visual design
- Improved user experience
        """

        messagebox.showinfo("📖 Panduan Penggunaan - Modern UI", help_text)

    def show_about(self):
        """Show about dialog"""
        from tkinter import messagebox

        about_text = f"""
🏆 SPK Karyawan TOPSIS v2.0 - Modern UI

📋 Sistem Pendukung Keputusan untuk Pengangkatan Karyawan Tetap
🔬 Menggunakan Metode TOPSIS (Technique for Order Preference by Similarity to Ideal Solution)

👨‍💻 Pengembang:
Muhammad Bayu Prasetyo Wibowo
NIM: 211011450583
Kelas: 06TPLP003

🆔 User Saat Ini:
Nama: {self.user_data['full_name']}
Username: {self.user_data['username']}
Role: {self.user_data['role'].title()}

🛠️ Teknologi:
- Python 3.8+
- CustomTkinter (Modern UI)
- SQLite Database
- TOPSIS Algorithm

🎨 UI Features:
- Modern CustomTkinter styling
- Enhanced visual design
- Improved user experience

© 2024 - Sistem Penunjang Keputusan
        """

        messagebox.showinfo("ℹ️ About - Modern UI", about_text)

    def logout(self):
        """Logout user and return to login"""
        from tkinter import messagebox

        if messagebox.askyesno("🚪 Logout",
                              f"Logout dari akun {self.user_data.get('full_name', 'User')}?\n\n"
                              f"Anda akan kembali ke halaman login."):
            self.db_manager.logout_user()
            self.root.destroy()

            # Restart application to show login again
            import subprocess
            import sys
            subprocess.Popen([sys.executable, __file__])

    def on_closing(self):
        """Handle window closing - simplified without redundant confirmation"""
        # Just logout and close - user already clicked X button
        self.db_manager.logout_user()
        self.root.destroy()

    def run(self):
        """Run the application"""
        self.root.mainloop()

def install_modern_dependencies():
    """Install modern UI dependencies"""
    print("📦 Installing modern UI dependencies...")
    
    try:
        import subprocess
        
        # Install CustomTkinter
        print("🔄 Installing CustomTkinter...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'customtkinter>=5.2.0'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CustomTkinter installed successfully")
        else:
            print(f"⚠️ CustomTkinter installation warning: {result.stderr}")
        
        # Install TTKBootstrap
        print("🔄 Installing TTKBootstrap...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'ttkbootstrap>=1.10.0'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ TTKBootstrap installed successfully")
        else:
            print(f"⚠️ TTKBootstrap installation warning: {result.stderr}")
        
        # Install other requirements
        print("🔄 Installing other requirements...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ All requirements installed successfully")
        else:
            print(f"⚠️ Some requirements may have issues: {result.stderr}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def main():
    """Main entry point"""
    try:
        # Check if dependencies are available
        if not check_modern_ui_dependencies():
            print("\n🔧 Would you like to install modern UI dependencies? (y/n): ", end="")
            try:
                choice = input().lower().strip()
                if choice in ['y', 'yes']:
                    if install_modern_dependencies():
                        print("\n✅ Dependencies installed! Restarting application...")
                        return run_modern_application()
                    else:
                        print("\n❌ Failed to install dependencies")
                        return False
                else:
                    print("\n🔄 Continuing with available components...")
            except (EOFError, KeyboardInterrupt):
                print("\n⚠️ Installation skipped")
        
        # Run application
        result = run_modern_application()
        
        if result:
            print("\n👋 Modern application closed successfully!")
        else:
            print("\n❌ Modern application failed to start")
            print("\n🔄 You can try the standard version:")
            print("python main_enhanced.py")
        
        return result
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Application interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Run application
    success = main()
    sys.exit(0 if success else 1)
