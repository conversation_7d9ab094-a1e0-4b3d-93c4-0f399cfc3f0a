@echo off
title <PERSON><PERSON> Karyawan TOPSIS Enhanced v2.0 - Modern UI
color 0D

echo ============================================================
echo 🏆 <PERSON><PERSON>wan TOPSIS Enhanced v2.0 - Modern UI
echo ============================================================
echo Author: <PERSON> Wibowo
echo NIM: 211011450583
echo Kelas: 06TPLP003
echo ============================================================
echo.

echo 🔍 Checking Python installation...
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.8+ first.
    echo 📥 Download from: https://python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python found

echo.
echo 📦 Checking modern UI dependencies...
py -c "import customtkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ CustomTkinter not found! Installing modern UI components...
    echo 🔄 Installing CustomTkinter and TTKBootstrap...
    py -m pip install customtkinter>=5.2.0 ttkbootstrap>=1.10.0
    if %errorlevel% neq 0 (
        echo ❌ Failed to install modern UI components!
        echo 💡 Continuing with standard Tkinter...
    ) else (
        echo ✅ Modern UI components installed successfully
    )
) else (
    echo ✅ CustomTkinter ready
)

echo.
echo 📦 Installing core dependencies...
py -m pip install numpy matplotlib openpyxl >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Some core dependencies may be missing, but continuing...
) else (
    echo ✅ Core dependencies ready
)

echo.
echo 🎨 Starting Modern UI Application...
echo 💡 Login Credentials:
echo    👑 Admin: admin / admin123 (Full Access)
echo    ⚙️ Operator: operator / operator123 (Data Input)
echo.
echo 🚀 Launching beautiful modern interface...
echo.

py main_modern.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Modern UI application failed!
    echo 🔄 Starting standard Tkinter fallback...
    echo.
    py main_enhanced.py
    if %errorlevel% neq 0 (
        echo ❌ Both modern and standard versions failed!
        echo 💡 Please check your Python installation and dependencies.
        pause
    )
) else (
    echo.
    echo 👋 Modern UI application closed successfully!
)

echo.
echo Thank you for using SPK TOPSIS Enhanced v2.0!
pause
