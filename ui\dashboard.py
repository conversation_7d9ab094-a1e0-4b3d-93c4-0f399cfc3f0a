"""
Dashboard Frame untuk SPK Karyawan TOPSIS
Menampilkan overview sistem dan statistik
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

class DashboardFrame:
    def __init__(self, parent, db_manager, topsis_calculator):
        """Initialize dashboard frame"""
        self.parent = parent
        self.db_manager = db_manager
        self.topsis_calculator = topsis_calculator
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        self.create_widgets()
        self.load_data()
    
    def create_widgets(self):
        """Create dashboard widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="Dashboard SPK Karyawan TOPSIS", 
                 style='Title.TLabel').pack(side='left')
        
        # Refresh button
        ttk.Button(title_frame, text="🔄 Refresh", 
                  command=self.refresh_data, style='Action.TButton').pack(side='right')
        
        # Statistics cards
        self.create_stats_cards()
        
        # Charts section
        self.create_charts_section()
        
        # Quick actions
        self.create_quick_actions()
    
    def create_stats_cards(self):
        """Create statistics cards"""
        stats_frame = ttk.Frame(self.frame)
        stats_frame.pack(fill='x', pady=(0, 20))
        
        # Configure grid
        for i in range(4):
            stats_frame.grid_columnconfigure(i, weight=1)
        
        # Get statistics
        employees_count = len(self.db_manager.get_all_employees())
        results_count = len(self.db_manager.get_topsis_results())
        history_count = len(self.db_manager.get_calculation_history())
        
        # Calculate average score if results exist
        results = self.db_manager.get_topsis_results()
        avg_score = 0
        if results:
            scores = [r.get('score', 0) for r in results if r.get('score')]
            avg_score = sum(scores) / len(scores) if scores else 0
        
        # Create cards
        cards_data = [
            ("👥", "Total Karyawan", str(employees_count), "#4CAF50"),
            ("🏆", "Hasil TOPSIS", str(results_count), "#2196F3"),
            ("📊", "Rata-rata Score", f"{avg_score:.3f}", "#FF9800"),
            ("📝", "Riwayat Perhitungan", str(history_count), "#9C27B0")
        ]
        
        self.stat_cards = []
        for i, (icon, title, value, color) in enumerate(cards_data):
            card = self.create_stat_card(stats_frame, icon, title, value, color)
            card.grid(row=0, column=i, padx=5, sticky='ew')
            self.stat_cards.append((card, title, value))
    
    def create_stat_card(self, parent, icon, title, value, color):
        """Create individual statistics card"""
        card = ttk.Frame(parent, style='Card.TFrame')
        card.configure(relief='raised', borderwidth=2)
        
        # Icon
        icon_label = ttk.Label(card, text=icon, font=('Arial', 24))
        icon_label.pack(pady=(10, 5))
        
        # Value
        value_label = ttk.Label(card, text=value, font=('Arial', 18, 'bold'))
        value_label.pack()
        
        # Title
        title_label = ttk.Label(card, text=title, font=('Arial', 10))
        title_label.pack(pady=(0, 10))
        
        return card
    
    def create_charts_section(self):
        """Create charts section"""
        charts_frame = ttk.Frame(self.frame)
        charts_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # Configure grid
        charts_frame.grid_rowconfigure(0, weight=1)
        charts_frame.grid_columnconfigure(0, weight=1)
        charts_frame.grid_columnconfigure(1, weight=1)
        
        # Left chart - Criteria weights
        self.create_criteria_chart(charts_frame)
        
        # Right chart - Results distribution
        self.create_results_chart(charts_frame)
    
    def create_criteria_chart(self, parent):
        """Create criteria weights pie chart"""
        chart_frame = ttk.LabelFrame(parent, text="Distribusi Bobot Kriteria", padding=10)
        chart_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 5))
        
        # Get criteria data
        criteria = self.db_manager.get_all_criteria()
        
        if criteria:
            # Create matplotlib figure
            fig, ax = plt.subplots(figsize=(5, 4))
            
            labels = [f"{c['kode']}: {c['nama']}" for c in criteria]
            sizes = [c['bobot'] * 100 for c in criteria]  # Convert to percentage
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
            
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, 
                                            autopct='%1.1f%%', startangle=90)
            
            # Improve text appearance
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            ax.set_title('Bobot Kriteria TOPSIS', fontweight='bold')
            
            # Embed in tkinter
            canvas = FigureCanvasTkAgg(fig, chart_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)
        else:
            ttk.Label(chart_frame, text="Tidak ada data kriteria", 
                     style='Info.TLabel').pack(expand=True)
    
    def create_results_chart(self, parent):
        """Create results distribution bar chart"""
        chart_frame = ttk.LabelFrame(parent, text="Distribusi Status Hasil", padding=10)
        chart_frame.grid(row=0, column=1, sticky='nsew', padx=(5, 0))
        
        # Get results data
        results = self.db_manager.get_topsis_results()
        
        if results:
            # Count status distribution
            status_counts = {}
            for result in results:
                status = result.get('status', 'Unknown')
                status_counts[status] = status_counts.get(status, 0) + 1
            
            if status_counts:
                # Create matplotlib figure
                fig, ax = plt.subplots(figsize=(5, 4))
                
                statuses = list(status_counts.keys())
                counts = list(status_counts.values())
                colors = ['#2ECC71', '#3498DB', '#F39C12', '#E74C3C']
                
                bars = ax.bar(range(len(statuses)), counts, color=colors[:len(statuses)])
                
                # Customize chart
                ax.set_xlabel('Status Rekomendasi')
                ax.set_ylabel('Jumlah Karyawan')
                ax.set_title('Distribusi Status Hasil TOPSIS', fontweight='bold')
                ax.set_xticks(range(len(statuses)))
                ax.set_xticklabels([s.replace(' ', '\n') for s in statuses], fontsize=8)
                
                # Add value labels on bars
                for bar, count in zip(bars, counts):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                           str(count), ha='center', va='bottom', fontweight='bold')
                
                plt.tight_layout()
                
                # Embed in tkinter
                canvas = FigureCanvasTkAgg(fig, chart_frame)
                canvas.draw()
                canvas.get_tk_widget().pack(fill='both', expand=True)
            else:
                ttk.Label(chart_frame, text="Tidak ada data hasil", 
                         style='Info.TLabel').pack(expand=True)
        else:
            ttk.Label(chart_frame, text="Belum ada hasil TOPSIS\nSilakan hitung terlebih dahulu", 
                     style='Info.TLabel').pack(expand=True)
    
    def create_quick_actions(self):
        """Create useful quick actions section"""
        actions_frame = ttk.LabelFrame(self.frame, text="⚡ Quick Actions", padding=15)
        actions_frame.pack(fill='x')

        # Configure grid
        for i in range(3):
            actions_frame.grid_columnconfigure(i, weight=1)

        # Only useful action buttons
        actions = [
            ("🧮 Hitung TOPSIS", self.calculate_topsis_smart, "Action.TButton"),
            ("📊 Lihat Hasil", self.view_results_smart, "Action.TButton"),
            ("🔄 Refresh Data", self.refresh_data, "Success.TButton")
        ]

        for i, (text, command, style) in enumerate(actions):
            btn = ttk.Button(actions_frame, text=text, command=command, style=style)
            btn.grid(row=0, column=i, padx=5, sticky='ew')
    
    def calculate_topsis_smart(self):
        """Smart TOPSIS calculation with better validation"""
        try:
            # Get employees and criteria
            employees = self.db_manager.get_all_employees()
            criteria = self.db_manager.get_all_criteria()

            if not employees:
                messagebox.showwarning("⚠️ Peringatan",
                                     "Tidak ada data karyawan untuk dihitung!\n\n"
                                     "Silakan tambah data karyawan terlebih dahulu.")
                return

            if not criteria:
                messagebox.showwarning("⚠️ Peringatan",
                                     "Tidak ada kriteria yang didefinisikan!\n\n"
                                     "Silakan tambah kriteria terlebih dahulu.")
                return

            if len(employees) < 2:
                messagebox.showwarning("⚠️ Peringatan",
                                     f"Minimal 2 karyawan diperlukan untuk perhitungan TOPSIS!\n\n"
                                     f"Saat ini hanya ada {len(employees)} karyawan.")
                return

            # Check total weight
            total_weight = sum(c['bobot'] for c in criteria)
            if abs(total_weight - 1.0) > 0.001:
                messagebox.showwarning("⚠️ Peringatan",
                                     f"Total bobot kriteria harus 100%!\n\n"
                                     f"Saat ini: {total_weight*100:.1f}%")
                return

            # Prepare criteria weights
            criteria_weights = {c['kode']: c['bobot'] for c in criteria}

            # Validate data
            is_valid, message = self.topsis_calculator.validate_data(employees, criteria_weights)
            if not is_valid:
                messagebox.showerror("❌ Error Validasi", message)
                return

            # Calculate TOPSIS
            results = self.topsis_calculator.calculate_topsis(employees, criteria_weights)

            # Save results
            session_name = f"Perhitungan {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            success = self.db_manager.save_topsis_results(results, session_name)

            if success:
                messagebox.showinfo("✅ Sukses",
                                  f"Perhitungan TOPSIS berhasil!\n\n"
                                  f"📊 Total karyawan: {len(results)}\n"
                                  f"📅 Sesi: {session_name}\n\n"
                                  f"Lihat hasil di menu 'Hasil TOPSIS'")
                self.refresh_data()
            else:
                messagebox.showerror("❌ Error", "Gagal menyimpan hasil perhitungan")

        except Exception as e:
            messagebox.showerror("❌ Error", f"Terjadi kesalahan saat perhitungan: {str(e)}")

    def view_results_smart(self):
        """Smart view results with validation"""
        try:
            results = self.db_manager.get_topsis_results()
            if not results:
                messagebox.showinfo("ℹ️ Info",
                                  "Belum ada hasil perhitungan TOPSIS!\n\n"
                                  "Silakan lakukan perhitungan terlebih dahulu.")
                return

            # Get the main window instance
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'show_results'):
                main_window.show_results()
            else:
                messagebox.showinfo("✅ Hasil Tersedia",
                                  f"Ada {len(results)} hasil perhitungan.\n\n"
                                  f"Gunakan menu 'Hasil TOPSIS' untuk melihat detail.")
        except Exception as e:
            messagebox.showerror("❌ Error", f"Error accessing results: {str(e)}")

    def get_main_window(self):
        """Get main window instance"""
        widget = self.parent
        while widget:
            if hasattr(widget, 'show_results'):
                return widget
            widget = widget.master
        return None
    
    def load_data(self):
        """Load initial data"""
        self.refresh_data()
    
    def refresh_data(self):
        """Refresh dashboard data"""
        try:
            # Clear and recreate widgets
            for widget in self.frame.winfo_children():
                widget.destroy()
            
            self.create_widgets()
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat data: {str(e)}")
