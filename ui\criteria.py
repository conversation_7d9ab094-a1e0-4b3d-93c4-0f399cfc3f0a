"""
Criteria Frame untuk SPK Karyawan TOPSIS
Mengelola kriteria dan bobot dengan slider dan validasi
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class CriteriaFrame:
    def __init__(self, parent, db_manager):
        """Initialize criteria frame"""
        self.parent = parent
        self.db_manager = db_manager
        
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill='both', expand=True)
        
        # Variables for sliders
        self.weight_vars = {}
        self.weight_labels = {}
        self.total_var = tk.StringVar()
        
        self.create_widgets()
        self.load_criteria()
    
    def create_widgets(self):
        """Create criteria management widgets"""
        # Title
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill='x', pady=(0, 20))
        
        ttk.Label(title_frame, text="Pengaturan Kriteria dan <PERSON>", 
                 style='Title.TLabel').pack(side='left')
        
        ttk.Button(title_frame, text="🔄 Reset Default", 
                  command=self.reset_to_default, style='Nav.TButton').pack(side='right', padx=(10, 0))
        
        ttk.Button(title_frame, text="💾 Simpan Bobot", 
                  command=self.save_weights, style='Success.TButton').pack(side='right')
        
        # Main container
        main_container = ttk.Frame(self.frame)
        main_container.pack(fill='both', expand=True)
        
        # Configure grid
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=2)
        main_container.grid_columnconfigure(1, weight=1)
        
        # Left side - Weight adjustment
        self.create_weight_panel(main_container)
        
        # Right side - Visualization
        self.create_visualization_panel(main_container)
    
    def create_weight_panel(self, parent):
        """Create weight adjustment panel"""
        weight_frame = ttk.LabelFrame(parent, text="Pengaturan Bobot Kriteria", padding=20)
        weight_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 10))
        
        # Instructions
        instruction_text = """
Atur bobot untuk setiap kriteria menggunakan slider di bawah.
Total bobot harus sama dengan 100% (1.0).
Bobot yang lebih tinggi menunjukkan kriteria yang lebih penting.
        """
        ttk.Label(weight_frame, text=instruction_text.strip(), 
                 style='Info.TLabel', justify='left').pack(anchor='w', pady=(0, 20))
        
        # Criteria information
        criteria_info = [
            ('C1', 'Kemampuan Teknik', 'benefit', 'Kemampuan teknis dalam bekerja'),
            ('C2', 'Kualitas', 'benefit', 'Kualitas hasil kerja'),
            ('C3', 'Presisi', 'benefit', 'Ketepatan dan ketelitian kerja'),
            ('C4', 'Pelanggaran', 'cost', 'Jumlah pelanggaran (semakin rendah semakin baik)'),
            ('C5', 'Absensi', 'benefit', 'Tingkat kehadiran (semakin tinggi semakin baik)')
        ]
        
        # Create sliders for each criteria
        for kode, nama, jenis, deskripsi in criteria_info:
            self.create_criteria_slider(weight_frame, kode, nama, jenis, deskripsi)
        
        # Total weight display
        total_frame = ttk.Frame(weight_frame)
        total_frame.pack(fill='x', pady=20)
        
        ttk.Label(total_frame, text="Total Bobot:", 
                 style='Header.TLabel').pack(side='left')
        
        self.total_label = ttk.Label(total_frame, textvariable=self.total_var, 
                                    style='Subtitle.TLabel', foreground='red')
        self.total_label.pack(side='left', padx=(10, 0))
        
        # Validation message
        self.validation_var = tk.StringVar()
        self.validation_label = ttk.Label(weight_frame, textvariable=self.validation_var, 
                                         style='Info.TLabel', foreground='red')
        self.validation_label.pack(pady=10)
    
    def create_criteria_slider(self, parent, kode, nama, jenis, deskripsi):
        """Create slider for individual criteria"""
        # Main frame for this criteria
        criteria_frame = ttk.Frame(parent)
        criteria_frame.pack(fill='x', pady=10)
        
        # Header with criteria info
        header_frame = ttk.Frame(criteria_frame)
        header_frame.pack(fill='x')
        
        # Criteria name and type
        name_text = f"{kode} - {nama}"
        if jenis == 'cost':
            name_text += " (Cost)"
        else:
            name_text += " (Benefit)"
        
        ttk.Label(header_frame, text=name_text, 
                 style='Header.TLabel').pack(side='left')
        
        # Weight display
        weight_var = tk.DoubleVar()
        self.weight_vars[kode] = weight_var
        
        weight_label = ttk.Label(header_frame, text="0.00 (0%)", 
                               style='Info.TLabel', foreground='blue')
        weight_label.pack(side='right')
        self.weight_labels[kode] = weight_label
        
        # Description
        ttk.Label(criteria_frame, text=deskripsi, 
                 style='Info.TLabel', foreground='gray').pack(anchor='w', pady=(2, 5))
        
        # Slider
        slider_frame = ttk.Frame(criteria_frame)
        slider_frame.pack(fill='x')
        
        ttk.Label(slider_frame, text="0%").pack(side='left')
        
        slider = ttk.Scale(slider_frame, from_=0, to=1, orient='horizontal',
                          variable=weight_var, command=lambda v, k=kode: self.on_weight_change(k))
        slider.pack(side='left', fill='x', expand=True, padx=10)
        
        ttk.Label(slider_frame, text="100%").pack(side='right')
        
        # Bind events
        weight_var.trace('w', lambda *args, k=kode: self.on_weight_change(k))
    
    def create_visualization_panel(self, parent):
        """Create visualization panel"""
        viz_frame = ttk.LabelFrame(parent, text="Visualisasi Bobot", padding=10)
        viz_frame.grid(row=0, column=1, sticky='nsew', padx=(10, 0))
        
        # Configure grid
        viz_frame.grid_rowconfigure(0, weight=1)
        viz_frame.grid_columnconfigure(0, weight=1)

        # Create matplotlib figure
        plt.style.use('default')  # Ensure default style
        self.fig, self.ax = plt.subplots(figsize=(6, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, viz_frame)
        self.canvas.get_tk_widget().grid(row=0, column=0, sticky='nsew')
        
        # Initial empty chart
        self.update_chart()
        
        # Chart controls
        control_frame = ttk.Frame(viz_frame)
        control_frame.grid(row=1, column=0, pady=10)
        
        ttk.Button(control_frame, text="📊 Update Chart", 
                  command=self.update_chart, style='Action.TButton').pack(side='left', padx=5)
        
        ttk.Button(control_frame, text="⚖️ Auto Balance", 
                  command=self.auto_balance, style='Nav.TButton').pack(side='left', padx=5)
    
    def load_criteria(self):
        """Load criteria data from database"""
        try:
            criteria = self.db_manager.get_all_criteria()
            
            for criterion in criteria:
                kode = criterion['kode']
                bobot = criterion['bobot']
                
                if kode in self.weight_vars:
                    self.weight_vars[kode].set(bobot)
            
            self.update_total()
            self.update_chart()
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal memuat data kriteria: {str(e)}")
    
    def on_weight_change(self, kode):
        """Handle weight change"""
        try:
            weight = self.weight_vars[kode].get()
            percentage = weight * 100
            
            # Update label
            self.weight_labels[kode].config(text=f"{weight:.2f} ({percentage:.1f}%)")
            
            # Update total
            self.update_total()
            
            # Update chart
            self.update_chart()
            
        except Exception as e:
            print(f"Error updating weight for {kode}: {e}")
    
    def update_total(self):
        """Update total weight display"""
        try:
            total = sum(var.get() for var in self.weight_vars.values())
            percentage = total * 100
            
            self.total_var.set(f"{total:.3f} ({percentage:.1f}%)")
            
            # Validation
            if abs(total - 1.0) < 0.001:
                self.total_label.config(foreground='green')
                self.validation_var.set("✓ Total bobot valid (100%)")
                self.validation_label.config(foreground='green')
            else:
                self.total_label.config(foreground='red')
                if total > 1.0:
                    self.validation_var.set("⚠ Total bobot terlalu besar! Kurangi beberapa kriteria.")
                else:
                    self.validation_var.set("⚠ Total bobot terlalu kecil! Tambah beberapa kriteria.")
                self.validation_label.config(foreground='red')
                
        except Exception as e:
            print(f"Error updating total: {e}")
    
    def update_chart(self):
        """Update pie chart visualization"""
        try:
            # Clear previous chart
            self.ax.clear()
            
            # Get current weights
            weights = []
            labels = []
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
            
            criteria_names = {
                'C1': 'Kemampuan Teknik',
                'C2': 'Kualitas',
                'C3': 'Presisi',
                'C4': 'Pelanggaran',
                'C5': 'Absensi'
            }
            
            for kode in ['C1', 'C2', 'C3', 'C4', 'C5']:
                if kode in self.weight_vars:
                    weight = self.weight_vars[kode].get()
                    weights.append(weight)
                    labels.append(f"{kode}: {criteria_names[kode]}")
            
            # Only create chart if we have weights
            if any(w > 0 for w in weights):
                wedges, texts, autotexts = self.ax.pie(weights, labels=labels, colors=colors,
                                                     autopct='%1.1f%%', startangle=90)
                
                # Improve text appearance
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')
                    autotext.set_fontsize(9)
                
                for text in texts:
                    text.set_fontsize(8)
            else:
                # Empty chart
                self.ax.text(0.5, 0.5, 'Atur bobot kriteria\nuntuk melihat visualisasi', 
                           ha='center', va='center', transform=self.ax.transAxes,
                           fontsize=12, style='italic')
            
            self.ax.set_title('Distribusi Bobot Kriteria TOPSIS', fontweight='bold', fontsize=12)
            
            # Refresh canvas
            self.canvas.draw()
            
        except Exception as e:
            print(f"Error updating chart: {e}")
    
    def auto_balance(self):
        """Auto balance weights to equal distribution"""
        try:
            # Set equal weights (0.2 each for 5 criteria)
            equal_weight = 1.0 / len(self.weight_vars)
            
            for var in self.weight_vars.values():
                var.set(equal_weight)
            
            messagebox.showinfo("Auto Balance", f"Bobot telah diatur secara merata: {equal_weight:.3f} ({equal_weight*100:.1f}%) untuk setiap kriteria")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal melakukan auto balance: {str(e)}")
    
    def reset_to_default(self):
        """Reset weights to default values"""
        try:
            default_weights = {
                'C1': 0.14,  # Kemampuan Teknik
                'C2': 0.19,  # Kualitas
                'C3': 0.28,  # Presisi
                'C4': 0.18,  # Pelanggaran
                'C5': 0.21   # Absensi
            }
            
            for kode, weight in default_weights.items():
                if kode in self.weight_vars:
                    self.weight_vars[kode].set(weight)
            
            messagebox.showinfo("Reset", "Bobot telah direset ke nilai default")
            
        except Exception as e:
            messagebox.showerror("Error", f"Gagal reset ke default: {str(e)}")
    
    def validate_weights(self):
        """Validate current weights"""
        try:
            total = sum(var.get() for var in self.weight_vars.values())
            
            if abs(total - 1.0) > 0.001:
                return False, f"Total bobot harus 1.0 (100%), saat ini: {total:.3f} ({total*100:.1f}%)"
            
            # Check if any weight is negative or too small
            for kode, var in self.weight_vars.items():
                weight = var.get()
                if weight < 0:
                    return False, f"Bobot {kode} tidak boleh negatif"
                if weight > 1:
                    return False, f"Bobot {kode} tidak boleh lebih dari 1.0"
            
            return True, "Bobot valid"
            
        except Exception as e:
            return False, f"Error validasi: {str(e)}"
    
    def save_weights(self):
        """Save current weights to database"""
        try:
            # Validate first
            is_valid, message = self.validate_weights()
            if not is_valid:
                messagebox.showerror("Error Validasi", message)
                return
            
            # Prepare weights dictionary
            weights = {}
            for kode, var in self.weight_vars.items():
                weights[kode] = var.get()
            
            # Save to database
            success = self.db_manager.update_criteria_weights(weights)
            
            if success:
                messagebox.showinfo("Sukses", "Bobot kriteria berhasil disimpan!")
            else:
                messagebox.showerror("Error", "Gagal menyimpan bobot kriteria!")
                
        except Exception as e:
            messagebox.showerror("Error", f"Terjadi kesalahan: {str(e)}")
