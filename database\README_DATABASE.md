# 🗄️ DATABASE SPK TOPSIS ENHANCED v2.0

## Folder Database Lengkap

---

**Author:** <PERSON>rasetyo Wibowo  
**NIM:** 211011450583  
**Kelas:** 06TPLP003  
**Database Engine:** SQLite 3

---

## 📁 Struktur Folder Database

```
database/
├── README_DATABASE.md              # 📖 Dokumentasi folder ini
├── SPK_TOPSIS_DATABASE_COMPLETE.sql # 🗄️ File SQL lengkap (UTAMA) ⭐
├── setup_database.py               # 🐍 Python setup script
└── view_database.py                # 👁️ Python database viewer
```

**Total: 4 file penting saja!**

---

## 🎯 File Utama

### 1. **SPK_TOPSIS_DATABASE_COMPLETE.sql** ⭐

**File paling penting!** Berisi:

- ✅ 6 Tabel utama dengan struktur lengkap
- ✅ 4 Views untuk reporting
- ✅ Triggers untuk validasi data
- ✅ Index untuk optimasi performa
- ✅ Data sample lengkap (users, criteria, employees, evaluations)
- ✅ Verification queries

### 2. **setup_database.py**

Script Python untuk:

- ✅ Setup database otomatis
- ✅ Create tables & indexes
- ✅ Insert initial data
- ✅ Validation & verification

### 3. **view_database.py**

Script Python untuk:

- ✅ Melihat isi database
- ✅ Tampilan user-friendly
- ✅ Statistik database
- ✅ Verifikasi data

---

## 🚀 Cara Menggunakan

### **Metode 1: SQL File (Recommended)**

```bash
# Buat database dari file SQL lengkap
sqlite3 spk_karyawan_enhanced.db < database/SPK_TOPSIS_DATABASE_COMPLETE.sql
```

### **Metode 2: Python Script**

```bash
# Jalankan setup Python
cd database
python setup_database.py
```

---

## 📊 Data Sample

### 👤 **Users (2 users)**

| Username | Password    | Role     |
| -------- | ----------- | -------- |
| admin    | admin123    | admin    |
| operator | operator123 | operator |

### 📊 **Kriteria (5 kriteria, total 100%)**

| Code | Nama             | Bobot | Jenis   |
| ---- | ---------------- | ----- | ------- |
| C1   | Kemampuan Teknik | 14%   | Benefit |
| C2   | Kualitas         | 19%   | Benefit |
| C3   | Presisi          | 28%   | Benefit |
| C4   | Pelanggaran      | 18%   | Cost    |
| C5   | Absensi          | 21%   | Benefit |

### 👥 **Karyawan (3 karyawan)**

| Nama   | Posisi   | Employee ID |
| ------ | -------- | ----------- |
| Rahmat | Karyawan | EMP001      |
| Jaya   | Karyawan | EMP002      |
| Bunga  | Karyawan | EMP003      |

### 📝 **Matriks Evaluasi (15 data)**

| Karyawan | C1  | C2  | C3  | C4  | C5  |
| -------- | --- | --- | --- | --- | --- |
| Rahmat   | 10  | 9   | 10  | 2   | 15  |
| Jaya     | 14  | 15  | 12  | 2   | 13  |
| Bunga    | 13  | 12  | 15  | 1   | 12  |

---

## 🔍 Melihat Isi Database

### **Metode 1: Python Viewer**

```bash
cd database
python view_database.py
```

### **Metode 2: SQLite Command Line**

```bash
sqlite3 spk_karyawan_enhanced.db
.tables
.headers on
.mode table
SELECT * FROM users;
SELECT * FROM dynamic_criteria;
```

---

## 📈 Prediksi Ranking TOPSIS

Berdasarkan data sample dan bobot kriteria:

| Ranking | Karyawan   | Prediksi Nilai | Alasan                                             |
| ------- | ---------- | -------------- | -------------------------------------------------- |
| 🥇 1    | **Bunga**  | ~77%           | Presisi tertinggi (15) + pelanggaran terendah (1)  |
| 🥈 2    | **Jaya**   | ~55%           | Kemampuan teknik (14) + kualitas (15) terbaik      |
| 🥉 3    | **Rahmat** | ~45%           | Absensi terbaik (15) tapi skill perlu ditingkatkan |

---

## 🛠️ Maintenance

### **Backup Database**

```bash
# Manual backup
cp spk_karyawan_enhanced.db database/backup/backup_$(date +%Y%m%d_%H%M%S).db

# Python backup
python -c "from database_manager import SPKTopsisDatabase; db = SPKTopsisDatabase(); db.connect(); db.backup_database()"
```

### **Optimize Database**

```sql
VACUUM;
REINDEX;
ANALYZE;
```

### **Check Integrity**

```sql
PRAGMA integrity_check;
PRAGMA foreign_key_check;
```

---

## 🔧 Troubleshooting

### **Database tidak terbuat**

1. Pastikan SQLite3 terinstall
2. Cek permissions folder
3. Jalankan sebagai administrator

### **Error foreign key**

```sql
PRAGMA foreign_keys = ON;
```

### **Data tidak muncul**

1. Cek apakah data ter-insert
2. Cek field `is_active = 1`
3. Verify dengan query manual

---

## 📝 Customization

### **Tambah Kriteria Baru**

```sql
INSERT INTO dynamic_criteria (code, name, weight, type, description)
VALUES ('C6', 'Kriteria Baru', 0.1, 'benefit', 'Deskripsi');

-- Update bobot lain agar total tetap 100%
UPDATE dynamic_criteria SET weight = weight * 0.9 WHERE code != 'C6';
```

### **Tambah Karyawan Baru**

```sql
INSERT INTO dynamic_alternatives (name, position, employee_id)
VALUES ('Karyawan Baru', 'Staff', 'EMP004');
```

### **Input Evaluasi**

```sql
INSERT INTO dynamic_evaluations (alternative_id, criteria_id, score, evaluator)
VALUES (4, 1, 12.0, 'admin');
```

---

## 📞 Support

**Issues:** Database setup, struktur, data  
**Contact:** Muhammad Bayu Prasetyo Wibowo  
**Files:** Semua file database ada di folder ini  
**Backup:** Selalu backup sebelum modifikasi

---

## ✅ Checklist Setup

- [ ] Download semua file di folder database/
- [ ] Jalankan SPK_TOPSIS_DATABASE_COMPLETE.sql
- [ ] Verify data dengan view_database.py
- [ ] Test login admin/admin123
- [ ] Cek matriks evaluasi lengkap
- [ ] Backup database hasil setup
- [ ] Siap untuk aplikasi TOPSIS!

---

**© 2024 SPK TOPSIS Enhanced v2.0 - Database Complete Package**
