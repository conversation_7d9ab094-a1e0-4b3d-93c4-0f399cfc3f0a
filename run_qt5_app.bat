@echo off
title <PERSON><PERSON> Karyawan TOPSIS Enhanced v2.0 - Qt5 Version
color 0B

echo ============================================================
echo 🏆 <PERSON><PERSON>wan TOPSIS Enhanced v2.0 - Qt5 Version
echo ============================================================
echo Author: <PERSON>tyo Wibowo
echo NIM: 211011450583
echo Kelas: 06TPLP003
echo ============================================================
echo.

echo 🔍 Checking Python installation...
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.8+ first.
    echo 📥 Download from: https://python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python found

echo.
echo 📦 Checking PyQt5 installation...
py -c "import PyQt5" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ PyQt5 not found! Installing...
    echo 🔄 Installing PyQt5 and dependencies...
    py -m pip install PyQt5 PyQt5-tools
    if %errorlevel% neq 0 (
        echo ❌ Failed to install PyQt5!
        echo 💡 Try manual installation:
        echo    pip install PyQt5 PyQt5-tools
        echo.
        echo 🔄 Fallback: Starting Tkinter version...
        py main_enhanced.py
        pause
        exit /b 1
    )
    echo ✅ PyQt5 installed successfully
) else (
    echo ✅ PyQt5 ready
)

echo.
echo 📦 Installing other dependencies...
py -m pip install -r requirements.txt >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Some dependencies may be missing, but continuing...
) else (
    echo ✅ Dependencies ready
)

echo.
echo 🎨 Starting Qt5 Application...
echo 💡 Login Credentials:
echo    👑 Admin: admin / admin123 (Full Access)
echo    ⚙️ Operator: operator / operator123 (Data Input)
echo.
echo 🚀 Launching modern Qt5 interface...
echo.

py main_qt5.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Qt5 application failed!
    echo 🔄 Starting Tkinter fallback...
    echo.
    py main_enhanced.py
    if %errorlevel% neq 0 (
        echo ❌ Both Qt5 and Tkinter versions failed!
        pause
    )
) else (
    echo.
    echo 👋 Qt5 application closed successfully!
)

echo.
echo Thank you for using SPK TOPSIS Enhanced v2.0!
pause
